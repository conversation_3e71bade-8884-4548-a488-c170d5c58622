import React, { FormEvent, useState, useRef, useEffect, forwardRef, useImperativeHandle } from "react";
import { usePathname, useRouter } from "next/navigation";
import { v4 as uuidv4 } from "uuid";
import {
  PiArrowUp,
  PiMicrophone,
  PiPaperclip,
  PiLightbulb,
  PiSparkle,
  PiGlobe,
  PiStop,
  PiWaveform,
  PiPencilSimple,
  PiCheck,
  PiSpinner,
  PiFile,
  PiFileText,
  PiMusicNote,
  PiYoutubeLogo,
  PiLink,
  PiX
} from "react-icons/pi";
import { useChatHandler } from "@/stores/chatList";
import SpeechRecognition, { useSpeechRecognition } from 'react-speech-recognition';
// Import components that are actually used
import ChatInputUpload from "./ChatInputUpload";
// import UploadedContentIndicator from "./UploadedContentIndicator";
// Import services that are actually used
import { ApiService } from "./services/ApiService";
import { TranslationService } from "./services/TranslationService";
import { ValidationService } from "./services/ValidationService";
import { CacheService } from "./services/CacheService";
// Import debug component for connection testing
import ConnectionTest from "../debug/ConnectionTest";



interface ChatBoxProps {
  onLanguageChange?: (language: string) => void;
}

export interface ChatBoxRef {
  setInputFromQuestion: (question: string) => void;
}

const ChatBox = forwardRef<ChatBoxRef, ChatBoxProps>(({ onLanguageChange }, ref) => {
  const [inputText, setInputText] = useState("");
  const [showSuggestions, setShowSuggestions] = useState(false);
  // Add language dropup state for responsive design
  const [showLanguageMenu, setShowLanguageMenu] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState("English");
  const [isListening, setIsListening] = useState(false);
  const [speaking, setSpeaking] = useState(false);
  const [wordCount, setWordCount] = useState(0);
  // Track recent words for word count
  const [recentWords, setRecentWords] = useState<string[]>([]);
  // Add state for transcript editing
  const [isEditingTranscript, setIsEditingTranscript] = useState(false);
  const [editedTranscript, setEditedTranscript] = useState("");
  // Add state for language validation
  const [languageError, setLanguageError] = useState<string | null>(null);
  // Add state to track if language buttons should be disabled
  const [languageButtonsDisabled, setLanguageButtonsDisabled] = useState(false);
  // Add state for user email from localStorage
  const [userEmail, setUserEmail] = useState<string | null>(null);
  // Add state for FAISS indexes - fetch from PINE collection
  const [pineconeIndexes, setPineconeIndexes] = useState<string[]>([]);
  const [selectedIndex, setSelectedIndex] = useState<string>('');
  const [apiEnvironment, setApiEnvironment] = useState<'development' | 'production'>('production');
  // Add state for loading indexes
  const [indexesLoading, setIndexesLoading] = useState<boolean>(true);
  // Add state for index selector dropdown visibility
  const [showIndexSelector, setShowIndexSelector] = useState<boolean>(false);
  // Add state for index selection confirmation
  const [showIndexConfirmation, setShowIndexConfirmation] = useState<boolean>(false);
  // Add state to track if user has made first request - check localStorage for persistence
  const [hasUserMadeFirstRequest, setHasUserMadeFirstRequest] = useState<boolean>(() => {
    if (typeof window !== 'undefined') {
      const hasFirstRequest = localStorage.getItem('hasUserMadeFirstRequest') === 'true';
      console.log('🔍 ChatBox: hasUserMadeFirstRequest initialized to:', hasFirstRequest);
      return hasFirstRequest;
    }
    return false;
  });
  // Add state to track upload component state
  const [uploadIsActive, setUploadIsActive] = useState<boolean>(false);
  const [uploadDropdownVisible, setUploadDropdownVisible] = useState<boolean>(false);
  // Add state for connection test dialog
  const [showConnectionTest, setShowConnectionTest] = useState<boolean>(false);

  // Handle upload state changes
  const handleUploadStateChange = (isActive: boolean, showDropdown: boolean) => {
    setUploadIsActive(isActive);
    setUploadDropdownVisible(showDropdown);
  };
  // Add state for uploaded content display
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [uploadedURLs, setUploadedURLs] = useState<Array<{url: string, type: 'youtube' | 'article'}>>([]);
  const [showUploadedContent, setShowUploadedContent] = useState<boolean>(false);
  // Add state for response data sources
  const [responseHasUploadedContent, setResponseHasUploadedContent] = useState<boolean>(false);
  const [responseUploadSources, setResponseUploadSources] = useState<string[]>([]);
  // Reference for the index selector dropdown
  const indexSelectorRef = useRef<HTMLDivElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);
  // Add language menu ref for responsive design
  const languageMenuRef = useRef<HTMLDivElement>(null);
  const transcriptRef = useRef<HTMLDivElement>(null);
  const editableTranscriptRef = useRef<HTMLTextAreaElement>(null);
  const router = useRouter();
  const path = usePathname();
  const { userQuery, handleSubmit, addMessage, setUserQuery, isLoading, setIsLoading, chatList } = useChatHandler();

  // Check if current chat has existing messages
  useEffect(() => {
    const chatIdUrl = path && path.includes("/chat/") ? path.split("/chat/")[1] : "";
    if (chatIdUrl) {
      const currentChat = chatList.find(chat => chat.id === chatIdUrl);
      if (currentChat && currentChat.messages && currentChat.messages.length > 0) {
        console.log("🔍 ChatBox: Found existing chat with messages, setting hasUserMadeFirstRequest to true");
        setHasUserMadeFirstRequest(true);
        if (typeof window !== 'undefined') {
          localStorage.setItem('hasUserMadeFirstRequest', 'true');
        }
      }
    }
  }, [chatList, path]);

  // Speech detection timer
  const speakingTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Expose methods to parent component
  useImperativeHandle(ref, () => ({
    setInputFromQuestion: (question: string) => {
      console.log("🎯 ChatBox: setInputFromQuestion called with:", question);
      
      // Update both state variables
      setInputText(question);
      setUserQuery(question);

      // Use requestAnimationFrame to ensure DOM updates are complete
      requestAnimationFrame(() => {
        const inputElement = document.querySelector('input.w-full.outline-none.p-4.pr-12.bg-transparent') as HTMLInputElement;
        if (inputElement) {
          // Set the value directly on the input element
          inputElement.value = question;
          // Create and dispatch input event
          const inputEvent = new Event('input', { bubbles: true });
          inputElement.dispatchEvent(inputEvent);
          // Create and dispatch change event
          const changeEvent = new Event('change', { bubbles: true });
          inputElement.dispatchEvent(changeEvent);
          // Focus the input
          inputElement.focus();
          // Set cursor to end of text
          inputElement.setSelectionRange(question.length, question.length);
          console.log("✅ ChatBox: Input element updated and focused");
        } else {
          console.warn("❌ ChatBox: Could not find input element!");
        }
      });
    }
  }));



  // Available languages for voice input with their language codes - English, Tamil, Telugu, and Kannada
  const languages = [
    { name: "English", code: "en-US", color: "blue" },
    { name: "Tamil", code: "ta-IN", color: "purple" },
    { name: "Telugu", code: "te-IN", color: "green" },
    { name: "Kannada", code: "kn-IN", color: "orange" }
  ];

  // Get the language code for the selected language
  const getLanguageCode = () => {
    const language = languages.find(lang => lang.name === selectedLanguage);
    return language ? language.code : "en-US"; // Default to English if not found
  };

  // Initialize with default values for server-side rendering
  const [transcript, setTranscript] = useState("");
  const [listening, setListening] = useState(false);
  const [browserSupportsSpeechRecognition, setBrowserSupportsSpeechRecognition] = useState(false);
  const [isMicrophoneAvailable, setIsMicrophoneAvailable] = useState(false);

  // Speech recognition setup - only run on client side
  const {
    transcript: clientTranscript,
    listening: clientListening,
    resetTranscript,
    browserSupportsSpeechRecognition: clientBrowserSupport,
    isMicrophoneAvailable: clientMicrophoneAvailable,
    // We don't need these transcripts as we're using the combined transcript
    // interimTranscript,
    // finalTranscript
  } = typeof window !== 'undefined' ? useSpeechRecognition({
    clearTranscriptOnListen: false,
    transcribing: true,
    commands: [
      {
        command: '*',
        callback: (command) => {
          console.log('Voice command detected:', command);
        }
      }
    ]
  }) : {
    transcript: "",
    listening: false,
    resetTranscript: () => {},
    browserSupportsSpeechRecognition: false,
    isMicrophoneAvailable: false
  };

  // Handle click outside to close dropdowns
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      // Close suggestions dropdown if clicked outside
      if (suggestionsRef.current && !suggestionsRef.current.contains(event.target as Node)) {
        setShowSuggestions(false);
      }

      // Close language menu if clicked outside
      if (languageMenuRef.current && !languageMenuRef.current.contains(event.target as Node)) {
        setShowLanguageMenu(false);
      }

      // Close index selector if clicked outside
      if (indexSelectorRef.current && !indexSelectorRef.current.contains(event.target as Node)) {
        setShowIndexSelector(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Auto-scroll transcript to bottom when it gets too long
  useEffect(() => {
    if (transcriptRef.current && transcript) {
      transcriptRef.current.scrollTop = transcriptRef.current.scrollHeight;
    }
  }, [transcript]);

  // Update word count when transcript changes
  useEffect(() => {
    if (transcript) {
      const words = transcript.trim().split(/\s+/).filter(word => word !== "");
      setWordCount(words.length);

      // Track most recent words for animation
      if (words.length > 0) {
        const lastWord = words[words.length - 1];
        if (lastWord && lastWord.length > 0) {
          setRecentWords((prev: string[]) => {
            const newWords = [...prev, lastWord];
            return newWords.slice(-5); // Keep only the last 5 words
          });
        }
      }

      // Set speaking state when new words are detected
      if (isListening) {
        setSpeaking(true);

        // Clear previous timer if it exists
        if (speakingTimerRef.current) {
          clearTimeout(speakingTimerRef.current);
        }

        // Set a timer to detect when speaking has paused
        speakingTimerRef.current = setTimeout(() => {
          setSpeaking(false);
        }, 1500); // 1.5 seconds of silence is considered a pause
      }
    }
  }, [transcript, isListening]);

  // Update input text when transcript changes
  useEffect(() => {
    // Always update with the latest transcript when listening
    if (listening || isListening) {
      setInputText(transcript);
      setUserQuery(transcript);

      // Also update the edited transcript to match the current transcript
      // This ensures that when we switch to edit mode, we have the latest transcript
      setEditedTranscript(transcript);

      console.log("Speech recognized:", transcript);

      if (transcript && !isListening) {
        console.log("Got transcript but isListening was false. Fixing state...");
        setIsListening(true);
      }
    }
  }, [transcript, listening, isListening, setUserQuery]);

  // Separate effect to handle listening state changes
  useEffect(() => {
    if (isListening) {
      if (inputText !== "") {
        setInputText("");
        setUserQuery("");
      }
    } else {
      console.log("Stopped listening, transcript:", transcript);

      if (transcript && transcript.trim() !== "") {
        setInputText(transcript);
        setUserQuery(transcript);
      } else if (typeof window !== 'undefined') {
        resetTranscript();
      }

      // Reset speaking state and clear any timers
      setSpeaking(false);
      if (speakingTimerRef.current) {
        clearTimeout(speakingTimerRef.current);
        speakingTimerRef.current = null;
      }

      // No longer need to close language dropdown
      // setShowLanguageDropdown(false);
    }

    console.log("Listening state changed:", isListening);
  }, [isListening, transcript, inputText]);

  // Update listening state when speech recognition status changes
  useEffect(() => {
    if (typeof window === 'undefined') return; // Skip on server-side

    console.log("Speech recognition listening state changed:", listening);

    if (isListening !== listening) {
      setIsListening(listening);
    }

    if (!listening && isListening) {
      console.warn("Speech recognition stopped unexpectedly. Checking if we should restart...");

      // Only attempt to restart if we're still supposed to be listening
      // This prevents infinite restart loops
      const restartTimeout = setTimeout(() => {
        if (isListening && typeof window !== 'undefined') {
          console.log("Attempting to restart speech recognition...");

          // Try to restart speech recognition
          const languageCode = getLanguageCode();
          SpeechRecognition.startListening({
            continuous: true,
            language: languageCode,
            interimResults: true
          });
        }
      }, 1000);

      return () => clearTimeout(restartTimeout);
    }
  }, [listening, isListening]);

  // Effect to handle language changes
  useEffect(() => {
    if (typeof window === 'undefined') return; // Skip on server-side

    console.log(`Language changed to: ${selectedLanguage}`);
    // This effect can be used to update any UI elements when language changes
    // The placeholder text and suggestions are already handled by the render logic

    // If we're listening, we need to restart with the new language
    if (isListening) {
      const restartWithNewLanguage = async () => {
        try {
          await SpeechRecognition.stopListening();
          await new Promise(resolve => setTimeout(resolve, 300));

          const languageCode = getLanguageCode();
          await SpeechRecognition.startListening({
            continuous: true,
            language: languageCode,
            interimResults: true
          });

          console.log(`Restarted speech recognition with language: ${selectedLanguage} (${languageCode})`);
        } catch (error) {
          console.error("Error restarting speech recognition with new language:", error);
        }
      };

      restartWithNewLanguage();
    }
  }, [selectedLanguage]);

  // Effect to hide index confirmation after 5 seconds
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (showIndexConfirmation) {
      timer = setTimeout(() => {
        setShowIndexConfirmation(false);
      }, 5000);
    }
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [showIndexConfirmation]);

  // Sample recommended suggestions
  const englishSuggestions = [
    "How does the new tax regime (post-2023) compare with the old tax regime in terms of benefits for salaried individuals?",
    "How has the rise of UPI (Unified Payments Interface) transformed retail banking and cashless transactions in India?",
    "What factors should a retail investor in India consider before investing in IPOs?",
    "How effective has the Pradhan Mantri Jan Dhan Yojana (PMJDY) been in achieving financial inclusion in rural India?",
    "How are fintech startups like Zerodha and Groww changing the investment landscape for young Indians?"
  ];

  // Tamil financial suggestions
  const tamilSuggestions = [
    "இந்தியாவில் டிஜிட்டல் வாலட் மற்றும் மொபைல் பேமெண்ட் பயன்பாடுகள் நிதி சேவைகளை அணுகுவதை எவ்வாறு மாற்றியுள்ளன?",
    "நீண்ட கால ஓய்வூதிய திட்டங்களில் முதலீடு செய்வதற்கான சிறந்த வழிகள் என்ன மற்றும் அவற்றின் வரி நன்மைகள் என்ன?",
    "சிறு மற்றும் நடுத்தர தொழில்களுக்கு (SMEs) இந்தியாவில் கிடைக்கும் நிதி ஆதரவு திட்டங்கள் என்னென்ன?",
    "பங்குச் சந்தை முதலீட்டிற்கும் தங்கம் மற்றும் நிலம் போன்ற பாரம்பரிய முதலீடுகளுக்கும் இடையே உள்ள முக்கிய வேறுபாடுகள் என்ன?",
    "இந்தியாவில் கிரிப்டோகரன்சி மற்றும் டிஜிட்டல் சொத்துக்களுக்கான தற்போதைய ஒழுங்குமுறை நிலைப்பாடு என்ன?"
  ];

  // Telugu financial suggestions
  const teluguSuggestions = [
    "భారతదేశంలో మ్యూచువల్ ఫండ్స్ లో పెట్టుబడి పెట్టడానికి ఉత్తమ వ్యూహాలు ఏమిటి మరియు వాటి ప్రయోజనాలు ఏమిటి?",
    "వ్యక్తిగత ఆర్థిక ప్రణాళిక కోసం డిజిటల్ టూల్స్ మరియు యాప్‌లు ఎలా ఉపయోగపడతాయి?",
    "భారతదేశంలో స్టార్టప్‌లకు వెంచర్ క్యాపిటల్ మరియు ఏంజెల్ ఇన్వెస్టర్‌ల నుండి నిధులు సేకరించడం ఎలా?",
    "రియల్ ఎస్టేట్ పెట్టుబడులకు REIT (రియల్ ఎస్టేట్ ఇన్వెస్ట్‌మెంట్ ట్రస్ట్‌లు) ఎలా ప్రత్యామ్నాయంగా పనిచేస్తాయి?",
    "భారతదేశంలో ఫినాన్షియల్ లిటరసీని మెరుగుపరచడానికి ప్రభుత్వం మరియు ప్రైవేట్ రంగం తీసుకుంటున్న చర్యలు ఏమిటి?"
  ];

  // Kannada financial suggestions
  const kannadaSuggestions = [
    "ಭಾರತದಲ್ಲಿ ಸಣ್ಣ ಉಳಿತಾಯ ಯೋಜನೆಗಳು ಮತ್ತು ಸರ್ಕಾರಿ ಬಾಂಡ್‌ಗಳಲ್ಲಿ ಹೂಡಿಕೆ ಮಾಡುವುದರ ಪ್ರಯೋಜನಗಳು ಯಾವುವು?",
    "ಹಣದುಬ್ಬರದ ಸಮಯದಲ್ಲಿ ಹಣಕಾಸು ಸ್ಥಿರತೆಯನ್ನು ಕಾಪಾಡಿಕೊಳ್ಳಲು ಉತ್ತಮ ಆರ್ಥಿಕ ತಂತ್ರಗಳು ಯಾವುವು?",
    "ಭಾರತದಲ್ಲಿ ಸ್ವಯಂ ಉದ್ಯೋಗಿಗಳು ಮತ್ತು ಫ್ರೀಲ್ಯಾನ್ಸರ್‌ಗಳಿಗೆ ಲಭ್ಯವಿರುವ ತೆರಿಗೆ ಯೋಜನೆ ಮತ್ತು ಹಣಕಾಸು ಸಾಧನಗಳು ಯಾವುವು?",
    "ಭಾರತದಲ್ಲಿ ಹೊಸ ಪೆನ್ಷನ್ ಯೋಜನೆ (NPS) ಮತ್ತು ಅಟಲ್ ಪೆನ್ಷನ್ ಯೋಜನೆ (APY) ನಡುವಿನ ವ್ಯತ್ಯಾಸಗಳು ಯಾವುವು?",
    "ಭಾರತದಲ್ಲಿ ಮಹಿಳಾ ಉದ್ಯಮಿಗಳಿಗೆ ಲಭ್ಯವಿರುವ ವಿಶೇಷ ಹಣಕಾಸು ಯೋಜನೆಗಳು ಮತ್ತು ಸಾಲ ಕಾರ್ಯಕ್ರಮಗಳು ಯಾವುವು?"
  ];

  // Get suggestions based on selected language
  const getSuggestionsByLanguage = () => {
    switch(selectedLanguage) {
      case "Tamil":
        return tamilSuggestions;
      case "Telugu":
        return teluguSuggestions;
      case "Kannada":
        return kannadaSuggestions;
      default:
        return englishSuggestions;
    }
  };

  const recommendedSuggestions = getSuggestionsByLanguage();

  // Extract chat ID from URL
  const chatIdUrl = path && path.includes("/chat/") ? path.split("/chat/")[1] : "";

  // Handle selecting a suggestion
  const handleSelectSuggestion = (suggestion: string) => {
    setInputText(suggestion);
    setUserQuery(suggestion);
    setShowSuggestions(false);
  };


  // Function to get language-specific text for uploaded content display
  const getUploadDisplayText = () => {
    switch(selectedLanguage) {
      case "Tamil":
        return {
          uploadedFiles: 'பதிவேற்றப்பட்ட கோப்புகள்',
          uploadedUrls: 'பதிவேற்றப்பட்ட இணைப்புகள்',
          removeFile: 'கோப்பை அகற்று',
          removeUrl: 'இணைப்பை அகற்று',
          pdfDocument: 'PDF ஆவணம்',
          mp3Audio: 'MP3 ஆடியோ',
          youtubeVideo: 'YouTube வீடியோ',
          articleLink: 'கட்டுரை இணைப்பு'
        };
      case "Telugu":
        return {
          uploadedFiles: 'అప్‌లోడ్ చేసిన ఫైల్‌లు',
          uploadedUrls: 'అప్‌లోడ్ చేసిన లింక్‌లు',
          removeFile: 'ఫైల్‌ను తొలగించండి',
          removeUrl: 'లింక్‌ను తొలగించండి',
          pdfDocument: 'PDF డాక్యుమెంట్',
          mp3Audio: 'MP3 ఆడియో',
          youtubeVideo: 'YouTube వీడియో',
          articleLink: 'వ్యాసం లింక్'
        };
      case "Kannada":
        return {
          uploadedFiles: 'ಅಪ್‌ಲೋಡ್ ಮಾಡಿದ ಫೈಲ್‌ಗಳು',
          uploadedUrls: 'ಅಪ್‌ಲೋಡ್ ಮಾಡಿದ ಲಿಂಕ್‌ಗಳು',
          removeFile: 'ಫೈಲ್ ತೆಗೆದುಹಾಕಿ',
          removeUrl: 'ಲಿಂಕ್ ತೆಗೆದುಹಾಕಿ',
          pdfDocument: 'PDF ಡಾಕ್ಯುಮೆಂಟ್',
          mp3Audio: 'MP3 ಆಡಿಯೋ',
          youtubeVideo: 'YouTube ವೀಡಿಯೊ',
          articleLink: 'ಲೇಖನ ಲಿಂಕ್'
        };
      default:
        return {
          uploadedFiles: 'Uploaded Files',
          uploadedUrls: 'Uploaded URLs',
          removeFile: 'Remove file',
          removeUrl: 'Remove URL',
          pdfDocument: 'PDF Document',
          mp3Audio: 'MP3 Audio',
          youtubeVideo: 'YouTube Video',
          articleLink: 'Article Link'
        };
    }
  };

  // Function to get file icon based on file type
  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();

    // Document files
    if (['pdf', 'doc', 'docx', 'txt', 'rtf', 'odt'].includes(extension || '')) {
      return <PiFileText className="w-5 h-5" />;
    }

    // Audio files
    if (['mp3', 'wav', 'm4a', 'aac', 'flac', 'ogg', 'wma'].includes(extension || '')) {
      return <PiMusicNote className="w-5 h-5" />;
    }

    // Default file icon
    return <PiFile className="w-5 h-5" />;
  };

  // Function to get URL icon based on type
  const getUrlIcon = (type: 'youtube' | 'article') => {
    if (type === 'youtube') {
      return <PiYoutubeLogo className="w-5 h-5" />;
    }
    return <PiLink className="w-5 h-5" />;
  };

  // Function to remove uploaded file
  const removeUploadedFile = (index: number) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index));
    if (uploadedFiles.length === 1 && uploadedURLs.length === 0) {
      setShowUploadedContent(false);
    }
  };

  // Function to remove uploaded URL
  const removeUploadedURL = (index: number) => {
    setUploadedURLs(prev => prev.filter((_, i) => i !== index));
    if (uploadedURLs.length === 1 && uploadedFiles.length === 0) {
      setShowUploadedContent(false);
    }
  };

  // Handle selecting a language for voice input and UI
  const handleSelectLanguage = async (e: React.MouseEvent, language: string) => {
    // Prevent the event from bubbling up and triggering form submission
    e.preventDefault();
    e.stopPropagation();

    setSelectedLanguage(language);
    // Close language menu when a language is selected
    setShowLanguageMenu(false);

    // Clear any language error when the user changes the language
    if (languageError) {
      setLanguageError(null);
    }

    // Update placeholder and UI based on language
    console.log(`Language set to: ${language}`);

    // Notify parent component about language change if callback is provided
    if (onLanguageChange) {
      onLanguageChange(language);
    }

    // If suggestions are showing, close and reopen to refresh the content
    if (showSuggestions) {
      setShowSuggestions(false);
      setTimeout(() => setShowSuggestions(true), 100);
    }

    // If currently listening, restart with new language
    if (isListening && typeof window !== 'undefined') {
      try {
        await SpeechRecognition.stopListening();
        console.log("Speech recognition stopped for language change");

        await new Promise(resolve => setTimeout(resolve, 300));
        await startListening();
      } catch (error) {
        console.error("Error changing speech recognition language:", error);
      }
    }
  };

  // Toggle voice recognition
  const toggleListening = async () => {
    // Skip on server-side
    if (typeof window === 'undefined') {
      console.warn("Attempted to toggle listening on server-side");
      return;
    }

    if (isListening) {
      try {
        await SpeechRecognition.stopListening();
        setIsListening(false); // Explicitly set to false to ensure state is updated

        // Don't reset transcript when stopping, so it can be edited
        // resetTranscript();

        console.log("Speech recognition stopped successfully");
      } catch (error) {
        console.error("Error stopping speech recognition:", error);
      }
    } else {
      await startListening();
    }
  };

  // Toggle between edit and view modes for the transcript
  const toggleTranscriptEditMode = () => {
    if (isEditingTranscript) {
      // Save the edited transcript
      if (editedTranscript.trim() !== "") {
        setInputText(editedTranscript);
        setUserQuery(editedTranscript);
      }
      setIsEditingTranscript(false);
    } else {
      // Enter edit mode
      setIsEditingTranscript(true);

      // Focus the editable textarea after a short delay to ensure it's rendered
      setTimeout(() => {
        if (editableTranscriptRef.current) {
          editableTranscriptRef.current.focus();
        }
      }, 50);
    }
  };

  // Handle changes to the editable transcript
  const handleTranscriptChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setEditedTranscript(e.target.value);

    // Update word count
    const words = e.target.value.trim().split(/\s+/).filter(word => word !== "");
    setWordCount(words.length);

    // Clear any language error when the user edits the transcript
    if (languageError) {
      setLanguageError(null);
    }
  };

  // Start listening with the selected language
  const startListening = async () => {
    // Skip on server-side
    if (typeof window === 'undefined') {
      console.warn("Attempted to start listening on server-side");
      return;
    }

    // Only reset transcript if we're starting fresh
    // If we have edited transcript, keep it
    if (!editedTranscript || editedTranscript.trim() === "") {
      resetTranscript();
      setInputText("");
      setUserQuery("");
      setWordCount(0);
      setRecentWords([]);
    }

    const languageCode = getLanguageCode();
    console.log(`Starting speech recognition in ${selectedLanguage} (${languageCode})`);

    try {
      setIsListening(true);

      await new Promise(resolve => setTimeout(resolve, 100));

      if (!browserSupportsSpeechRecognition) {
        console.error("Browser doesn't support speech recognition");
        alert("Your browser doesn't support speech recognition. Please try using Chrome.");
        setIsListening(false);
        return;
      }

      if (!isMicrophoneAvailable) {
        console.error("Microphone is not available");
        alert("Microphone is not available. Please check your microphone permissions.");
        setIsListening(false);
        return;
      }

      await SpeechRecognition.startListening({
        continuous: true,
        language: languageCode,
        interimResults: true
      });

      console.log("Speech recognition started successfully");

      setTimeout(() => {
        if (!listening && typeof window !== 'undefined') {
          console.warn("Speech recognition may not have started properly. Trying again...");
          SpeechRecognition.startListening({
            continuous: true,
            language: languageCode,
            interimResults: true
          });
        }
      }, 500);

    } catch (error) {
      console.error("Error starting speech recognition:", error);
      setIsListening(false);
      alert("There was an error starting speech recognition. Please try again.");
    }
  };










  const handleSendMessage = async (e: FormEvent) => {
    e.preventDefault();

    // If we're in edit mode, use the edited transcript
    // Otherwise, use the transcript if listening, or the input text
    const textToSend = isEditingTranscript ? editedTranscript : (isListening ? transcript : inputText);

    if (!textToSend || !textToSend.trim()) {
      return;
    }

    // Validate that the input text matches the selected language
    if (!ValidationService.validateLanguageMatch(textToSend, selectedLanguage)) {
      setLanguageError("Please select the proper language or type in the currently selected language.");
      return;
    }

    // Clear any previous language errors
    setLanguageError(null);

    // If we're in edit mode, exit edit mode
    if (isEditingTranscript) {
      setIsEditingTranscript(false);
    }

    if (isListening && typeof window !== 'undefined') {
      SpeechRecognition.stopListening();
      setIsListening(false); // Explicitly set to false
      setSpeaking(false); // Reset speaking state
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // Close any open dropdowns/popups
    setShowLanguageMenu(false);
    setShowSuggestions(false);

    // Disable language buttons during query processing
    setLanguageButtonsDisabled(true);

    // Mark that user has made their first request and persist it
    setHasUserMadeFirstRequest(true);
    if (typeof window !== 'undefined') {
      localStorage.setItem('hasUserMadeFirstRequest', 'true');
    }

    setIsLoading(true);

    const currentChatId = chatIdUrl || uuidv4();

    if (!chatIdUrl) {
      router.push(`/chat/${currentChatId}`);
    }

    const queryText = textToSend.trim();

    // Determine the language of the query
    const isTamil = ValidationService.isTamilText(queryText) || selectedLanguage === "Tamil";
    const isTelugu = ValidationService.isTeluguText(queryText) || selectedLanguage === "Telugu";
    const isKannada = ValidationService.isKannadaText(queryText) || selectedLanguage === "Kannada";

    // Log language detection for Tamil support
    if (isTamil) {
      console.log(`🌏 Tamil language detected - Selected: ${selectedLanguage}, Text detection: ${ValidationService.isTamilText(queryText)}`);
    }

    // Store the original query for display purposes (commented out as it's not currently used)
    // const originalQuery = queryText;
    let translatedQuery = queryText;
    let needsTranslation = false;

    // Use the financial_query endpoint for all languages
    // For Tamil, Telugu, and Kannada, we'll translate the query and response

    // Extract continuous capital English words that should not be translated
    const capitalWordsMatches = queryText.match(/\b[A-Z]{2,}\b/g) || [];
    const capitalWords = capitalWordsMatches.map((word: string) => ({
      word,
      placeholder: `__CAPITAL_WORD_${Math.random().toString(36).substring(2, 10)}__`
    }));

    // Create a version of the query with placeholders for capital words
    let queryWithPlaceholders = queryText;
    capitalWords.forEach((item: { word: string, placeholder: string }) => {
      queryWithPlaceholders = queryWithPlaceholders.replace(item.word, item.placeholder);
    });

    if (isTamil) {
      // For Tamil, we need to translate the query to English before sending
      needsTranslation = true;

      // Translate Tamil to English
      try {
        console.log(`Translating Tamil query to English (preserving capital words): "${queryWithPlaceholders}"`);
        translatedQuery = await TranslationService.translateText(queryWithPlaceholders, "ta", "en");

        // After translation, restore the capital words
        capitalWords.forEach(item => {
          translatedQuery = translatedQuery.replace(item.placeholder, item.word);
        });
      } catch (error) {
        console.error("Error translating Tamil query:", error);
      }
    } else if (isTelugu) {
      // For Telugu, we need to translate the query to English before sending
      needsTranslation = true;

      // Translate Telugu to English
      try {
        console.log(`Translating Telugu query to English (preserving capital words): "${queryWithPlaceholders}"`);
        translatedQuery = await TranslationService.translateText(queryWithPlaceholders, "te", "en");

        // After translation, restore the capital words
        capitalWords.forEach(item => {
          translatedQuery = translatedQuery.replace(item.placeholder, item.word);
        });
      } catch (error) {
        console.error("Error translating Telugu query:", error);
      }
    } else if (isKannada) {
      // For Kannada, we need to translate the query to English before sending
      needsTranslation = true;

      // Translate Kannada to English
      try {
        console.log(`Translating Kannada query to English (preserving capital words): "${queryWithPlaceholders}"`);
        translatedQuery = await TranslationService.translateText(queryWithPlaceholders, "kn", "en");

        // After translation, restore the capital words
        capitalWords.forEach(item => {
          translatedQuery = translatedQuery.replace(item.placeholder, item.word);
        });
      } catch (error) {
        console.error("Error translating Kannada query:", error);
      }
    }

    console.log(`Query detected as ${isTamil ? 'Tamil' : isTelugu ? 'Telugu' : isKannada ? 'Kannada' : 'English'}`);

    setInputText("");
    setUserQuery("");
    resetTranscript();
    setWordCount(0);
    setRecentWords([]);

    // Note: Uploaded content is intentionally NOT cleared here to allow users to send multiple messages
    // with the same uploaded files/URLs. Content will only be cleared when explicitly removed by user
    // or when starting a new chat session.

    const userMessageTimestamp = new Date().toISOString();

    // Convert uploaded files to the format expected by the Message interface
    const messageUploadedFiles = uploadedFiles.map(file => ({
      name: file.name,
      size: file.size,
      type: file.type,
      lastModified: file.lastModified
    }));

    addMessage({
      isUser: true,
      text: queryText,
      timestamp: userMessageTimestamp,
      uploadedFiles: messageUploadedFiles.length > 0 ? messageUploadedFiles : undefined,
      uploadedURLs: uploadedURLs.length > 0 ? uploadedURLs : undefined
    }, currentChatId);

    const loadingMessageTimestamp = new Date().toISOString();
    const loadingMessageId = `loading-${currentChatId}-${Date.now()}`;

    addMessage({
      isUser: false,
      text: "__LOADING__",
      timestamp: loadingMessageTimestamp,
      messageId: loadingMessageId
    }, currentChatId);

    try {
      // Use the translated query for Telugu, otherwise use the original query
      const queryToSend = needsTranslation ? translatedQuery : queryText;

      // Get FAISS configuration from localStorage if available
      const faissIndexName = typeof window !== 'undefined' ? localStorage.getItem('faiss_index_name') : null;
      const faissEmbedModel = typeof window !== 'undefined' ? localStorage.getItem('faiss_embed_model') : null;
      const faissClientEmail = typeof window !== 'undefined' ? localStorage.getItem('faiss_client_email') : null;

      // Prepare request body with all available data including language preference
      const requestBody: any = {
        query: queryToSend,
        language: selectedLanguage  // Add language preference for Tamil support
      };

      // Add client email if available
      if (userEmail) {
        requestBody.client_email = userEmail;
        requestBody.user_email = userEmail; // Also add for direct FAISS query validation
        console.log(`Including user email in request: ${userEmail}`);
      }

      // Determine which index to use for FAISS
      const indexToUse = selectedIndex || faissIndexName;
      console.log(`🎯 Selected index from UI: ${selectedIndex}`);
      console.log(`💾 Stored index from localStorage: ${faissIndexName}`);
      console.log(`📌 Final index to use: ${indexToUse}`);

      // Add context about uploaded content if any
      if (uploadedFiles.length > 0 || uploadedURLs.length > 0) {
        const uploadContext = [];

        if (uploadedFiles.length > 0) {
          uploadContext.push(`Recently uploaded files: ${uploadedFiles.map(f => f.name).join(', ')}`);
        }

        if (uploadedURLs.length > 0) {
          uploadContext.push(`Recently processed URLs: ${uploadedURLs.map(u => u.url).join(', ')}`);
        }

        requestBody.upload_context = uploadContext.join('. ');
        requestBody.has_recent_uploads = true;
        console.log(`📎 Including upload context: ${requestBody.upload_context}`);
      }

      // For FAISS, we don't need API keys, just the index name
      if (indexToUse) {
        requestBody.index_name = indexToUse;
        console.log(`✅ SENDING REQUEST WITH - Selected FAISS Index: "${indexToUse}"`);
      }
      // If no index selected, fall back to default index
      else {
        requestBody.index_name = 'default';
        console.log(`🔁 FALLING BACK TO DEFAULT INDEX - Using: "default"`);
        console.log(`💡 Note: Default index is accessible to all users`);
      }

      // Detect query language for better caching and translation
      const queryLanguage = TranslationService.detectLanguage(queryToSend);
      const targetLanguage = selectedLanguage === 'Tamil' ? 'ta' :
                           selectedLanguage === 'Telugu' ? 'te' :
                           selectedLanguage === 'Kannada' ? 'kn' : 'en';

      // 🚀 CACHE-FIRST APPROACH: Check cache before making API call with language support
      const cacheContext = `${requestBody.index_name || 'default'}|${userEmail || 'anonymous'}|${requestBody.upload_context || ''}`;
      const cachedResponse = await CacheService.getCachedResponseWithDelay(queryToSend, cacheContext, targetLanguage);

      let data;
      if (cachedResponse) {
        // Use cached response (delay already applied by getCachedResponseWithDelay)
        console.log(`⚡ Using cached response for query: "${queryToSend.substring(0, 50)}..." (Language: ${targetLanguage})`);

        data = {
          ai_response: cachedResponse.ai_response,
          related_questions: cachedResponse.related_questions,
          sentence_analysis: cachedResponse.sentence_analysis,
          pinecone_indexes: cachedResponse.pinecone_indexes,
          faiss_categories: cachedResponse.faiss_categories,
          has_uploaded_content: cachedResponse.has_uploaded_content,
          upload_sources: cachedResponse.upload_sources,
          translation_applied: cachedResponse.translation_applied,
          query_language: cachedResponse.query_language
        };

        console.log(`✅ Cached response ready with language support`);
      } else {
        // No cache hit - make API call
        console.log(`🌐 Making API call for query: "${queryToSend.substring(0, 50)}..." (Language: ${selectedLanguage})`);

        // Add translation parameters to request if needed
        if (targetLanguage !== 'en') {
          requestBody.target_language = targetLanguage;
          requestBody.enable_translation = true;
        }

        data = await ApiService.sendQuery(requestBody);

        // Apply client-side translation if backend translation wasn't applied
        if (!data.translation_applied && targetLanguage !== 'en') {
          console.log(`🌐 Applying client-side translation to ${targetLanguage}`);
          data = await TranslationService.translateResponse(data, targetLanguage);
        }

        // Cache the response for future use with language context
        CacheService.setCachedResponse(queryToSend, data, cacheContext, targetLanguage);
      }
      console.log("API Response received:", data);

      // Explicitly check for related_questions
      if (data.related_questions) {
        console.log("Found related_questions in API response:", data.related_questions);
      } else {
        console.warn("No related_questions found in API response");
      }

      // Check if the response includes a list of FAISS indexes/categories
      if ((data as any).faiss_categories && Array.isArray((data as any).faiss_categories) && (data as any).faiss_categories.length > 0) {
        console.log("Received FAISS categories from API:", (data as any).faiss_categories);
        // Update the dropdown options with the available categories
        setPineconeIndexes((data as any).faiss_categories.map((cat: any) => cat.index_name || cat));
      }

      let aiResponse = data.ai_response;
      console.log("Original AI Response:", aiResponse);

      // For Tamil, Telugu, or Kannada, translate the response from English
      if ((isTamil || isTelugu || isKannada) && aiResponse) {
        try {
          // Extract continuous capital English words that should not be translated
          const responseCapitalWordsMatches = aiResponse.match(/\b[A-Z]{2,}\b/g) || [];
          const responseCapitalWords = responseCapitalWordsMatches.map((word: string) => ({
            word,
            placeholder: `__CAPITAL_WORD_${Math.random().toString(36).substring(2, 10)}__`
          }));

          // Create a version of the response with placeholders for capital words
          let responseWithPlaceholders = aiResponse;
          responseCapitalWords.forEach((item: { word: string, placeholder: string }) => {
            responseWithPlaceholders = responseWithPlaceholders.replace(item.word, item.placeholder);
          });

          // Translate the response based on the detected language
          if (isTamil) {
            console.log(`Translating response from English to Tamil (preserving capital words): "${responseWithPlaceholders.substring(0, 50)}..."`);
            const translatedResponse = await TranslationService.translateText(responseWithPlaceholders, "en", "ta");
            aiResponse = translatedResponse;

            // After translation, restore the capital words
            responseCapitalWords.forEach((item: { word: string, placeholder: string }) => {
              aiResponse = aiResponse.replace(item.placeholder, item.word);
            });
          } else if (isTelugu) {
            console.log(`Translating response from English to Telugu (preserving capital words): "${responseWithPlaceholders.substring(0, 50)}..."`);
            const translatedResponse = await TranslationService.translateText(responseWithPlaceholders, "en", "te");
            aiResponse = translatedResponse;

            // After translation, restore the capital words
            responseCapitalWords.forEach((item: { word: string, placeholder: string }) => {
              aiResponse = aiResponse.replace(item.placeholder, item.word);
            });
          } else if (isKannada) {
            console.log(`Translating response from English to Kannada (preserving capital words): "${responseWithPlaceholders.substring(0, 50)}..."`);
            const translatedResponse = await TranslationService.translateText(responseWithPlaceholders, "en", "kn");
            aiResponse = translatedResponse;

            // After translation, restore the capital words
            responseCapitalWords.forEach((item: { word: string, placeholder: string }) => {
              aiResponse = aiResponse.replace(item.placeholder, item.word);
            });
          }
        } catch (error) {
          console.error(`Error translating response to ${isTamil ? 'Tamil' : isTelugu ? 'Telugu' : 'Kannada'}:`, error);
        }
      }

      console.log("AI Response to display:", aiResponse);

      // Update response data sources state
      const extendedData = data as any;
      if (extendedData.has_uploaded_content !== undefined) {
        setResponseHasUploadedContent(extendedData.has_uploaded_content);
      }
      if (extendedData.upload_sources && Array.isArray(extendedData.upload_sources)) {
        setResponseUploadSources(extendedData.upload_sources);
      }

      if (data.sentence_analysis && Array.isArray(data.sentence_analysis)) {
        console.log("Sentence analysis data:", data.sentence_analysis);

        // For Tamil, Telugu, or Kannada, we might want to translate the sentence analysis too
        if (isTamil || isTelugu || isKannada) {
          // In a real app, you would translate each sentence and summary
          // This is just a placeholder for the actual implementation
          console.log(`Would translate sentence analysis for ${isTamil ? 'Tamil' : isTelugu ? 'Telugu' : 'Kannada'} here`);

          // Example of how you might translate each item in a real implementation:
          // for (let i = 0; i < data.sentence_analysis.length; i++) {
          //   const item = data.sentence_analysis[i];
          //   if (item.summary) {
          //     item.summary = await translateText(item.summary, "en", isTamil ? "ta" : "te");
          //   }
          //   if (item.sentence) {
          //     item.sentence = await translateText(item.sentence, "en", isTamil ? "ta" : "te");
          //   }
          // }
        }

        data.sentence_analysis.forEach((item: { sentence: string; url: string; summary?: string }, index: number) => {
          const sentence = item.sentence;
          const url = item.url;
          const summary = item.summary || "";
          console.log(`Sentence ${index + 1}: ${sentence}`);
          console.log(`URL ${index + 1}: ${url}`);
          console.log(`Summary ${index + 1}: ${summary}`);
        });
      } else {
        console.log("No sentence_analysis data available.");
      }

      // Log and translate related questions if available
      if (data.related_questions && Array.isArray(data.related_questions)) {
        console.log("Related questions data:", data.related_questions);

        // For Tamil, Telugu, or Kannada, translate the related questions
        if (isTamil || isTelugu || isKannada) {
          console.log(`Translating related questions for ${isTamil ? 'Tamil' : isTelugu ? 'Telugu' : 'Kannada'}`);

          try {
            // Translate each related question
            const translatedQuestions = await Promise.all(
              data.related_questions.map(async (question: string) => {
                // Extract continuous capital English words that should not be translated
                const questionCapitalWordsMatches = question.match(/\b[A-Z]{2,}\b/g) || [];
                const questionCapitalWords = questionCapitalWordsMatches.map((word: string) => ({
                  word,
                  placeholder: `__CAPITAL_WORD_${Math.random().toString(36).substring(2, 10)}__`
                }));

                // Create a version of the question with placeholders for capital words
                let questionWithPlaceholders = question;
                questionCapitalWords.forEach((item: { word: string, placeholder: string }) => {
                  questionWithPlaceholders = questionWithPlaceholders.replace(item.word, item.placeholder);
                });

                let translatedQuestion = question;

                // Translate the question based on the detected language
                if (isTamil) {
                  translatedQuestion = await TranslationService.translateText(questionWithPlaceholders, "en", "ta");
                } else if (isTelugu) {
                  translatedQuestion = await TranslationService.translateText(questionWithPlaceholders, "en", "te");
                } else if (isKannada) {
                  translatedQuestion = await TranslationService.translateText(questionWithPlaceholders, "en", "kn");
                }

                // After translation, restore the capital words
                questionCapitalWords.forEach((item: { word: string, placeholder: string }) => {
                  translatedQuestion = translatedQuestion.replace(item.placeholder, item.word);
                });

                return translatedQuestion;
              })
            );

            // Update the data with translated questions
            data.related_questions = translatedQuestions;
            console.log(`✅ Successfully translated ${translatedQuestions.length} related questions to ${isTamil ? 'Tamil' : isTelugu ? 'Telugu' : 'Kannada'}`);
          } catch (error) {
            console.error(`❌ Error translating related questions to ${isTamil ? 'Tamil' : isTelugu ? 'Telugu' : 'Kannada'}:`, error);
            // Keep original questions if translation fails
          }
        }

        data.related_questions.forEach((question: string, index: number) => {
          console.log(`Related Question ${index + 1}: ${question}`);
        });
      } else {
        console.log("No related_questions data available.");
      }

      if (aiResponse === undefined || aiResponse === null) {
        console.warn("API returned null/undefined ai_response");
        handleSubmit(queryText, currentChatId, "Sorry, I couldn't process your request properly.");
        return;
      }

      // Create a properly structured response object with all fields
      const responseObject = {
        ai_response: aiResponse,
        sentence_analysis: data.sentence_analysis || [],
        related_questions: data.related_questions || []
      };

      // Explicitly log the related_questions in the response object
      console.log("Related questions in response object:", responseObject.related_questions);
      console.log("Sending structured response to handleSubmit:", responseObject);
      handleSubmit(queryText, currentChatId, responseObject);
    } catch (error) {
      console.error("Error fetching AI response:", error);

      // Provide a more specific error message if it's related to the FAISS index
      let errorMessage = "I'm sorry, I couldn't process your request at the moment. Please try again later.";

      if (error instanceof Error) {
        const errorText = error.message;

        // Check if the error is related to the FAISS index
        if (errorText.includes("index") || errorText.includes("Index") || errorText.includes("FAISS")) {
          errorMessage = errorText;
          console.log("Index-related error detected:", errorText);
        }
      }

      handleSubmit(queryText, currentChatId, errorMessage);
    } finally {
      setIsLoading(false);
      // Re-enable language buttons once the response is received or if there's an error
      setLanguageButtonsDisabled(false);
    }
  };

  // Initialize client-side values after mount to prevent hydration errors
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Update state with client-side values
      setTranscript(clientTranscript);
      setListening(clientListening);
      setBrowserSupportsSpeechRecognition(clientBrowserSupport);
      setIsMicrophoneAvailable(clientMicrophoneAvailable);

      // Clean up expired cache entries on component mount
      CacheService.cleanupExpiredCache();

      console.log("SpeechRecognition API check:", {
        webkitSpeechRecognition: 'webkitSpeechRecognition' in window,
        SpeechRecognition: 'SpeechRecognition' in window,
        mozSpeechRecognition: 'mozSpeechRecognition' in window,
        msSpeechRecognition: 'msSpeechRecognition' in window,
        clientBrowserSupport
      });

      // Fetch available indexes and initialize selected index
      const fetchIndexesAndInitialize = async () => {
        setIndexesLoading(true);
        console.log("Fetching available FAISS indexes from PINE collection...");

        try {
          // Fetch user's available indexes from PINE collection
          const availableIndexes = await ApiService.fetchUserIndexes();
          console.log("Available indexes for user:", availableIndexes);

          // Update the indexes state
          setPineconeIndexes(availableIndexes);

          // Initialize selectedIndex from localStorage, fallback to first available or 'default'
          const savedIndex = localStorage.getItem('selectedFaissIndex') || localStorage.getItem('faiss_index_name');
          let indexToSelect = 'default';

          if (savedIndex && availableIndexes.includes(savedIndex)) {
            indexToSelect = savedIndex;
          } else if (availableIndexes.includes('default')) {
            indexToSelect = 'default';
          } else if (availableIndexes.length > 0) {
            indexToSelect = availableIndexes[0];
          }

          setSelectedIndex(indexToSelect);
          localStorage.setItem('selectedFaissIndex', indexToSelect);
          localStorage.setItem('faiss_index_name', indexToSelect);

          console.log(`Selected index: ${indexToSelect}`);
        } catch (error) {
          console.error("Error fetching indexes:", error);
          // Fallback to default configuration
          setPineconeIndexes(['default']);
          setSelectedIndex('default');
          localStorage.setItem('selectedFaissIndex', 'default');
          localStorage.setItem('faiss_index_name', 'default');
        } finally {
          setIndexesLoading(false);
        }
      };

      fetchIndexesAndInitialize();
    }
  }, [clientBrowserSupport, clientMicrophoneAvailable, clientListening, clientTranscript]);

  // Get user email from localStorage and set up API environment
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Get the logged-in user's email from localStorage
      const email = localStorage.getItem('user_email');
      setUserEmail(email);

      // Check if we should use the development environment
      const useDevEnv = localStorage.getItem('use_dev_environment');
      if (useDevEnv === 'true') {
        console.log('Using development API endpoint');
        setApiEnvironment('development');
      } else {
        console.log('Using production API endpoint');
        setApiEnvironment('production');
      }
    }
  }, []);

  // Clear uploaded content when navigating to a different chat
  useEffect(() => {
    // Clear uploaded content when chat ID changes (new chat session)
    setUploadedFiles([]);
    setUploadedURLs([]);
    setShowUploadedContent(false);
    console.log('Cleared uploaded content for new chat session:', chatIdUrl);
  }, [chatIdUrl]);

  // Check for browser support and microphone availability
  useEffect(() => {
    if (!browserSupportsSpeechRecognition && typeof window !== 'undefined') {
      console.warn("Browser doesn't support speech recognition.");
    }

    if (!isMicrophoneAvailable && typeof window !== 'undefined') {
      console.warn("Microphone is not available.");
    }

    // Cleanup function to ensure microphone is stopped when component unmounts
    return () => {
      if (isListening && typeof window !== 'undefined') {
        console.log("Component unmounting, stopping speech recognition");
        SpeechRecognition.stopListening();
      }

      // Clear any timers
      if (speakingTimerRef.current) {
        clearTimeout(speakingTimerRef.current);
        speakingTimerRef.current = null;
      }
    };
  }, [browserSupportsSpeechRecognition, isMicrophoneAvailable, isListening]);

  // Function to log speech recognition state for debugging - uncomment the debug button below to use
  // const logSpeechRecognitionState = () => {
  //   console.group("Speech Recognition Debug Info");
  //   console.log("Browser supports speech recognition:", browserSupportsSpeechRecognition);
  //   console.log("Microphone available:", isMicrophoneAvailable);
  //   console.log("Current listening state (from hook):", listening);
  //   console.log("Current isListening state (component):", isListening);
  //   console.log("Current transcript:", transcript);
  //   console.log("Selected language:", selectedLanguage);
  //   console.log("Language code:", getLanguageCode());
  //   console.groupEnd();
  // };

  // Generate sound wave animation elements
  // Debug log for hasUserMadeFirstRequest state
  console.log('🔍 ChatBox render: hasUserMadeFirstRequest =', hasUserMadeFirstRequest);

  const renderSoundWave = () => {
    // Use different colors based on selected language
    let waveColor = "bg-red-500"; // Default color

    if (selectedLanguage === "Tamil") {
      waveColor = "bg-purple-500";
    } else if (selectedLanguage === "Telugu") {
      waveColor = "bg-green-500";
    } else if (selectedLanguage === "Kannada") {
      waveColor = "bg-orange-500";
    }

    return (
      <div className="flex items-center gap-[2px] h-5">
        {Array.from({ length: 5 }).map((_, i) => {
          const isActive = speaking || (i === 2); // Middle bar always active when not speaking
          const delay = i * 0.1;
          const duration = isActive ? (0.5 + (i * 0.1)) : 1; // Use deterministic duration
          const height = isActive ?
            (i % 2 === 0 ? 10 + (i * 2) : 6 + (i * 1.5)) : // Use deterministic height based on index
            (i === 2 ? 6 : 4);

          return (
            <div
              key={`wave-${i}`} // Use stable key
              className={`w-1 ${waveColor} rounded-full transition-all`}
              style={{
                height: `${height}px`,
                animationName: isActive ? 'soundWavePulse' : 'none',
                animationDuration: `${duration}s`,
                animationIterationCount: 'infinite',
                animationDirection: 'alternate',
                animationDelay: `${delay}s`,
                opacity: isActive ? 1 : 0.5
              }}
            ></div>
          );
        })}
      </div>
    );
  };

  return (
    <div className="w-full max-w-[1070px] mx-auto px-2 sm:px-4 md:px-6">
      <style jsx global>{`
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(-5px); }
          to { opacity: 1; transform: translateY(0); }
        }
        .animate-fadeIn {
          animation: fadeIn 0.3s ease-out forwards;
        }
      `}</style>



      {isListening && (
        <div className={`mb-4 p-3 rounded-lg flex items-center justify-between shadow-sm relative z-[10]
          ${selectedLanguage === "Tamil"
            ? "bg-gradient-to-r from-purple-50 to-purple-50/70 border border-purple-200"
            : selectedLanguage === "Telugu"
              ? "bg-gradient-to-r from-green-50 to-green-50/70 border border-green-200"
              : selectedLanguage === "Kannada"
                ? "bg-gradient-to-r from-orange-50 to-orange-50/70 border border-orange-200"
                : "bg-gradient-to-r from-red-50 to-red-50/70 border border-red-200"}`}>
          <div className="flex items-center gap-3">
            <div className="flex items-center bg-white p-1 rounded-full shadow-sm">
              {renderSoundWave()}
            </div>
            <div>
              <span className={`text-sm font-medium ${
                selectedLanguage === "Tamil"
                  ? "text-purple-700"
                  : selectedLanguage === "Telugu"
                    ? "text-green-700"
                    : selectedLanguage === "Kannada"
                      ? "text-orange-700"
                      : "text-red-700"
              }`}>
                {selectedLanguage === "Tamil"
                  ? "குரல் பதிவு செயலில் உள்ளது"
                  : selectedLanguage === "Telugu"
                    ? "వాయిస్ రికార్డింగ్ యాక్టివ్"
                    : selectedLanguage === "Kannada"
                      ? "ಧ್ವನಿ ರೆಕಾರ್ಡಿಂಗ್ ಸಕ್ರಿಯವಾಗಿದೆ"
                      : "Voice recording active in"} <strong>{selectedLanguage}</strong>
              </span>
              {wordCount > 0 && (
                <div className={`text-xs mt-0.5 ${
                  selectedLanguage === "Tamil"
                    ? "text-purple-500/80"
                    : selectedLanguage === "Telugu"
                      ? "text-green-500/80"
                      : selectedLanguage === "Kannada"
                        ? "text-orange-500/80"
                        : "text-red-500/80"
                }`}>
                  {selectedLanguage === "Tamil"
                    ? `இதுவரை ${wordCount} சொற்கள் பதிவு செய்யப்பட்டுள்ளன`
                    : selectedLanguage === "Telugu"
                      ? `ఇప్పటివరకు ${wordCount} పదాలు క్యాప్చర్ చేయబడ్డాయి`
                      : selectedLanguage === "Kannada"
                        ? `ಇಲ್ಲಿಯವರೆಗೆ ${wordCount} ಪದಗಳನ್ನು ಸೆರೆಹಿಡಿಯಲಾಗಿದೆ`
                        : `Captured ${wordCount} ${wordCount === 1 ? 'word' : 'words'} so far`}
                </div>
              )}
            </div>
          </div>
          <button
            onClick={() => {
              toggleListening();
              // Additional cleanup to ensure UI is reset
              setIsListening(false);
              resetTranscript();
              setSpeaking(false);
            }}
            className={`text-xs px-3 py-1.5 bg-white rounded-full transition-colors shadow-sm hover:shadow flex items-center gap-1.5
              ${selectedLanguage === "Tamil"
                ? "border border-purple-300 text-purple-600 hover:bg-purple-100"
                : selectedLanguage === "Telugu"
                  ? "border border-green-300 text-green-600 hover:bg-green-100"
                  : selectedLanguage === "Kannada"
                    ? "border border-orange-300 text-orange-600 hover:bg-orange-100"
                    : "border border-red-300 text-red-600 hover:bg-red-100"}`}
          >
            <PiStop className={
              selectedLanguage === "Tamil"
                ? "text-purple-600"
                : selectedLanguage === "Telugu"
                  ? "text-green-600"
                  : selectedLanguage === "Kannada"
                    ? "text-orange-600"
                    : "text-red-600"
            } />
            <span>{
              selectedLanguage === "Tamil"
                ? "பதிவை மீட்டமை"
                : selectedLanguage === "Telugu"
                  ? "రీసెట్ రికార్డింగ్"
                  : selectedLanguage === "Kannada"
                    ? "ರಿಸೆಟ್ ರೆಕಾರ್ಡಿಂಗ್"
                    : "Reset Recording"
            }</span>
          </button>
        </div>
      )}
      {/* Index Selector and Indicator - Only show after first request */}
      {hasUserMadeFirstRequest && (
        <div className="w-full flex justify-between items-center mb-2">
          {/* Index Selector Dropdown */}
          <div className="relative" ref={indexSelectorRef}>
            {/* <button
              onClick={() => setShowIndexSelector(!showIndexSelector)}
              disabled={indexesLoading}
              className="px-3 py-1.5 bg-gray-50 dark:bg-gray-800 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700 shadow-sm hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {indexesLoading ? (
                <>
                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600"></div>
                  <span>Loading...</span>
                </>
              ) : (
                <>
                  <span>Select Index</span>
                  <svg className={`w-4 h-4 transition-transform ${showIndexSelector ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </>
              )}
            </button> */}

            {showIndexSelector && !indexesLoading && (
              <div className="absolute top-full left-0 mt-1 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg z-[50] max-h-60 overflow-y-auto">
                {pineconeIndexes.length > 0 ? (
                  pineconeIndexes.map((index) => (
                    <button
                      key={index}
                      onClick={() => {
                        setSelectedIndex(index);
                        setShowIndexSelector(false);
                        setShowIndexConfirmation(true);
                        localStorage.setItem('selectedFaissIndex', index);
                        localStorage.setItem('faiss_index_name', index);
                        console.log(`Selected index: ${index}`);
                      }}
                      className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex items-center justify-between
                        ${selectedIndex === index ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' : 'text-gray-700 dark:text-gray-300'}`}
                    >
                      <span>{index}</span>
                      {selectedIndex === index && (
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      )}
                    </button>
                  ))
                ) : (
                  <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
                    No indexes available
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Index Indicator - Shows which index is currently selected */}
          <div className="px-2 py-0.5 bg-blue-50 dark:bg-blue-900/20 rounded-md text-xs font-medium text-gray-600 dark:text-gray-400 border border-blue-200 dark:border-blue-800/50 shadow-sm flex items-center gap-1">
            <span>Active Category:</span>
            <span className="text-blue-600 dark:text-blue-400 font-semibold">
              {selectedIndex || "Default"}
            </span>
          </div>
        </div>
      )}

      {/* Index Selection Confirmation */}
      {showIndexConfirmation && (
        <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg animate-fadeIn">
          <div className="flex items-center gap-2 text-green-700 dark:text-green-400">
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            <span className="text-sm font-medium">
              Index "{selectedIndex}" selected! Your responses will be filtered based on this index.
            </span>
          </div>
        </div>
      )}

      {/* Data Sources Indicator */}
      {/* <UploadedContentIndicator
        hasUploadedContent={responseHasUploadedContent}
        selectedIndex={selectedIndex}
        uploadSources={responseUploadSources}
        selectedLanguage={selectedLanguage.toLowerCase()}
      /> */}

      <form
        onSubmit={handleSendMessage}
        className="w-full bg-primaryColor/5 p-2 sm:p-3 lg:p-4 rounded-xl border border-primaryColor/20"
      >
        {/* Language validation error message */}
        {languageError && (
          <div className="mb-3 p-3 bg-red-50 border border-red-200 rounded-lg text-red-600 text-sm flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            {languageError}
          </div>
        )}


        <div className="w-full bg-white rounded-lg max-lg:text-sm block dark:bg-n0 relative z-[5]">


          {isListening || transcript ? (
            <div className="w-full p-4 min-h-[56px] max-h-[200px] overflow-y-auto relative flex flex-col">
              {isEditingTranscript ? (
                // Editable textarea for transcript
                <div className="flex-grow relative">
                  <textarea
                    ref={editableTranscriptRef}
                    className="w-full h-full min-h-[80px] p-2 border border-primaryColor/30 rounded-md focus:outline-none focus:ring-2 focus:ring-primaryColor/50 text-gray-800 resize-none"
                    value={editedTranscript}
                    onChange={handleTranscriptChange}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        // Only submit if there's text to send and not currently loading
                        if (editedTranscript.trim() && !isLoading) {
                          // Create a synthetic form event to trigger handleSendMessage
                          const syntheticEvent = {
                            preventDefault: () => {},
                            stopPropagation: () => {},
                            nativeEvent: e.nativeEvent,
                            target: e.target,
                            currentTarget: e.currentTarget,
                            bubbles: false,
                            cancelable: false,
                            defaultPrevented: false,
                            eventPhase: 0,
                            isTrusted: false,
                            timeStamp: Date.now(),
                            type: 'submit',
                            isDefaultPrevented: () => false,
                            isPropagationStopped: () => false,
                            persist: () => {}
                          } as unknown as FormEvent;
                          handleSendMessage(syntheticEvent);
                        }
                      }
                    }}
                    placeholder="Edit your transcribed text here..."
                  />
                  <button
                    onClick={toggleTranscriptEditMode}
                    className={`absolute top-2 right-2 p-1.5 rounded-full transition-colors
                      ${selectedLanguage === "Tamil"
                        ? 'bg-purple-100 text-purple-600 hover:bg-purple-200'
                        : selectedLanguage === "Telugu"
                          ? 'bg-green-100 text-green-600 hover:bg-green-200'
                          : 'bg-blue-100 text-blue-600 hover:bg-blue-200'
                      }`}
                    title="Save edits"
                  >
                    <PiCheck className="text-lg" />
                  </button>
                </div>
              ) : (
                // View mode for transcript
                <div
                  ref={transcriptRef}
                  className="flex-grow text-gray-800 dark:text-white break-words relative"
                >
                  {transcript && transcript.trim() !== "" ? (
                    // Show transcript with animated cursor
                    <div className="relative">
                      <p className="m-0 leading-relaxed">
                        {transcript ? transcript : ""}
                        {speaking && (
                          <span className="inline-block w-1 h-4 bg-primaryColor ml-1"
                            style={{
                              animationName: 'pulse',
                              animationDuration: '1s',
                              animationIterationCount: 'infinite',
                              animationDirection: 'alternate'
                            }}
                          ></span>
                        )}
                      </p>

                      {/* Action buttons - only show when not actively listening */}
                      {!isListening && transcript && transcript.trim() !== "" && (
                        <div className="absolute top-0 right-0 flex gap-1">
                          <button
                            onClick={() => {
                              setTranscript("");
                              setInputText("");
                              setUserQuery("");
                              resetTranscript();
                              setWordCount(0);
                              setRecentWords([]);
                            }}
                            className={`p-1.5 rounded-full transition-colors
                              ${selectedLanguage === "Tamil"
                                ? 'bg-red-100 text-red-600 hover:bg-red-200'
                                : selectedLanguage === "Telugu"
                                  ? 'bg-red-100 text-red-600 hover:bg-red-200'
                                  : 'bg-red-100 text-red-600 hover:bg-red-200'
                              }`}
                            title="Clear transcript"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          </button>
                          <button
                            onClick={toggleTranscriptEditMode}
                            className={`p-1.5 rounded-full transition-colors
                              ${selectedLanguage === "Tamil"
                                ? 'bg-purple-100 text-purple-600 hover:bg-purple-200'
                                : selectedLanguage === "Telugu"
                                  ? 'bg-green-100 text-green-600 hover:bg-green-200'
                                  : 'bg-blue-100 text-blue-600 hover:bg-blue-200'
                              }`}
                            title="Edit transcript"
                          >
                            <PiPencilSimple className="text-lg" />
                          </button>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="flex items-center gap-2 text-gray-400 italic">
                      <span>
                        {selectedLanguage === "Tamil"
                          ? "தமிழில் கேட்கிறது... இப்போது பேசவும்"
                          : selectedLanguage === "Telugu"
                            ? "తెలుగులో వింటున్నాము... ఇప్పుడు మాట్లాడండి"
                            : selectedLanguage === "Kannada"
                              ? "ಕನ್ನಡದಲ್ಲಿ ಆಲಿಸುತ್ತಿದ್ದೇವೆ... ಈಗ ಮಾತನಾಡಿ"
                              : `Listening in ${selectedLanguage}... Speak now`}
                      </span>
                      <div className="flex space-x-1 ml-2">
                        {[0, 1, 2].map((i) => (
                          <div
                            key={`dot-${i}`} /* Use stable key */
                            className={`h-2 w-2 rounded-full ${
                              selectedLanguage === "Tamil"
                                ? "bg-purple-500"
                                : selectedLanguage === "Telugu"
                                  ? "bg-green-500"
                                  : selectedLanguage === "Kannada"
                                    ? "bg-orange-500"
                                    : "bg-red-500"
                            }`}
                            style={{
                              animationName: 'pulse',
                              animationDuration: '1s',
                              animationIterationCount: 'infinite',
                              animationDirection: 'alternate',
                              animationDelay: `${i * 0.2}s`
                            }}
                          ></div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Speaking indicator */}
              {(transcript && transcript.trim() !== "") && (
                <div className="mt-2 flex items-center justify-between text-xs text-gray-500 border-t pt-2 border-gray-100">
                  <div className="flex items-center gap-2">
                    {isEditingTranscript ? (
                      <span className="text-blue-500 flex items-center gap-1">
                        <PiPencilSimple />
                        {selectedLanguage === "Tamil"
                          ? "திருத்துகிறது"
                          : selectedLanguage === "Telugu"
                            ? "సవరిస్తోంది"
                            : selectedLanguage === "Kannada"
                              ? "ಸಂಪಾದಿಸುತ್ತಿದೆ"
                              : "Editing"}
                      </span>
                    ) : speaking ? (
                      <span className="text-green-500 flex items-center gap-1">
                        <PiWaveform className="animate-pulse" />
                        {selectedLanguage === "Tamil"
                          ? "செயலில்"
                          : selectedLanguage === "Telugu"
                            ? "యాక్టివ్"
                            : selectedLanguage === "Kannada"
                              ? "ಸಕ್ರಿಯ"
                              : "Active"}
                      </span>
                    ) : (
                      <span className="text-gray-400 flex items-center gap-1">
                        <PiWaveform />
                        {selectedLanguage === "Tamil"
                          ? "இடைநிறுத்தப்பட்டது"
                          : selectedLanguage === "Telugu"
                            ? "నిలిపివేయబడింది"
                            : selectedLanguage === "Kannada"
                              ? "ವಿರಾಮಗೊಳಿಸಲಾಗಿದೆ"
                              : "Paused"}
                      </span>
                    )}
                  </div>
                  <span className="bg-gray-100 px-2 py-1 rounded-full text-xs">
                    {selectedLanguage === "Tamil"
                      ? `${wordCount} சொற்கள்`
                      : selectedLanguage === "Telugu"
                        ? `${wordCount} పదాలు`
                        : selectedLanguage === "Kannada"
                          ? `${wordCount} ಪದಗಳು`
                          : `${wordCount} words`}
                  </span>
                </div>
              )}
            </div>
          ) : (
            <div className="relative w-full">
              {/* Uploaded content display for regular input mode */}
              {showUploadedContent && (uploadedFiles.length > 0 || uploadedURLs.length > 0) && (
                <div className="px-4 pt-3 pb-2 border-b border-gray-100 dark:border-gray-700">
                  <div className="space-y-2">
                    {/* Files */}
                    {uploadedFiles.map((file, index) => (
                      <div
                        key={`file-${index}`}
                        className="flex items-center gap-2 text-sm text-gray-700 dark:text-gray-300"
                      >
                        <div className={`flex-shrink-0 ${
                          selectedLanguage === "Tamil"
                            ? 'text-purple-600'
                            : selectedLanguage === "Telugu"
                              ? 'text-green-600'
                              : selectedLanguage === "Kannada"
                                ? 'text-orange-600'
                                : 'text-blue-600'
                        }`}>
                          {getFileIcon(file.name)}
                        </div>
                        <span className="flex-grow truncate font-medium">
                          {file.name}
                        </span>
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {(file.size / 1024 / 1024).toFixed(1)}MB
                        </span>
                        <button
                          onClick={() => removeUploadedFile(index)}
                          className="flex-shrink-0 p-1 text-gray-400 hover:text-red-500 transition-colors rounded"
                          title={getUploadDisplayText().removeFile}
                        >
                          <PiX className="w-3 h-3" />
                        </button>
                      </div>
                    ))}

                    {/* URLs */}
                    {uploadedURLs.map((urlItem, index) => (
                      <div
                        key={`url-${index}`}
                        className="flex items-center gap-2 text-sm text-gray-700 dark:text-gray-300"
                      >
                        <div className={`flex-shrink-0 ${
                          urlItem.type === 'youtube' ? 'text-red-600' :
                          selectedLanguage === "Tamil"
                            ? 'text-purple-600'
                            : selectedLanguage === "Telugu"
                              ? 'text-green-600'
                              : selectedLanguage === "Kannada"
                                ? 'text-orange-600'
                                : 'text-blue-600'
                        }`}>
                          {getUrlIcon(urlItem.type)}
                        </div>
                        <span className="flex-grow truncate font-medium">
                          {urlItem.type === 'youtube'
                            ? getUploadDisplayText().youtubeVideo
                            : getUploadDisplayText().articleLink}
                        </span>
                        <span className="text-xs text-gray-500 dark:text-gray-400 truncate max-w-[100px]">
                          {urlItem.url.replace(/^https?:\/\//, '')}
                        </span>
                        <button
                          onClick={() => removeUploadedURL(index)}
                          className="flex-shrink-0 p-1 text-gray-400 hover:text-red-500 transition-colors rounded"
                          title={getUploadDisplayText().removeUrl}
                        >
                          <PiX className="w-3 h-3" />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="relative flex items-center w-full">
                <input
                  className="w-full outline-none p-4 pr-12 bg-transparent relative z-[1]"
                placeholder={
                  selectedLanguage === "Tamil"
                    ? "நிதி தொடர்பான கேள்வியை தமிழில் கேளுங்கள்..."
                    : selectedLanguage === "Telugu"
                      ? "ఒక ఆర్థిక విషయంపై నేను ఒక ప్రశ్నను అడగదలుచుకున్నాను..."
                      : selectedLanguage === "Kannada"
                        ? "ಹಣಕಾಸು ವಿಷಯದ ಬಗ್ಗೆ ಪ್ರಶ్ನೆಯನ್ನು ಕೇಳಿ..."
                        : "ask question based financial topic.."
                }
                value={inputText}
                onChange={(e) => {
                  setUserQuery(e.target.value);
                  setInputText(e.target.value);
                  // Clear any language error when the user starts typing
                  if (languageError) {
                    setLanguageError(null);
                  }
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    // Only submit if there's text to send and not currently loading
                    if (inputText.trim() && !isLoading) {
                      // Create a synthetic form event to trigger handleSendMessage
                          const syntheticEvent = {
                            preventDefault: () => {},
                            stopPropagation: () => {},
                            nativeEvent: e.nativeEvent,
                            target: e.target,
                            currentTarget: e.currentTarget,
                            bubbles: false,
                            cancelable: false,
                            defaultPrevented: false,
                            eventPhase: 0,
                            isTrusted: false,
                            timeStamp: Date.now(),
                            type: 'submit',
                            isDefaultPrevented: () => false,
                            isPropagationStopped: () => false,
                            persist: () => {}
                          } as unknown as FormEvent;
                      handleSendMessage(syntheticEvent);
                    }
                  }
                }}
                disabled={isLoading}
                onClick={() => {
                  // Close any open dropdowns when clicking in the input field
                  // No longer need to close language dropdown since we're using horizontal buttons
                  // setShowLanguageDropdown(false);
                  setShowSuggestions(false);
                }}
              />

              {/* Upload component positioned to the right of the input with improved alignment */}
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2 z-[2]">
                  <ChatInputUpload
                    selectedLanguage={selectedLanguage}
                    disabled={isLoading}
                    onUploadStateChange={handleUploadStateChange}
                    onNetworkError={() => setShowConnectionTest(true)}
                    onFileUpload={(files: File[]) => {
                      console.log('Files uploaded:', files);
                      // Only process if we have valid files
                      if (files && files.length > 0) {
                        // Prevent duplicates by checking if files already exist
                        setUploadedFiles(prevFiles => {
                          const newFiles = files.filter(newFile =>
                            !prevFiles.some(existingFile =>
                              existingFile.name === newFile.name &&
                              existingFile.size === newFile.size &&
                              existingFile.lastModified === newFile.lastModified
                            )
                          );
                          if (newFiles.length > 0) {
                            setShowUploadedContent(true);
                            return [...prevFiles, ...newFiles];
                          }
                          return prevFiles;
                        });
                      }
                    }}
                    onURLSubmit={(url: string, type: 'youtube' | 'article') => {
                      console.log('URL submitted:', url, 'Type:', type);
                      // Validate URL before adding
                      if (url && url.trim()) {
                        // Prevent duplicate URLs
                        setUploadedURLs(prev => {
                          const urlExists = prev.some(existingUrl => existingUrl.url === url.trim());
                          if (urlExists) {
                            console.log('URL already exists:', url);
                            return prev;
                          }
                          setShowUploadedContent(true);
                          return [...prev, { url: url.trim(), type }];
                        });
                      }
                    }}
                  />
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-0 pt-3 sm:pt-4">
          <div className="flex justify-start items-center gap-3 relative">
            <button
              type="button"
              onClick={() => setShowSuggestions(!showSuggestions)}
              className={`bg-white px-3 py-2 rounded-lg flex items-center gap-2 border
                ${showSuggestions ? 'border-primaryColor bg-primaryColor/5' : 'border-primaryColor/20'}
                hover:bg-primaryColor/5 hover:border-primaryColor transition-all shadow-sm hover:shadow dark:bg-n0
                text-sm font-medium
                ${selectedLanguage === "Tamil"
                  ? 'text-purple-700'
                  : selectedLanguage === "Telugu"
                    ? 'text-green-700'
                    : selectedLanguage === "Kannada"
                      ? 'text-orange-700'
                      : 'text-gray-700'}
                dark:text-gray-300`}>
              <PiLightbulb className={`${showSuggestions
                ? 'text-primaryColor'
                : selectedLanguage === "Tamil"
                  ? 'text-purple-500'
                  : selectedLanguage === "Telugu"
                    ? 'text-green-500'
                    : selectedLanguage === "Kannada"
                      ? 'text-orange-500'
                      : 'text-amber-500'}`} />
              <span>{
                selectedLanguage === "Tamil"
                  ? 'பரிந்துரைகள்'
                  : selectedLanguage === "Telugu"
                    ? 'సూచనలు'
                    : selectedLanguage === "Kannada"
                      ? 'ಸಲಹೆಗಳು'
                      : 'Suggestions'
              }</span>
              <PiSparkle className={`${showSuggestions
                ? 'text-primaryColor'
                : selectedLanguage === "Tamil"
                  ? 'text-purple-400'
                  : selectedLanguage === "Telugu"
                    ? 'text-green-400'
                    : selectedLanguage === "Kannada"
                      ? 'text-orange-400'
                      : 'text-amber-400'}
                ${!showSuggestions ? 'animate-pulse' : ''}`} />
            </button>

            {/* Suggestions popup with professional styling and animations */}
            {showSuggestions && (
              <div
                className={`fixed inset-0 flex items-center justify-center z-[60] animate-fadeIn ${
                  selectedLanguage === "Tamil"
                    ? 'bg-gradient-to-br from-purple-900/30 to-black/40'
                    : selectedLanguage === "Telugu"
                      ? 'bg-gradient-to-br from-green-900/30 to-black/40'
                      : selectedLanguage === "Kannada"
                        ? 'bg-gradient-to-br from-orange-900/30 to-black/40'
                        : 'bg-gradient-to-br from-blue-900/30 to-black/40'
                }`}
                onClick={() => setShowSuggestions(false)}
                style={{ backdropFilter: 'blur(3px)', animationDuration: '0.2s' }}
              >
                <div
                  ref={suggestionsRef}
                  className={`rounded-xl shadow-2xl border max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden animate-scaleIn ${
                    selectedLanguage === "Tamil"
                      ? 'border-purple-200/50 bg-gradient-to-b from-white to-purple-50/30'
                      : selectedLanguage === "Telugu"
                        ? 'border-green-200/50 bg-gradient-to-b from-white to-green-50/30'
                        : selectedLanguage === "Kannada"
                          ? 'border-orange-200/50 bg-gradient-to-b from-white to-orange-50/30'
                          : 'border-blue-200/50 bg-gradient-to-b from-white to-blue-50/30'
                  }`}
                  onClick={(e) => e.stopPropagation()}
                  style={{
                    animationDuration: '0.3s',
                    boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.05)'
                  }}
                >
                  <div className={`p-5 border-b flex justify-between items-center rounded-t-xl ${
                    selectedLanguage === "Tamil"
                      ? 'bg-gradient-to-r from-purple-50 to-purple-100 border-purple-100'
                      : selectedLanguage === "Telugu"
                        ? 'bg-gradient-to-r from-green-50 to-green-100 border-green-100'
                        : selectedLanguage === "Kannada"
                          ? 'bg-gradient-to-r from-orange-50 to-orange-100 border-orange-100'
                          : 'bg-gradient-to-r from-blue-50 to-blue-100 border-blue-100'
                  }`}>
                    <h3 className="text-lg font-semibold flex items-center gap-2">
                      <div className={`p-2 rounded-full ${
                        selectedLanguage === "Tamil"
                          ? 'bg-purple-100'
                          : selectedLanguage === "Telugu"
                            ? 'bg-green-100'
                            : selectedLanguage === "Kannada"
                              ? 'bg-orange-100'
                              : 'bg-blue-100'
                      }`}>
                        <PiLightbulb className={`text-xl ${
                          selectedLanguage === "Tamil"
                            ? 'text-purple-500'
                            : selectedLanguage === "Telugu"
                              ? 'text-green-500'
                              : selectedLanguage === "Kannada"
                                ? 'text-orange-500'
                                : 'text-blue-500'
                        }`} />
                      </div>
                      <span className={
                        selectedLanguage === "Tamil"
                          ? 'text-purple-800'
                          : selectedLanguage === "Telugu"
                            ? 'text-green-800'
                            : selectedLanguage === "Kannada"
                              ? 'text-orange-800'
                              : 'text-blue-800'
                      }>
                        {selectedLanguage === "Tamil"
                          ? 'பரிந்துரைக்கப்பட்ட நிதி கேள்விகள்'
                          : selectedLanguage === "Telugu"
                            ? 'సిఫార్సు చేయబడిన ఆర్థిక ప్రశ్నలు'
                            : selectedLanguage === "Kannada"
                              ? 'ಶಿఫಾರಸು ಮಾಡಲಾದ ಹಣಕಾಸು ಪ್ರಶ್ನೆಗಳು'
                              : 'Recommended Financial Questions'}
                      </span>
                    </h3>
                    <button
                      onClick={() => setShowSuggestions(false)}
                      className={`p-2 rounded-full transition-colors ${
                        selectedLanguage === "Tamil"
                          ? 'bg-purple-100/50 text-purple-500 hover:bg-purple-200 hover:text-purple-700'
                          : selectedLanguage === "Telugu"
                            ? 'bg-green-100/50 text-green-500 hover:bg-green-200 hover:text-green-700'
                            : selectedLanguage === "Kannada"
                              ? 'bg-orange-100/50 text-orange-500 hover:bg-orange-200 hover:text-orange-700'
                              : 'bg-blue-100/50 text-blue-500 hover:bg-blue-200 hover:text-blue-700'
                      }`}
                      aria-label="Close"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>

                  <div className="p-5 overflow-y-auto max-h-[60vh]">
                    <div className={`rounded-lg p-3 mb-5 ${
                      selectedLanguage === "Tamil"
                        ? 'bg-gradient-to-r from-purple-50 to-white border border-purple-100'
                        : selectedLanguage === "Telugu"
                          ? 'bg-gradient-to-r from-green-50 to-white border border-green-100'
                          : selectedLanguage === "Kannada"
                            ? 'bg-gradient-to-r from-orange-50 to-white border border-orange-100'
                            : 'bg-gradient-to-r from-blue-50 to-white border border-blue-100'
                    }`}>
                      <p className={`text-sm font-medium ${
                        selectedLanguage === "Tamil"
                          ? 'text-purple-700'
                          : selectedLanguage === "Telugu"
                            ? 'text-green-700'
                            : selectedLanguage === "Kannada"
                              ? 'text-orange-700'
                              : 'text-blue-700'
                      }`}>
                        {selectedLanguage === "Tamil"
                          ? 'உங்கள் நிதி தேவைகளுக்கான பரிந்துரைக்கப்பட்ட கேள்விகளைப் பார்க்கவும்'
                          : selectedLanguage === "Telugu"
                            ? 'మీ ఆర్థిక అవసరాలకు సిఫార్సు చేయబడిన ప్రశ్నలను చూడండి'
                            : selectedLanguage === "Kannada"
                              ? 'ನಿಮ್ಮ ಹಣಕಾಸು ಅಗತ್ಯಗಳಿಗಾಗಿ ಈ ಶಿಫಾರಸು ಮಾಡಲಾದ ಪ್ರಶ್ನೆಗಳನ್ನು ಆಯ್ಕೆಮಾಡಿ'
                              : 'Select from these recommended questions for your financial needs'}
                      </p>
                    </div>

                    <div className="space-y-3">
                      {recommendedSuggestions.map((suggestion, index) => (
                        <button
                          key={index}
                          onClick={() => {
                            handleSelectSuggestion(suggestion);
                            setShowSuggestions(false);
                          }}
                          className={`w-full text-left px-5 py-4 text-sm rounded-lg border shadow-sm hover:shadow-md transition-all
                            ${selectedLanguage === "Tamil"
                              ? 'border-purple-200 bg-white hover:bg-gradient-to-r hover:from-purple-50 hover:to-white hover:border-purple-300'
                              : selectedLanguage === "Telugu"
                                ? 'border-green-200 bg-white hover:bg-gradient-to-r hover:from-green-50 hover:to-white hover:border-green-300'
                                : selectedLanguage === "Kannada"
                                  ? 'border-orange-200 bg-white hover:bg-gradient-to-r hover:from-orange-50 hover:to-white hover:border-orange-300'
                                  : 'border-blue-200 bg-white hover:bg-gradient-to-r hover:from-blue-50 hover:to-white hover:border-blue-300'}
                            text-gray-700 animate-fadeInUp`}
                          style={{ animationDelay: `${index * 0.05}s`, animationDuration: '0.3s' }}
                        >
                          <div className="flex items-start">
                            <span className={`inline-block p-1.5 rounded-full mr-3 mt-0.5 ${
                              selectedLanguage === "Tamil"
                                ? 'bg-gradient-to-br from-purple-100 to-purple-200 text-purple-600'
                                : selectedLanguage === "Telugu"
                                  ? 'bg-gradient-to-br from-green-100 to-green-200 text-green-600'
                                  : selectedLanguage === "Kannada"
                                    ? 'bg-gradient-to-br from-orange-100 to-orange-200 text-orange-600'
                                    : 'bg-gradient-to-br from-blue-100 to-blue-200 text-blue-600'
                            }`}>
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
                              </svg>
                            </span>
                            <span className="font-medium">{suggestion}</span>
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>

                  <div className={`p-4 border-t rounded-b-xl text-right ${
                    selectedLanguage === "Tamil"
                      ? 'bg-gradient-to-r from-purple-50 to-purple-100 border-purple-100'
                      : selectedLanguage === "Telugu"
                        ? 'bg-gradient-to-r from-green-50 to-green-100 border-green-100'
                        : selectedLanguage === "Kannada"
                          ? 'bg-gradient-to-r from-orange-50 to-orange-100 border-orange-100'
                          : 'bg-gradient-to-r from-blue-50 to-blue-100 border-blue-100'
                  }`}>
                    <button
                      onClick={() => setShowSuggestions(false)}
                      className={`px-5 py-2 rounded-lg text-white font-medium shadow-sm transition-all hover:shadow-md
                        ${selectedLanguage === "Tamil"
                          ? 'bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700'
                          : selectedLanguage === "Telugu"
                            ? 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700'
                            : selectedLanguage === "Kannada"
                              ? 'bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700'
                              : 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700'}`}
                    >
                      {selectedLanguage === "Tamil"
                        ? 'மூடு'
                        : selectedLanguage === "Telugu"
                          ? 'మూసివేయండి'
                          : selectedLanguage === "Kannada"
                            ? 'ಮುಚ್ಚಿರಿ'
                            : 'Close'}
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
          <div className="flex justify-between items-center gap-4">
            {/* Language selection - responsive design */}
            <div className="flex-grow">
              {/* Desktop view - horizontal buttons */}
              <div className="hidden md:flex md:items-center">
                {/* <div className="flex items-center mr-4">
                  <PiGlobe className="text-gray-600 mr-2 text-lg" />
                  <span className="text-sm font-medium text-gray-700">Select Language:</span>
                </div> */}

                <div className="flex space-x-4">
                  {/* Show English, Tamil, and Telugu buttons as prominent language selectors on desktop */}
                  {languages.map((language, index) => {
                    const isSelected = selectedLanguage === language.name;

                    // Get color classes based on language
                    const getColorClass = () => {
                      if (language.name === "Tamil") return "text-purple-700 border-purple-300 bg-purple-50";
                      if (language.name === "Telugu") return "text-green-700 border-green-300 bg-green-50";
                      if (language.name === "Kannada") return "text-orange-700 border-orange-300 bg-orange-50";
                      return "text-blue-700 border-blue-300 bg-blue-50";
                    };

                    const getHoverClass = () => {
                      if (language.name === "Tamil") return "hover:bg-purple-100 hover:border-purple-400";
                      if (language.name === "Telugu") return "hover:bg-green-100 hover:border-green-400";
                      if (language.name === "Kannada") return "hover:bg-orange-100 hover:border-orange-400";
                      return "hover:bg-blue-100 hover:border-blue-400";
                    };

                    const getIconColor = () => {
                      if (language.name === "Tamil") return "text-purple-600";
                      if (language.name === "Telugu") return "text-green-600";
                      if (language.name === "Kannada") return "text-orange-600";
                      return "text-blue-600";
                    };

                    const getBorderColor = () => {
                      if (language.name === "Tamil") return "bg-purple-500";
                      if (language.name === "Telugu") return "bg-green-500";
                      if (language.name === "Kannada") return "bg-orange-500";
                      return "bg-blue-500";
                    };

                    // Using type="button" to prevent form submission when clicking language buttons
                    return (
                      <button
                        key={index}
                        type="button"
                        onClick={(e) => handleSelectLanguage(e, language.name)}
                        disabled={languageButtonsDisabled || isLoading}
                        className={`px-5 py-2 rounded-lg text-sm font-medium transition-all border relative
                          ${isSelected
                            ? getColorClass()
                            : 'bg-white text-gray-600 border-gray-200 ' + getHoverClass()}
                          transform transition-all duration-300 ease-in-out
                          ${isSelected ? 'scale-105 shadow-md' : 'hover:scale-105 hover:shadow-sm'}
                          ${(languageButtonsDisabled || isLoading) ? 'opacity-50 cursor-not-allowed' : ''}
                          animate-fadeIn
                        `}
                        style={{
                          animationDelay: `${index * 0.05}s`,
                          animationDuration: '0.3s'
                        }}
                      >
                        <div className="flex items-center gap-2">
                          <PiGlobe className={`${getIconColor()} text-lg ${isSelected ? 'animate-fadeIn' : ''}`}
                            style={{ animationDuration: '0.3s' }}
                          />
                          <span className="font-medium">{language.name}</span>
                        </div>

                        {/* Bottom border animation for selected language */}
                        {isSelected && (
                          <div
                            className={`absolute bottom-0 left-0 h-0.5 rounded-full animate-scaleInHorizontal ${getBorderColor()}`}
                            style={{
                              width: '100%',
                              transformOrigin: 'left',
                              animationDuration: '0.4s'
                            }}
                          ></div>
                        )}

                        {/* Top border animation for selected language */}
                        {isSelected && (
                          <div
                            className={`absolute top-0 right-0 h-0.5 rounded-full animate-scaleInHorizontal ${getBorderColor()}`}
                            style={{
                              width: '100%',
                              transformOrigin: 'right',
                              animationDuration: '0.4s',
                              animationDelay: '0.1s'
                            }}
                          ></div>
                        )}
                      </button>
                    );
                  })}
                </div>
              </div>

              {/* Mobile view - dropup menu */}
              <div className="md:hidden relative" ref={languageMenuRef}>
                <button
                  type="button"
                  onClick={() => setShowLanguageMenu(!showLanguageMenu)}
                  disabled={languageButtonsDisabled || isLoading}
                  className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-all
                    ${selectedLanguage === "Tamil"
                      ? "text-purple-700 border-purple-300 bg-purple-50"
                      : selectedLanguage === "Telugu"
                        ? "text-green-700 border-green-300 bg-green-50"
                        : selectedLanguage === "Kannada"
                          ? "text-orange-700 border-orange-300 bg-orange-50"
                          : "text-blue-700 border-blue-300 bg-blue-50"
                    }
                    hover:shadow-sm
                    ${(languageButtonsDisabled || isLoading) ? 'opacity-50 cursor-not-allowed' : ''}
                  `}
                >
                  <PiGlobe className={`text-lg ${
                    selectedLanguage === "Tamil"
                      ? "text-purple-600"
                      : selectedLanguage === "Telugu"
                        ? "text-green-600"
                        : selectedLanguage === "Kannada"
                          ? "text-orange-600"
                          : "text-blue-600"
                  }`} />
                  <span className="font-medium text-sm">{selectedLanguage}</span>
                  <span className={`transition-transform duration-300 ${showLanguageMenu ? 'rotate-180' : ''}`}>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                    </svg>
                  </span>
                </button>

                {/* Dropup menu */}
                {showLanguageMenu && (
                  <div className="absolute bottom-full left-0 mb-1 w-full bg-white dark:bg-n0 rounded-lg border shadow-lg z-[40] overflow-hidden animate-fadeIn"
                    style={{ animationDuration: '0.2s' }}
                  >
                    {languages.map((language, index) => {
                      const isSelected = selectedLanguage === language.name;

                      // Get color classes based on language
                      const getItemColorClass = () => {
                        if (language.name === "Tamil") return isSelected ? "bg-purple-50 text-purple-700" : "hover:bg-purple-50";
                        if (language.name === "Telugu") return isSelected ? "bg-green-50 text-green-700" : "hover:bg-green-50";
                        if (language.name === "Kannada") return isSelected ? "bg-orange-50 text-orange-700" : "hover:bg-orange-50";
                        return isSelected ? "bg-blue-50 text-blue-700" : "hover:bg-blue-50";
                      };

                      const getIconColor = () => {
                        if (language.name === "Tamil") return "text-purple-600";
                        if (language.name === "Telugu") return "text-green-600";
                        if (language.name === "Kannada") return "text-orange-600";
                        return "text-blue-600";
                      };

                      return (
                        <button
                          key={index}
                          type="button"
                          onClick={(e) => {
                            handleSelectLanguage(e, language.name);
                            setShowLanguageMenu(false);
                          }}
                          disabled={languageButtonsDisabled || isLoading}
                          className={`w-full flex items-center gap-2 px-4 py-3 text-left text-sm ${getItemColorClass()} transition-colors
                            ${(languageButtonsDisabled || isLoading) ? 'opacity-50 cursor-not-allowed' : ''}
                          `}
                          style={{
                            animationDelay: `${index * 0.05}s`,
                          }}
                        >
                          <PiGlobe className={`${getIconColor()} text-lg`} />
                          <span className="font-medium">{language.name}</span>
                          {isSelected && (
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                          )}
                        </button>
                      );
                    })}
                  </div>
                )}
              </div>
            </div>

            {/* Action buttons container with improved spacing */}
            <div className="flex items-center gap-4">
              {/* Microphone button with enhanced alignment - hide when upload is active or dropdown is visible */}
              {!uploadIsActive && !uploadDropdownVisible && (
                <div className="relative z-[3] flex items-center justify-center">
                  {browserSupportsSpeechRecognition ? (
                  <button
                    type="button"
                    onClick={isListening ? toggleListening : toggleListening}
                    className={`p-3 rounded-full flex justify-center items-center border-2 transition-all duration-200 shadow-md hover:shadow-lg relative z-[3] min-w-[48px] min-h-[48px]
                      ${isListening
                        ? 'border-red-500 bg-gradient-to-r from-red-50 to-red-100 hover:from-red-100 hover:to-red-200 scale-105'
                        : selectedLanguage === "Tamil"
                          ? 'border-purple-400 bg-white hover:bg-purple-50 hover:border-purple-500 hover:scale-105'
                          : selectedLanguage === "Telugu"
                            ? 'border-green-400 bg-white hover:bg-green-50 hover:border-green-500 hover:scale-105'
                            : selectedLanguage === "Kannada"
                              ? 'border-orange-400 bg-white hover:bg-orange-50 hover:border-orange-500 hover:scale-105'
                              : 'border-blue-400 bg-white hover:bg-blue-50 hover:border-blue-500 hover:scale-105'
                      }
                      dark:bg-n0 transform`}
                    title={isListening ? "Stop voice recording" : `Start voice recording in ${selectedLanguage}`}
                  >
                    {isListening ? (
                      <div className="relative flex items-center justify-center">
                        <div
                          className="absolute -inset-2 bg-red-200 rounded-full opacity-60"
                          style={{
                            animationName: 'pulse',
                            animationDuration: '1.5s',
                            animationIterationCount: 'infinite',
                            animationDirection: 'alternate'
                          }}
                        ></div>
                        <PiStop className="text-red-600 relative z-[4] w-5 h-5" />
                        <span
                          className="absolute -top-1 -right-1 h-3 w-3 rounded-full bg-red-500"
                          style={{
                            animationName: 'ping',
                            animationDuration: '1.5s',
                            animationIterationCount: 'infinite'
                          }}
                        ></span>
                      </div>
                    ) : (
                      <PiMicrophone className={`w-5 h-5 ${
                        selectedLanguage === "Tamil"
                          ? 'text-purple-600'
                          : selectedLanguage === "Telugu"
                            ? 'text-green-600'
                            : selectedLanguage === "Kannada"
                              ? 'text-orange-600'
                              : 'text-blue-600'
                      }`} />
                    )}
                  </button>
                ) : (
                  <button
                    type="button"
                    className="bg-white p-3 rounded-full flex justify-center items-center border-2 border-gray-300 text-gray-400 cursor-not-allowed min-w-[48px] min-h-[48px] shadow-md"
                    title="Speech recognition not supported in this browser"
                    disabled
                  >
                    <PiMicrophone className="w-5 h-5" />
                  </button>
                )}
                </div>
              )}

              {/* Submit button with enhanced alignment */}
              <button
                type="submit"
                className={`rounded-full flex justify-center items-center border-2 text-white shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105 min-h-[48px]
                  ${selectedLanguage === "Tamil"
                    ? "px-5 py-3 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 border-purple-600"
                    : selectedLanguage === "Telugu"
                      ? "px-5 py-3 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 border-green-600"
                      : selectedLanguage === "Kannada"
                        ? "px-5 py-3 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 border-orange-600"
                        : "px-5 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 border-blue-600"}
                  ${isLoading ? 'opacity-80 cursor-not-allowed scale-100' : ''}`}
                disabled={isLoading}
                title={
                  isLoading
                    ? selectedLanguage === "Tamil"
                      ? "அனுப்புகிறது..."
                      : selectedLanguage === "Telugu"
                        ? "పంపుతోంది..."
                        : selectedLanguage === "Kannada"
                          ? "ಕಳುಹಿಸಲಾಗುತ್ತಿದೆ..."
                          : "Sending..."
                    : selectedLanguage === "Tamil"
                      ? "கேள்வியை அனுப்பு"
                      : selectedLanguage === "Telugu"
                        ? "ప్రశ్నను పంపండి"
                        : selectedLanguage === "Kannada"
                          ? "ಪ್ರಶ್ನೆಯನ್ನು ಕಳುಹಿಸಿ"
                          : "Send question"
                }
              >
                {isLoading ? (
                  // Loading state with spinner
                  selectedLanguage === "Tamil" ? (
                    <span className="text-sm font-medium flex items-center gap-1.5">
                      அனுப்புகிறது... <PiSpinner className="animate-spin" />
                    </span>
                  ) : selectedLanguage === "Telugu" ? (
                    <span className="text-sm font-medium flex items-center gap-1.5">
                      పంపుతోంది... <PiSpinner className="animate-spin" />
                    </span>
                  ) : selectedLanguage === "Kannada" ? (
                    <span className="text-sm font-medium flex items-center gap-1.5">
                      ಕಳುಹಿಸಲಾಗುತ್ತಿದೆ... <PiSpinner className="animate-spin" />
                    </span>
                  ) : (
                    <span className="text-sm font-medium flex items-center gap-1.5">
                      Sending... <PiSpinner className="animate-spin" />
                    </span>
                  )
                ) : (
                  // Normal state
                  selectedLanguage === "Tamil" ? (
                    <span className="text-sm font-medium flex items-center gap-1.5">
                      அனுப்பு <PiArrowUp />
                    </span>
                  ) : selectedLanguage === "Telugu" ? (
                    <span className="text-sm font-medium flex items-center gap-1.5">
                      పంపండి <PiArrowUp />
                    </span>
                  ) : selectedLanguage === "Kannada" ? (
                    <span className="text-sm font-medium flex items-center gap-1.5">
                      ಕಳುಹಿಸಿ <PiArrowUp />
                    </span>
                  ) : (
                    <span className="text-sm font-medium flex items-center gap-1.5">
                      Send <PiArrowUp />
                    </span>
                  )
                )}
              </button>
            </div>

            {/* Debug button - only visible in development */}
            {/* {process.env.NODE_ENV === 'development' && (
              <button
                type="button"
                onClick={() => setShowConnectionTest(true)}
                className="text-xs px-2 py-1 bg-blue-100 border border-blue-300 rounded text-blue-600 hover:bg-blue-200 dark:bg-blue-900 dark:border-blue-700 dark:text-blue-300 dark:hover:bg-blue-800"
                title="Test backend connection"
              >
                🔌 Test Connection
              </button>
            )} */}
          </div>
        </div>
      </form>

      {/* Add style for animation and z-index fixes */}
      <style jsx>{`
        @keyframes pulse {
          0% { height: 4px; }
          100% { height: 16px; }
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        /* Spinner animation */
        .animate-spin {
          animation: spin 1s linear infinite;
        }

        /* Ensure microphone elements are above other UI elements */
        .relative.z-50 {
          position: relative;
          z-index: 50;
        }

        /* Fix for overlapping elements */
        .absolute.z-50 {
          position: absolute;
          z-index: 50;
        }

        /* Ensure input is always clickable */
        input {
          position: relative;
          z-index: 30;
        }

        /* Style for editable transcript textarea */
        textarea {
          font-family: inherit;
          line-height: 1.5;
          transition: all 0.2s ease-in-out;
        }

        /* Animation for edit button */
        button:hover svg {
          transform: scale(1.1);
          transition: transform 0.2s ease-in-out;
        }

        /* Dropup animations */
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(10px); }
          to { opacity: 1; transform: translateY(0); }
        }

        .animate-fadeIn {
          animation: fadeIn 0.2s ease-out forwards;
        }

        /* Scale in animation for horizontal borders */
        @keyframes scaleInHorizontal {
          from { transform: scaleX(0); }
          to { transform: scaleX(1); }
        }

        .animate-scaleInHorizontal {
          animation: scaleInHorizontal 0.4s ease-out forwards;
        }

        /* Additional animations for upload components */
        @keyframes fadeInUp {
          from { opacity: 0; transform: translateY(20px); }
          to { opacity: 1; transform: translateY(0); }
        }

        .animate-fadeInUp {
          animation: fadeInUp 0.3s ease-out forwards;
        }

        @keyframes scaleIn {
          from { opacity: 0; transform: scale(0.95); }
          to { opacity: 1; transform: scale(1); }
        }

        .animate-scaleIn {
          animation: scaleIn 0.2s ease-out forwards;
        }
      `}</style>

      {/* Connection Test Dialog */}
      {showConnectionTest && (
        <ConnectionTest onClose={() => setShowConnectionTest(false)} />
      )}
    </div>
  );
});

ChatBox.displayName = 'ChatBox';

export default ChatBox;
