{"version": 3, "sources": [], "sections": [{"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/app/favicon--route-entry.js"], "sourcesContent": ["import { NextResponse } from 'next/server'\n\nconst contentType = \"image/x-icon\"\nconst cacheControl = \"public, max-age=0, must-revalidate\"\nconst buffer = Buffer.from(\"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\", 'base64')\n\nif (false || false) {\n    const fileSizeInMB = buffer.byteLength / 1024 / 1024\n    if (fileSizeInMB > 8) {\n        throw new Error('File size for Open Graph image \"[project]/app/favicon.ico\" exceeds 8MB. ' +\n        `(Current: ${fileSizeInMB.toFixed(2)}MB)\\n` +\n        'Read more: https://nextjs.org/docs/app/api-reference/file-conventions/metadata/opengraph-image#image-files-jpg-png-gif'\n        )\n    }\n}\n\nexport function GET() {\n    return new NextResponse(buffer, {\n        headers: {\n            'Content-Type': contentType,\n            'Cache-Control': cacheControl,\n        },\n    })\n}\n\nexport const dynamic = 'force-static'\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,cAAc;AACpB,MAAM,eAAe;AACrB,MAAM,SAAS,OAAO,IAAI,CAAC,g0PAAg0P;AAE31P,uCAAoB;;AAQpB;AAEO,SAAS;IACZ,OAAO,IAAI,8HAAA,CAAA,eAAY,CAAC,QAAQ;QAC5B,SAAS;YACL,gBAAgB;YAChB,iBAAiB;QACrB;IACJ;AACJ;AAEO,MAAM,UAAU"}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}