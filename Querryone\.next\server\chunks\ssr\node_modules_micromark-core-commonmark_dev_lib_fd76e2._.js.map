{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/node_modules/micromark-core-commonmark/dev/lib/thematic-break.js"], "sourcesContent": ["/**\n * @import {\n *   Code,\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding, markdownSpace} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const thematicBreak = {\n  name: 'thematicBreak',\n  tokenize: tokenizeThematicBreak\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeThematicBreak(effects, ok, nok) {\n  let size = 0\n  /** @type {NonNullable<Code>} */\n  let marker\n\n  return start\n\n  /**\n   * Start of thematic break.\n   *\n   * ```markdown\n   * > | ***\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    effects.enter(types.thematicBreak)\n    // To do: parse indent like `markdown-rs`.\n    return before(code)\n  }\n\n  /**\n   * After optional whitespace, at marker.\n   *\n   * ```markdown\n   * > | ***\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function before(code) {\n    assert(\n      code === codes.asterisk ||\n        code === codes.dash ||\n        code === codes.underscore,\n      'expected `*`, `-`, or `_`'\n    )\n    marker = code\n    return atBreak(code)\n  }\n\n  /**\n   * After something, before something else.\n   *\n   * ```markdown\n   * > | ***\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function atBreak(code) {\n    if (code === marker) {\n      effects.enter(types.thematicBreakSequence)\n      return sequence(code)\n    }\n\n    if (\n      size >= constants.thematicBreakMarkerCountMin &&\n      (code === codes.eof || markdownLineEnding(code))\n    ) {\n      effects.exit(types.thematicBreak)\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In sequence.\n   *\n   * ```markdown\n   * > | ***\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function sequence(code) {\n    if (code === marker) {\n      effects.consume(code)\n      size++\n      return sequence\n    }\n\n    effects.exit(types.thematicBreakSequence)\n    return markdownSpace(code)\n      ? factorySpace(effects, atBreak, types.whitespace)(code)\n      : atBreak(code)\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;AAKD;AAHA;AAGA;AAAA;AADA;AADA;;;;;AAKO,MAAM,gBAAgB;IAC3B,MAAM;IACN,UAAU;AACZ;AAEA;;;;CAIC,GACD,SAAS,sBAAsB,OAAO,EAAE,EAAE,EAAE,GAAG;IAC7C,IAAI,OAAO;IACX,8BAA8B,GAC9B,IAAI;IAEJ,OAAO;;IAEP;;;;;;;;;GASC,GACD,SAAS,MAAM,IAAI;QACjB,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,aAAa;QACjC,0CAA0C;QAC1C,OAAO,OAAO;IAChB;IAEA;;;;;;;;;GASC,GACD,SAAS,OAAO,IAAI;QAClB,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EACH,SAAS,2JAAA,CAAA,QAAK,CAAC,QAAQ,IACrB,SAAS,2JAAA,CAAA,QAAK,CAAC,IAAI,IACnB,SAAS,2JAAA,CAAA,QAAK,CAAC,UAAU,EAC3B;QAEF,SAAS;QACT,OAAO,QAAQ;IACjB;IAEA;;;;;;;;;GASC,GACD,SAAS,QAAQ,IAAI;QACnB,IAAI,SAAS,QAAQ;YACnB,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,qBAAqB;YACzC,OAAO,SAAS;QAClB;QAEA,IACE,QAAQ,+JAAA,CAAA,YAAS,CAAC,2BAA2B,IAC7C,CAAC,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,IAAI,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,KAAK,GAC/C;YACA,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,aAAa;YAChC,OAAO,GAAG;QACZ;QAEA,OAAO,IAAI;IACb;IAEA;;;;;;;;;GASC,GACD,SAAS,SAAS,IAAI;QACpB,IAAI,SAAS,QAAQ;YACnB,QAAQ,OAAO,CAAC;YAChB;YACA,OAAO;QACT;QAEA,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,qBAAqB;QACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE,QACjB,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EAAE,SAAS,SAAS,2JAAA,CAAA,QAAK,CAAC,UAAU,EAAE,QACjD,QAAQ;IACd;AACF", "ignoreList": [0]}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/node_modules/micromark-core-commonmark/dev/lib/blank-line.js"], "sourcesContent": ["/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding, markdownSpace} from 'micromark-util-character'\nimport {codes, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const blankLine = {partial: true, tokenize: tokenizeBlankLine}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeBlankLine(effects, ok, nok) {\n  return start\n\n  /**\n   * Start of blank line.\n   *\n   * > 👉 **Note**: `␠` represents a space character.\n   *\n   * ```markdown\n   * > | ␠␠␊\n   *     ^\n   * > | ␊\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    return markdownSpace(code)\n      ? factorySpace(effects, after, types.linePrefix)(code)\n      : after(code)\n  }\n\n  /**\n   * At eof/eol, after optional whitespace.\n   *\n   * > 👉 **Note**: `␠` represents a space character.\n   *\n   * ```markdown\n   * > | ␠␠␊\n   *       ^\n   * > | ␊\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    return code === codes.eof || markdownLineEnding(code) ? ok(code) : nok(code)\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AAGD;AADA;AAEA;AAAA;;;;AAGO,MAAM,YAAY;IAAC,SAAS;IAAM,UAAU;AAAiB;AAEpE;;;;CAIC,GACD,SAAS,kBAAkB,OAAO,EAAE,EAAE,EAAE,GAAG;IACzC,OAAO;;IAEP;;;;;;;;;;;;;GAaC,GACD,SAAS,MAAM,IAAI;QACjB,OAAO,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE,QACjB,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EAAE,SAAS,OAAO,2JAAA,CAAA,QAAK,CAAC,UAAU,EAAE,QAC/C,MAAM;IACZ;IAEA;;;;;;;;;;;;;GAaC,GACD,SAAS,MAAM,IAAI;QACjB,OAAO,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,IAAI,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,QAAQ,GAAG,QAAQ,IAAI;IACzE;AACF", "ignoreList": [0]}}, {"offset": {"line": 175, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 181, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/node_modules/micromark-core-commonmark/dev/lib/list.js"], "sourcesContent": ["/**\n * @import {\n *   Code,\n *   Construct,\n *   Exiter,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {asciiDigit, markdownSpace} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\nimport {blankLine} from './blank-line.js'\nimport {thematicBreak} from './thematic-break.js'\n\n/** @type {Construct} */\nexport const list = {\n  continuation: {tokenize: tokenizeListContinuation},\n  exit: tokenizeListEnd,\n  name: 'list',\n  tokenize: tokenizeListStart\n}\n\n/** @type {Construct} */\nconst listItemPrefixWhitespaceConstruct = {\n  partial: true,\n  tokenize: tokenizeListItemPrefixWhitespace\n}\n\n/** @type {Construct} */\nconst indentConstruct = {partial: true, tokenize: tokenizeIndent}\n\n// To do: `markdown-rs` parses list items on their own and later stitches them\n// together.\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeListStart(effects, ok, nok) {\n  const self = this\n  const tail = self.events[self.events.length - 1]\n  let initialSize =\n    tail && tail[1].type === types.linePrefix\n      ? tail[2].sliceSerialize(tail[1], true).length\n      : 0\n  let size = 0\n\n  return start\n\n  /** @type {State} */\n  function start(code) {\n    assert(self.containerState, 'expected state')\n    const kind =\n      self.containerState.type ||\n      (code === codes.asterisk || code === codes.plusSign || code === codes.dash\n        ? types.listUnordered\n        : types.listOrdered)\n\n    if (\n      kind === types.listUnordered\n        ? !self.containerState.marker || code === self.containerState.marker\n        : asciiDigit(code)\n    ) {\n      if (!self.containerState.type) {\n        self.containerState.type = kind\n        effects.enter(kind, {_container: true})\n      }\n\n      if (kind === types.listUnordered) {\n        effects.enter(types.listItemPrefix)\n        return code === codes.asterisk || code === codes.dash\n          ? effects.check(thematicBreak, nok, atMarker)(code)\n          : atMarker(code)\n      }\n\n      if (!self.interrupt || code === codes.digit1) {\n        effects.enter(types.listItemPrefix)\n        effects.enter(types.listItemValue)\n        return inside(code)\n      }\n    }\n\n    return nok(code)\n  }\n\n  /** @type {State} */\n  function inside(code) {\n    assert(self.containerState, 'expected state')\n    if (asciiDigit(code) && ++size < constants.listItemValueSizeMax) {\n      effects.consume(code)\n      return inside\n    }\n\n    if (\n      (!self.interrupt || size < 2) &&\n      (self.containerState.marker\n        ? code === self.containerState.marker\n        : code === codes.rightParenthesis || code === codes.dot)\n    ) {\n      effects.exit(types.listItemValue)\n      return atMarker(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * @type {State}\n   **/\n  function atMarker(code) {\n    assert(self.containerState, 'expected state')\n    assert(code !== codes.eof, 'eof (`null`) is not a marker')\n    effects.enter(types.listItemMarker)\n    effects.consume(code)\n    effects.exit(types.listItemMarker)\n    self.containerState.marker = self.containerState.marker || code\n    return effects.check(\n      blankLine,\n      // Can’t be empty when interrupting.\n      self.interrupt ? nok : onBlank,\n      effects.attempt(\n        listItemPrefixWhitespaceConstruct,\n        endOfPrefix,\n        otherPrefix\n      )\n    )\n  }\n\n  /** @type {State} */\n  function onBlank(code) {\n    assert(self.containerState, 'expected state')\n    self.containerState.initialBlankLine = true\n    initialSize++\n    return endOfPrefix(code)\n  }\n\n  /** @type {State} */\n  function otherPrefix(code) {\n    if (markdownSpace(code)) {\n      effects.enter(types.listItemPrefixWhitespace)\n      effects.consume(code)\n      effects.exit(types.listItemPrefixWhitespace)\n      return endOfPrefix\n    }\n\n    return nok(code)\n  }\n\n  /** @type {State} */\n  function endOfPrefix(code) {\n    assert(self.containerState, 'expected state')\n    self.containerState.size =\n      initialSize +\n      self.sliceSerialize(effects.exit(types.listItemPrefix), true).length\n    return ok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeListContinuation(effects, ok, nok) {\n  const self = this\n\n  assert(self.containerState, 'expected state')\n  self.containerState._closeFlow = undefined\n\n  return effects.check(blankLine, onBlank, notBlank)\n\n  /** @type {State} */\n  function onBlank(code) {\n    assert(self.containerState, 'expected state')\n    assert(typeof self.containerState.size === 'number', 'expected size')\n    self.containerState.furtherBlankLines =\n      self.containerState.furtherBlankLines ||\n      self.containerState.initialBlankLine\n\n    // We have a blank line.\n    // Still, try to consume at most the items size.\n    return factorySpace(\n      effects,\n      ok,\n      types.listItemIndent,\n      self.containerState.size + 1\n    )(code)\n  }\n\n  /** @type {State} */\n  function notBlank(code) {\n    assert(self.containerState, 'expected state')\n    if (self.containerState.furtherBlankLines || !markdownSpace(code)) {\n      self.containerState.furtherBlankLines = undefined\n      self.containerState.initialBlankLine = undefined\n      return notInCurrentItem(code)\n    }\n\n    self.containerState.furtherBlankLines = undefined\n    self.containerState.initialBlankLine = undefined\n    return effects.attempt(indentConstruct, ok, notInCurrentItem)(code)\n  }\n\n  /** @type {State} */\n  function notInCurrentItem(code) {\n    assert(self.containerState, 'expected state')\n    // While we do continue, we signal that the flow should be closed.\n    self.containerState._closeFlow = true\n    // As we’re closing flow, we’re no longer interrupting.\n    self.interrupt = undefined\n    // Always populated by defaults.\n    assert(\n      self.parser.constructs.disable.null,\n      'expected `disable.null` to be populated'\n    )\n    return factorySpace(\n      effects,\n      effects.attempt(list, ok, nok),\n      types.linePrefix,\n      self.parser.constructs.disable.null.includes('codeIndented')\n        ? undefined\n        : constants.tabSize\n    )(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeIndent(effects, ok, nok) {\n  const self = this\n\n  assert(self.containerState, 'expected state')\n  assert(typeof self.containerState.size === 'number', 'expected size')\n\n  return factorySpace(\n    effects,\n    afterPrefix,\n    types.listItemIndent,\n    self.containerState.size + 1\n  )\n\n  /** @type {State} */\n  function afterPrefix(code) {\n    assert(self.containerState, 'expected state')\n    const tail = self.events[self.events.length - 1]\n    return tail &&\n      tail[1].type === types.listItemIndent &&\n      tail[2].sliceSerialize(tail[1], true).length === self.containerState.size\n      ? ok(code)\n      : nok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Exiter}\n */\nfunction tokenizeListEnd(effects) {\n  assert(this.containerState, 'expected state')\n  assert(typeof this.containerState.type === 'string', 'expected type')\n  effects.exit(this.containerState.type)\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeListItemPrefixWhitespace(effects, ok, nok) {\n  const self = this\n\n  // Always populated by defaults.\n  assert(\n    self.parser.constructs.disable.null,\n    'expected `disable.null` to be populated'\n  )\n\n  return factorySpace(\n    effects,\n    afterPrefix,\n    types.listItemPrefixWhitespace,\n    self.parser.constructs.disable.null.includes('codeIndented')\n      ? undefined\n      : constants.tabSize + 1\n  )\n\n  /** @type {State} */\n  function afterPrefix(code) {\n    const tail = self.events[self.events.length - 1]\n\n    return !markdownSpace(code) &&\n      tail &&\n      tail[1].type === types.listItemPrefixWhitespace\n      ? ok(code)\n      : nok(code)\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;CASC;;;AAKD;AAHA;AAGA;AADA;AAGA;AAFA;AACA;AAHA;;;;;;;AAOO,MAAM,OAAO;IAClB,cAAc;QAAC,UAAU;IAAwB;IACjD,MAAM;IACN,MAAM;IACN,UAAU;AACZ;AAEA,sBAAsB,GACtB,MAAM,oCAAoC;IACxC,SAAS;IACT,UAAU;AACZ;AAEA,sBAAsB,GACtB,MAAM,kBAAkB;IAAC,SAAS;IAAM,UAAU;AAAc;AAEhE,8EAA8E;AAC9E,YAAY;AAEZ;;;;CAIC,GACD,SAAS,kBAAkB,OAAO,EAAE,EAAE,EAAE,GAAG;IACzC,MAAM,OAAO,IAAI;IACjB,MAAM,OAAO,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC,MAAM,GAAG,EAAE;IAChD,IAAI,cACF,QAAQ,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,2JAAA,CAAA,QAAK,CAAC,UAAU,GACrC,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,MAAM,GAC5C;IACN,IAAI,OAAO;IAEX,OAAO;;IAEP,kBAAkB,GAClB,SAAS,MAAM,IAAI;QACjB,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,cAAc,EAAE;QAC5B,MAAM,OACJ,KAAK,cAAc,CAAC,IAAI,IACxB,CAAC,SAAS,2JAAA,CAAA,QAAK,CAAC,QAAQ,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,QAAQ,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,IAAI,GACtE,2JAAA,CAAA,QAAK,CAAC,aAAa,GACnB,2JAAA,CAAA,QAAK,CAAC,WAAW;QAEvB,IACE,SAAS,2JAAA,CAAA,QAAK,CAAC,aAAa,GACxB,CAAC,KAAK,cAAc,CAAC,MAAM,IAAI,SAAS,KAAK,cAAc,CAAC,MAAM,GAClE,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD,EAAE,OACf;YACA,IAAI,CAAC,KAAK,cAAc,CAAC,IAAI,EAAE;gBAC7B,KAAK,cAAc,CAAC,IAAI,GAAG;gBAC3B,QAAQ,KAAK,CAAC,MAAM;oBAAC,YAAY;gBAAI;YACvC;YAEA,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,aAAa,EAAE;gBAChC,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,cAAc;gBAClC,OAAO,SAAS,2JAAA,CAAA,QAAK,CAAC,QAAQ,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,IAAI,GACjD,QAAQ,KAAK,CAAC,kLAAA,CAAA,gBAAa,EAAE,KAAK,UAAU,QAC5C,SAAS;YACf;YAEA,IAAI,CAAC,KAAK,SAAS,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,MAAM,EAAE;gBAC5C,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,cAAc;gBAClC,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,aAAa;gBACjC,OAAO,OAAO;YAChB;QACF;QAEA,OAAO,IAAI;IACb;IAEA,kBAAkB,GAClB,SAAS,OAAO,IAAI;QAClB,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,cAAc,EAAE;QAC5B,IAAI,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD,EAAE,SAAS,EAAE,OAAO,+JAAA,CAAA,YAAS,CAAC,oBAAoB,EAAE;YAC/D,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,IACE,CAAC,CAAC,KAAK,SAAS,IAAI,OAAO,CAAC,KAC5B,CAAC,KAAK,cAAc,CAAC,MAAM,GACvB,SAAS,KAAK,cAAc,CAAC,MAAM,GACnC,SAAS,2JAAA,CAAA,QAAK,CAAC,gBAAgB,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,GACzD;YACA,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,aAAa;YAChC,OAAO,SAAS;QAClB;QAEA,OAAO,IAAI;IACb;IAEA;;IAEE,GACF,SAAS,SAAS,IAAI;QACpB,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,cAAc,EAAE;QAC5B,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,EAAE;QAC3B,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,cAAc;QAClC,QAAQ,OAAO,CAAC;QAChB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,cAAc;QACjC,KAAK,cAAc,CAAC,MAAM,GAAG,KAAK,cAAc,CAAC,MAAM,IAAI;QAC3D,OAAO,QAAQ,KAAK,CAClB,8KAAA,CAAA,YAAS,EACT,oCAAoC;QACpC,KAAK,SAAS,GAAG,MAAM,SACvB,QAAQ,OAAO,CACb,mCACA,aACA;IAGN;IAEA,kBAAkB,GAClB,SAAS,QAAQ,IAAI;QACnB,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,cAAc,EAAE;QAC5B,KAAK,cAAc,CAAC,gBAAgB,GAAG;QACvC;QACA,OAAO,YAAY;IACrB;IAEA,kBAAkB,GAClB,SAAS,YAAY,IAAI;QACvB,IAAI,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE,OAAO;YACvB,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,wBAAwB;YAC5C,QAAQ,OAAO,CAAC;YAChB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,wBAAwB;YAC3C,OAAO;QACT;QAEA,OAAO,IAAI;IACb;IAEA,kBAAkB,GAClB,SAAS,YAAY,IAAI;QACvB,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,cAAc,EAAE;QAC5B,KAAK,cAAc,CAAC,IAAI,GACtB,cACA,KAAK,cAAc,CAAC,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,cAAc,GAAG,MAAM,MAAM;QACtE,OAAO,GAAG;IACZ;AACF;AAEA;;;;CAIC,GACD,SAAS,yBAAyB,OAAO,EAAE,EAAE,EAAE,GAAG;IAChD,MAAM,OAAO,IAAI;IAEjB,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,cAAc,EAAE;IAC5B,KAAK,cAAc,CAAC,UAAU,GAAG;IAEjC,OAAO,QAAQ,KAAK,CAAC,8KAAA,CAAA,YAAS,EAAE,SAAS;;IAEzC,kBAAkB,GAClB,SAAS,QAAQ,IAAI;QACnB,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,cAAc,EAAE;QAC5B,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,OAAO,KAAK,cAAc,CAAC,IAAI,KAAK,UAAU;QACrD,KAAK,cAAc,CAAC,iBAAiB,GACnC,KAAK,cAAc,CAAC,iBAAiB,IACrC,KAAK,cAAc,CAAC,gBAAgB;QAEtC,wBAAwB;QACxB,gDAAgD;QAChD,OAAO,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EAChB,SACA,IACA,2JAAA,CAAA,QAAK,CAAC,cAAc,EACpB,KAAK,cAAc,CAAC,IAAI,GAAG,GAC3B;IACJ;IAEA,kBAAkB,GAClB,SAAS,SAAS,IAAI;QACpB,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,cAAc,EAAE;QAC5B,IAAI,KAAK,cAAc,CAAC,iBAAiB,IAAI,CAAC,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE,OAAO;YACjE,KAAK,cAAc,CAAC,iBAAiB,GAAG;YACxC,KAAK,cAAc,CAAC,gBAAgB,GAAG;YACvC,OAAO,iBAAiB;QAC1B;QAEA,KAAK,cAAc,CAAC,iBAAiB,GAAG;QACxC,KAAK,cAAc,CAAC,gBAAgB,GAAG;QACvC,OAAO,QAAQ,OAAO,CAAC,iBAAiB,IAAI,kBAAkB;IAChE;IAEA,kBAAkB,GAClB,SAAS,iBAAiB,IAAI;QAC5B,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,cAAc,EAAE;QAC5B,kEAAkE;QAClE,KAAK,cAAc,CAAC,UAAU,GAAG;QACjC,uDAAuD;QACvD,KAAK,SAAS,GAAG;QACjB,gCAAgC;QAChC,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EACH,KAAK,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,EACnC;QAEF,OAAO,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EAChB,SACA,QAAQ,OAAO,CAAC,MAAM,IAAI,MAC1B,2JAAA,CAAA,QAAK,CAAC,UAAU,EAChB,KAAK,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,kBACzC,YACA,+JAAA,CAAA,YAAS,CAAC,OAAO,EACrB;IACJ;AACF;AAEA;;;;CAIC,GACD,SAAS,eAAe,OAAO,EAAE,EAAE,EAAE,GAAG;IACtC,MAAM,OAAO,IAAI;IAEjB,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,cAAc,EAAE;IAC5B,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,OAAO,KAAK,cAAc,CAAC,IAAI,KAAK,UAAU;IAErD,OAAO,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EAChB,SACA,aACA,2JAAA,CAAA,QAAK,CAAC,cAAc,EACpB,KAAK,cAAc,CAAC,IAAI,GAAG;;IAG7B,kBAAkB,GAClB,SAAS,YAAY,IAAI;QACvB,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,KAAK,cAAc,EAAE;QAC5B,MAAM,OAAO,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC,MAAM,GAAG,EAAE;QAChD,OAAO,QACL,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,2JAAA,CAAA,QAAK,CAAC,cAAc,IACrC,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,MAAM,KAAK,KAAK,cAAc,CAAC,IAAI,GACvE,GAAG,QACH,IAAI;IACV;AACF;AAEA;;;;CAIC,GACD,SAAS,gBAAgB,OAAO;IAC9B,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,IAAI,CAAC,cAAc,EAAE;IAC5B,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,KAAK,UAAU;IACrD,QAAQ,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI;AACvC;AAEA;;;;CAIC,GACD,SAAS,iCAAiC,OAAO,EAAE,EAAE,EAAE,GAAG;IACxD,MAAM,OAAO,IAAI;IAEjB,gCAAgC;IAChC,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EACH,KAAK,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,EACnC;IAGF,OAAO,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EAChB,SACA,aACA,2JAAA,CAAA,QAAK,CAAC,wBAAwB,EAC9B,KAAK,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,kBACzC,YACA,+JAAA,CAAA,YAAS,CAAC,OAAO,GAAG;;IAG1B,kBAAkB,GAClB,SAAS,YAAY,IAAI;QACvB,MAAM,OAAO,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC,MAAM,GAAG,EAAE;QAEhD,OAAO,CAAC,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE,SACpB,QACA,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,2JAAA,CAAA,QAAK,CAAC,wBAAwB,GAC7C,GAAG,QACH,IAAI;IACV;AACF", "ignoreList": [0]}}, {"offset": {"line": 383, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 389, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/node_modules/micromark-core-commonmark/dev/lib/block-quote.js"], "sourcesContent": ["/**\n * @import {\n *   Construct,\n *   Exiter,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownSpace} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const blockQuote = {\n  continuation: {tokenize: tokenizeBlockQuoteContinuation},\n  exit,\n  name: 'blockQuote',\n  tokenize: tokenizeBlockQuoteStart\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeBlockQuoteStart(effects, ok, nok) {\n  const self = this\n\n  return start\n\n  /**\n   * Start of block quote.\n   *\n   * ```markdown\n   * > | > a\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if (code === codes.greaterThan) {\n      const state = self.containerState\n\n      assert(state, 'expected `containerState` to be defined in container')\n\n      if (!state.open) {\n        effects.enter(types.blockQuote, {_container: true})\n        state.open = true\n      }\n\n      effects.enter(types.blockQuotePrefix)\n      effects.enter(types.blockQuoteMarker)\n      effects.consume(code)\n      effects.exit(types.blockQuoteMarker)\n      return after\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `>`, before optional whitespace.\n   *\n   * ```markdown\n   * > | > a\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    if (markdownSpace(code)) {\n      effects.enter(types.blockQuotePrefixWhitespace)\n      effects.consume(code)\n      effects.exit(types.blockQuotePrefixWhitespace)\n      effects.exit(types.blockQuotePrefix)\n      return ok\n    }\n\n    effects.exit(types.blockQuotePrefix)\n    return ok(code)\n  }\n}\n\n/**\n * Start of block quote continuation.\n *\n * ```markdown\n *   | > a\n * > | > b\n *     ^\n * ```\n *\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeBlockQuoteContinuation(effects, ok, nok) {\n  const self = this\n\n  return contStart\n\n  /**\n   * Start of block quote continuation.\n   *\n   * Also used to parse the first block quote opening.\n   *\n   * ```markdown\n   *   | > a\n   * > | > b\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function contStart(code) {\n    if (markdownSpace(code)) {\n      // Always populated by defaults.\n      assert(\n        self.parser.constructs.disable.null,\n        'expected `disable.null` to be populated'\n      )\n\n      return factorySpace(\n        effects,\n        contBefore,\n        types.linePrefix,\n        self.parser.constructs.disable.null.includes('codeIndented')\n          ? undefined\n          : constants.tabSize\n      )(code)\n    }\n\n    return contBefore(code)\n  }\n\n  /**\n   * At `>`, after optional whitespace.\n   *\n   * Also used to parse the first block quote opening.\n   *\n   * ```markdown\n   *   | > a\n   * > | > b\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function contBefore(code) {\n    return effects.attempt(blockQuote, ok, nok)(code)\n  }\n}\n\n/** @type {Exiter} */\nfunction exit(effects) {\n  effects.exit(types.blockQuote)\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;AAKD;AAHA;AAGA;AADA;AADA;AAEA;;;;;AAGO,MAAM,aAAa;IACxB,cAAc;QAAC,UAAU;IAA8B;IACvD;IACA,MAAM;IACN,UAAU;AACZ;AAEA;;;;CAIC,GACD,SAAS,wBAAwB,OAAO,EAAE,EAAE,EAAE,GAAG;IAC/C,MAAM,OAAO,IAAI;IAEjB,OAAO;;IAEP;;;;;;;;;GASC,GACD,SAAS,MAAM,IAAI;QACjB,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,WAAW,EAAE;YAC9B,MAAM,QAAQ,KAAK,cAAc;YAEjC,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,OAAO;YAEd,IAAI,CAAC,MAAM,IAAI,EAAE;gBACf,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,UAAU,EAAE;oBAAC,YAAY;gBAAI;gBACjD,MAAM,IAAI,GAAG;YACf;YAEA,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,gBAAgB;YACpC,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,gBAAgB;YACpC,QAAQ,OAAO,CAAC;YAChB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,gBAAgB;YACnC,OAAO;QACT;QAEA,OAAO,IAAI;IACb;IAEA;;;;;;;;;GASC,GACD,SAAS,MAAM,IAAI;QACjB,IAAI,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE,OAAO;YACvB,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,0BAA0B;YAC9C,QAAQ,OAAO,CAAC;YAChB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,0BAA0B;YAC7C,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,gBAAgB;YACnC,OAAO;QACT;QAEA,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,gBAAgB;QACnC,OAAO,GAAG;IACZ;AACF;AAEA;;;;;;;;;;;;CAYC,GACD,SAAS,+BAA+B,OAAO,EAAE,EAAE,EAAE,GAAG;IACtD,MAAM,OAAO,IAAI;IAEjB,OAAO;;IAEP;;;;;;;;;;;;GAYC,GACD,SAAS,UAAU,IAAI;QACrB,IAAI,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE,OAAO;YACvB,gCAAgC;YAChC,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EACH,KAAK,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,EACnC;YAGF,OAAO,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EAChB,SACA,YACA,2JAAA,CAAA,QAAK,CAAC,UAAU,EAChB,KAAK,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,kBACzC,YACA,+JAAA,CAAA,YAAS,CAAC,OAAO,EACrB;QACJ;QAEA,OAAO,WAAW;IACpB;IAEA;;;;;;;;;;;;GAYC,GACD,SAAS,WAAW,IAAI;QACtB,OAAO,QAAQ,OAAO,CAAC,YAAY,IAAI,KAAK;IAC9C;AACF;AAEA,mBAAmB,GACnB,SAAS,KAAK,OAAO;IACnB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,UAAU;AAC/B", "ignoreList": [0]}}, {"offset": {"line": 529, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 535, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/node_modules/micromark-core-commonmark/dev/lib/definition.js"], "sourcesContent": ["/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factoryDestination} from 'micromark-factory-destination'\nimport {factoryLabel} from 'micromark-factory-label'\nimport {factorySpace} from 'micromark-factory-space'\nimport {factoryTitle} from 'micromark-factory-title'\nimport {factoryWhitespace} from 'micromark-factory-whitespace'\nimport {\n  markdownLineEndingOrSpace,\n  markdownLineEnding,\n  markdownSpace\n} from 'micromark-util-character'\nimport {normalizeIdentifier} from 'micromark-util-normalize-identifier'\nimport {codes, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const definition = {name: 'definition', tokenize: tokenizeDefinition}\n\n/** @type {Construct} */\nconst titleBefore = {partial: true, tokenize: tokenizeTitleBefore}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeDefinition(effects, ok, nok) {\n  const self = this\n  /** @type {string} */\n  let identifier\n\n  return start\n\n  /**\n   * At start of a definition.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // Do not interrupt paragraphs (but do follow definitions).\n    // To do: do `interrupt` the way `markdown-rs` does.\n    // To do: parse whitespace the way `markdown-rs` does.\n    effects.enter(types.definition)\n    return before(code)\n  }\n\n  /**\n   * After optional whitespace, at `[`.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function before(code) {\n    // To do: parse whitespace the way `markdown-rs` does.\n    assert(code === codes.leftSquareBracket, 'expected `[`')\n    return factoryLabel.call(\n      self,\n      effects,\n      labelAfter,\n      // Note: we don’t need to reset the way `markdown-rs` does.\n      nok,\n      types.definitionLabel,\n      types.definitionLabelMarker,\n      types.definitionLabelString\n    )(code)\n  }\n\n  /**\n   * After label.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelAfter(code) {\n    identifier = normalizeIdentifier(\n      self.sliceSerialize(self.events[self.events.length - 1][1]).slice(1, -1)\n    )\n\n    if (code === codes.colon) {\n      effects.enter(types.definitionMarker)\n      effects.consume(code)\n      effects.exit(types.definitionMarker)\n      return markerAfter\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After marker.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function markerAfter(code) {\n    // Note: whitespace is optional.\n    return markdownLineEndingOrSpace(code)\n      ? factoryWhitespace(effects, destinationBefore)(code)\n      : destinationBefore(code)\n  }\n\n  /**\n   * Before destination.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function destinationBefore(code) {\n    return factoryDestination(\n      effects,\n      destinationAfter,\n      // Note: we don’t need to reset the way `markdown-rs` does.\n      nok,\n      types.definitionDestination,\n      types.definitionDestinationLiteral,\n      types.definitionDestinationLiteralMarker,\n      types.definitionDestinationRaw,\n      types.definitionDestinationString\n    )(code)\n  }\n\n  /**\n   * After destination.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function destinationAfter(code) {\n    return effects.attempt(titleBefore, after, after)(code)\n  }\n\n  /**\n   * After definition.\n   *\n   * ```markdown\n   * > | [a]: b\n   *           ^\n   * > | [a]: b \"c\"\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    return markdownSpace(code)\n      ? factorySpace(effects, afterWhitespace, types.whitespace)(code)\n      : afterWhitespace(code)\n  }\n\n  /**\n   * After definition, after optional whitespace.\n   *\n   * ```markdown\n   * > | [a]: b\n   *           ^\n   * > | [a]: b \"c\"\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function afterWhitespace(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.definition)\n\n      // Note: we don’t care about uniqueness.\n      // It’s likely that that doesn’t happen very frequently.\n      // It is more likely that it wastes precious time.\n      self.parser.defined.push(identifier)\n\n      // To do: `markdown-rs` interrupt.\n      // // You’d be interrupting.\n      // tokenizer.interrupt = true\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeTitleBefore(effects, ok, nok) {\n  return titleBefore\n\n  /**\n   * After destination, at whitespace.\n   *\n   * ```markdown\n   * > | [a]: b\n   *           ^\n   * > | [a]: b \"c\"\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function titleBefore(code) {\n    return markdownLineEndingOrSpace(code)\n      ? factoryWhitespace(effects, beforeMarker)(code)\n      : nok(code)\n  }\n\n  /**\n   * At title.\n   *\n   * ```markdown\n   *   | [a]: b\n   * > | \"c\"\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function beforeMarker(code) {\n    return factoryTitle(\n      effects,\n      titleAfter,\n      nok,\n      types.definitionTitle,\n      types.definitionTitleMarker,\n      types.definitionTitleString\n    )(code)\n  }\n\n  /**\n   * After title.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function titleAfter(code) {\n    return markdownSpace(code)\n      ? factorySpace(\n          effects,\n          titleAfterOptionalWhitespace,\n          types.whitespace\n        )(code)\n      : titleAfterOptionalWhitespace(code)\n  }\n\n  /**\n   * After title, after optional whitespace.\n   *\n   * ```markdown\n   * > | [a]: b \"c\"\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function titleAfterOptionalWhitespace(code) {\n    return code === codes.eof || markdownLineEnding(code) ? ok(code) : nok(code)\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AAcD;AAZA;AAYA;AAVA;AASA;AALA;AADA;AAJA;AAEA;AACA;;;;;;;;;;AAWO,MAAM,aAAa;IAAC,MAAM;IAAc,UAAU;AAAkB;AAE3E,sBAAsB,GACtB,MAAM,cAAc;IAAC,SAAS;IAAM,UAAU;AAAmB;AAEjE;;;;CAIC,GACD,SAAS,mBAAmB,OAAO,EAAE,EAAE,EAAE,GAAG;IAC1C,MAAM,OAAO,IAAI;IACjB,mBAAmB,GACnB,IAAI;IAEJ,OAAO;;IAEP;;;;;;;;;GASC,GACD,SAAS,MAAM,IAAI;QACjB,2DAA2D;QAC3D,oDAAoD;QACpD,sDAAsD;QACtD,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,UAAU;QAC9B,OAAO,OAAO;IAChB;IAEA;;;;;;;;;GASC,GACD,SAAS,OAAO,IAAI;QAClB,sDAAsD;QACtD,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,SAAS,2JAAA,CAAA,QAAK,CAAC,iBAAiB,EAAE;QACzC,OAAO,6JAAA,CAAA,eAAY,CAAC,IAAI,CACtB,MACA,SACA,YACA,2DAA2D;QAC3D,KACA,2JAAA,CAAA,QAAK,CAAC,eAAe,EACrB,2JAAA,CAAA,QAAK,CAAC,qBAAqB,EAC3B,2JAAA,CAAA,QAAK,CAAC,qBAAqB,EAC3B;IACJ;IAEA;;;;;;;;;GASC,GACD,SAAS,WAAW,IAAI;QACtB,aAAa,CAAA,GAAA,4KAAA,CAAA,sBAAmB,AAAD,EAC7B,KAAK,cAAc,CAAC,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC,MAAM,GAAG,EAAE,CAAC,EAAE,EAAE,KAAK,CAAC,GAAG,CAAC;QAGxE,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,KAAK,EAAE;YACxB,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,gBAAgB;YACpC,QAAQ,OAAO,CAAC;YAChB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,gBAAgB;YACnC,OAAO;QACT;QAEA,OAAO,IAAI;IACb;IAEA;;;;;;;;;GASC,GACD,SAAS,YAAY,IAAI;QACvB,gCAAgC;QAChC,OAAO,CAAA,GAAA,8JAAA,CAAA,4BAAyB,AAAD,EAAE,QAC7B,CAAA,GAAA,kKAAA,CAAA,oBAAiB,AAAD,EAAE,SAAS,mBAAmB,QAC9C,kBAAkB;IACxB;IAEA;;;;;;;;;GASC,GACD,SAAS,kBAAkB,IAAI;QAC7B,OAAO,CAAA,GAAA,mKAAA,CAAA,qBAAkB,AAAD,EACtB,SACA,kBACA,2DAA2D;QAC3D,KACA,2JAAA,CAAA,QAAK,CAAC,qBAAqB,EAC3B,2JAAA,CAAA,QAAK,CAAC,4BAA4B,EAClC,2JAAA,CAAA,QAAK,CAAC,kCAAkC,EACxC,2JAAA,CAAA,QAAK,CAAC,wBAAwB,EAC9B,2JAAA,CAAA,QAAK,CAAC,2BAA2B,EACjC;IACJ;IAEA;;;;;;;;;GASC,GACD,SAAS,iBAAiB,IAAI;QAC5B,OAAO,QAAQ,OAAO,CAAC,aAAa,OAAO,OAAO;IACpD;IAEA;;;;;;;;;;;GAWC,GACD,SAAS,MAAM,IAAI;QACjB,OAAO,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE,QACjB,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EAAE,SAAS,iBAAiB,2JAAA,CAAA,QAAK,CAAC,UAAU,EAAE,QACzD,gBAAgB;IACtB;IAEA;;;;;;;;;;;GAWC,GACD,SAAS,gBAAgB,IAAI;QAC3B,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,IAAI,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAClD,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,UAAU;YAE7B,wCAAwC;YACxC,wDAAwD;YACxD,kDAAkD;YAClD,KAAK,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC;YAEzB,kCAAkC;YAClC,4BAA4B;YAC5B,6BAA6B;YAC7B,OAAO,GAAG;QACZ;QAEA,OAAO,IAAI;IACb;AACF;AAEA;;;;CAIC,GACD,SAAS,oBAAoB,OAAO,EAAE,EAAE,EAAE,GAAG;IAC3C,OAAO;;IAEP;;;;;;;;;;;GAWC,GACD,SAAS,YAAY,IAAI;QACvB,OAAO,CAAA,GAAA,8JAAA,CAAA,4BAAyB,AAAD,EAAE,QAC7B,CAAA,GAAA,kKAAA,CAAA,oBAAiB,AAAD,EAAE,SAAS,cAAc,QACzC,IAAI;IACV;IAEA;;;;;;;;;;GAUC,GACD,SAAS,aAAa,IAAI;QACxB,OAAO,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EAChB,SACA,YACA,KACA,2JAAA,CAAA,QAAK,CAAC,eAAe,EACrB,2JAAA,CAAA,QAAK,CAAC,qBAAqB,EAC3B,2JAAA,CAAA,QAAK,CAAC,qBAAqB,EAC3B;IACJ;IAEA;;;;;;;;;GASC,GACD,SAAS,WAAW,IAAI;QACtB,OAAO,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE,QACjB,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EACT,SACA,8BACA,2JAAA,CAAA,QAAK,CAAC,UAAU,EAChB,QACF,6BAA6B;IACnC;IAEA;;;;;;;;;GASC,GACD,SAAS,6BAA6B,IAAI;QACxC,OAAO,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,IAAI,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,QAAQ,GAAG,QAAQ,IAAI;IACzE;AACF", "ignoreList": [0]}}, {"offset": {"line": 768, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 774, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/node_modules/micromark-core-commonmark/dev/lib/code-indented.js"], "sourcesContent": ["/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding, markdownSpace} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const codeIndented = {\n  name: 'codeIndented',\n  tokenize: tokenizeCodeIndented\n}\n\n/** @type {Construct} */\nconst furtherStart = {partial: true, tokenize: tokenizeFurtherStart}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeCodeIndented(effects, ok, nok) {\n  const self = this\n  return start\n\n  /**\n   * Start of code (indented).\n   *\n   * > **Parsing note**: it is not needed to check if this first line is a\n   * > filled line (that it has a non-whitespace character), because blank lines\n   * > are parsed already, so we never run into that.\n   *\n   * ```markdown\n   * > |     aaa\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // To do: manually check if interrupting like `markdown-rs`.\n    assert(markdownSpace(code))\n    effects.enter(types.codeIndented)\n    // To do: use an improved `space_or_tab` function like `markdown-rs`,\n    // so that we can drop the next state.\n    return factorySpace(\n      effects,\n      afterPrefix,\n      types.linePrefix,\n      constants.tabSize + 1\n    )(code)\n  }\n\n  /**\n   * At start, after 1 or 4 spaces.\n   *\n   * ```markdown\n   * > |     aaa\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function afterPrefix(code) {\n    const tail = self.events[self.events.length - 1]\n    return tail &&\n      tail[1].type === types.linePrefix &&\n      tail[2].sliceSerialize(tail[1], true).length >= constants.tabSize\n      ? atBreak(code)\n      : nok(code)\n  }\n\n  /**\n   * At a break.\n   *\n   * ```markdown\n   * > |     aaa\n   *         ^  ^\n   * ```\n   *\n   * @type {State}\n   */\n  function atBreak(code) {\n    if (code === codes.eof) {\n      return after(code)\n    }\n\n    if (markdownLineEnding(code)) {\n      return effects.attempt(furtherStart, atBreak, after)(code)\n    }\n\n    effects.enter(types.codeFlowValue)\n    return inside(code)\n  }\n\n  /**\n   * In code content.\n   *\n   * ```markdown\n   * > |     aaa\n   *         ^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.codeFlowValue)\n      return atBreak(code)\n    }\n\n    effects.consume(code)\n    return inside\n  }\n\n  /** @type {State} */\n  function after(code) {\n    effects.exit(types.codeIndented)\n    // To do: allow interrupting like `markdown-rs`.\n    // Feel free to interrupt.\n    // tokenizer.interrupt = false\n    return ok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeFurtherStart(effects, ok, nok) {\n  const self = this\n\n  return furtherStart\n\n  /**\n   * At eol, trying to parse another indent.\n   *\n   * ```markdown\n   * > |     aaa\n   *            ^\n   *   |     bbb\n   * ```\n   *\n   * @type {State}\n   */\n  function furtherStart(code) {\n    // To do: improve `lazy` / `pierce` handling.\n    // If this is a lazy line, it can’t be code.\n    if (self.parser.lazy[self.now().line]) {\n      return nok(code)\n    }\n\n    if (markdownLineEnding(code)) {\n      effects.enter(types.lineEnding)\n      effects.consume(code)\n      effects.exit(types.lineEnding)\n      return furtherStart\n    }\n\n    // To do: the code here in `micromark-js` is a bit different from\n    // `markdown-rs` because there it can attempt spaces.\n    // We can’t yet.\n    //\n    // To do: use an improved `space_or_tab` function like `markdown-rs`,\n    // so that we can drop the next state.\n    return factorySpace(\n      effects,\n      afterPrefix,\n      types.linePrefix,\n      constants.tabSize + 1\n    )(code)\n  }\n\n  /**\n   * At start, after 1 or 4 spaces.\n   *\n   * ```markdown\n   * > |     aaa\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function afterPrefix(code) {\n    const tail = self.events[self.events.length - 1]\n    return tail &&\n      tail[1].type === types.linePrefix &&\n      tail[2].sliceSerialize(tail[1], true).length >= constants.tabSize\n      ? ok(code)\n      : markdownLineEnding(code)\n        ? furtherStart(code)\n        : nok(code)\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AAED;AAEA;AACA;AAFA;AAEA;AAAA;;;;;AAGO,MAAM,eAAe;IAC1B,MAAM;IACN,UAAU;AACZ;AAEA,sBAAsB,GACtB,MAAM,eAAe;IAAC,SAAS;IAAM,UAAU;AAAoB;AAEnE;;;;CAIC,GACD,SAAS,qBAAqB,OAAO,EAAE,EAAE,EAAE,GAAG;IAC5C,MAAM,OAAO,IAAI;IACjB,OAAO;;IAEP;;;;;;;;;;;;;GAaC,GACD,SAAS,MAAM,IAAI;QACjB,4DAA4D;QAC5D,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE;QACrB,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,YAAY;QAChC,qEAAqE;QACrE,sCAAsC;QACtC,OAAO,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EAChB,SACA,aACA,2JAAA,CAAA,QAAK,CAAC,UAAU,EAChB,+JAAA,CAAA,YAAS,CAAC,OAAO,GAAG,GACpB;IACJ;IAEA;;;;;;;;;GASC,GACD,SAAS,YAAY,IAAI;QACvB,MAAM,OAAO,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC,MAAM,GAAG,EAAE;QAChD,OAAO,QACL,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,2JAAA,CAAA,QAAK,CAAC,UAAU,IACjC,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,MAAM,IAAI,+JAAA,CAAA,YAAS,CAAC,OAAO,GAC/D,QAAQ,QACR,IAAI;IACV;IAEA;;;;;;;;;GASC,GACD,SAAS,QAAQ,IAAI;QACnB,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,EAAE;YACtB,OAAO,MAAM;QACf;QAEA,IAAI,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAC5B,OAAO,QAAQ,OAAO,CAAC,cAAc,SAAS,OAAO;QACvD;QAEA,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,aAAa;QACjC,OAAO,OAAO;IAChB;IAEA;;;;;;;;;GASC,GACD,SAAS,OAAO,IAAI;QAClB,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,IAAI,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAClD,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,aAAa;YAChC,OAAO,QAAQ;QACjB;QAEA,QAAQ,OAAO,CAAC;QAChB,OAAO;IACT;IAEA,kBAAkB,GAClB,SAAS,MAAM,IAAI;QACjB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,YAAY;QAC/B,gDAAgD;QAChD,0BAA0B;QAC1B,8BAA8B;QAC9B,OAAO,GAAG;IACZ;AACF;AAEA;;;;CAIC,GACD,SAAS,qBAAqB,OAAO,EAAE,EAAE,EAAE,GAAG;IAC5C,MAAM,OAAO,IAAI;IAEjB,OAAO;;IAEP;;;;;;;;;;GAUC,GACD,SAAS,aAAa,IAAI;QACxB,6CAA6C;QAC7C,4CAA4C;QAC5C,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,EAAE;YACrC,OAAO,IAAI;QACb;QAEA,IAAI,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAC5B,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,UAAU;YAC9B,QAAQ,OAAO,CAAC;YAChB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,UAAU;YAC7B,OAAO;QACT;QAEA,iEAAiE;QACjE,qDAAqD;QACrD,gBAAgB;QAChB,EAAE;QACF,qEAAqE;QACrE,sCAAsC;QACtC,OAAO,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EAChB,SACA,aACA,2JAAA,CAAA,QAAK,CAAC,UAAU,EAChB,+JAAA,CAAA,YAAS,CAAC,OAAO,GAAG,GACpB;IACJ;IAEA;;;;;;;;;GASC,GACD,SAAS,YAAY,IAAI;QACvB,MAAM,OAAO,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC,MAAM,GAAG,EAAE;QAChD,OAAO,QACL,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,2JAAA,CAAA,QAAK,CAAC,UAAU,IACjC,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,MAAM,IAAI,+JAAA,CAAA,YAAS,CAAC,OAAO,GAC/D,GAAG,QACH,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,QACjB,aAAa,QACb,IAAI;IACZ;AACF", "ignoreList": [0]}}, {"offset": {"line": 940, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 946, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/node_modules/micromark-core-commonmark/dev/lib/heading-atx.js"], "sourcesContent": ["/**\n * @import {\n *   Construct,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer,\n *   Token\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {\n  markdownLineEndingOrSpace,\n  markdownLineEnding,\n  markdownSpace\n} from 'micromark-util-character'\nimport {splice} from 'micromark-util-chunked'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const headingAtx = {\n  name: 'headingAtx',\n  resolve: resolveHeadingAtx,\n  tokenize: tokenizeHeadingAtx\n}\n\n/** @type {Resolver} */\nfunction resolveHeadingAtx(events, context) {\n  let contentEnd = events.length - 2\n  let contentStart = 3\n  /** @type {Token} */\n  let content\n  /** @type {Token} */\n  let text\n\n  // Prefix whitespace, part of the opening.\n  if (events[contentStart][1].type === types.whitespace) {\n    contentStart += 2\n  }\n\n  // Suffix whitespace, part of the closing.\n  if (\n    contentEnd - 2 > contentStart &&\n    events[contentEnd][1].type === types.whitespace\n  ) {\n    contentEnd -= 2\n  }\n\n  if (\n    events[contentEnd][1].type === types.atxHeadingSequence &&\n    (contentStart === contentEnd - 1 ||\n      (contentEnd - 4 > contentStart &&\n        events[contentEnd - 2][1].type === types.whitespace))\n  ) {\n    contentEnd -= contentStart + 1 === contentEnd ? 2 : 4\n  }\n\n  if (contentEnd > contentStart) {\n    content = {\n      type: types.atxHeadingText,\n      start: events[contentStart][1].start,\n      end: events[contentEnd][1].end\n    }\n    text = {\n      type: types.chunkText,\n      start: events[contentStart][1].start,\n      end: events[contentEnd][1].end,\n      contentType: constants.contentTypeText\n    }\n\n    splice(events, contentStart, contentEnd - contentStart + 1, [\n      ['enter', content, context],\n      ['enter', text, context],\n      ['exit', text, context],\n      ['exit', content, context]\n    ])\n  }\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeHeadingAtx(effects, ok, nok) {\n  let size = 0\n\n  return start\n\n  /**\n   * Start of a heading (atx).\n   *\n   * ```markdown\n   * > | ## aa\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // To do: parse indent like `markdown-rs`.\n    effects.enter(types.atxHeading)\n    return before(code)\n  }\n\n  /**\n   * After optional whitespace, at `#`.\n   *\n   * ```markdown\n   * > | ## aa\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function before(code) {\n    assert(code === codes.numberSign, 'expected `#`')\n    effects.enter(types.atxHeadingSequence)\n    return sequenceOpen(code)\n  }\n\n  /**\n   * In opening sequence.\n   *\n   * ```markdown\n   * > | ## aa\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceOpen(code) {\n    if (\n      code === codes.numberSign &&\n      size++ < constants.atxHeadingOpeningFenceSizeMax\n    ) {\n      effects.consume(code)\n      return sequenceOpen\n    }\n\n    // Always at least one `#`.\n    if (code === codes.eof || markdownLineEndingOrSpace(code)) {\n      effects.exit(types.atxHeadingSequence)\n      return atBreak(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After something, before something else.\n   *\n   * ```markdown\n   * > | ## aa\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function atBreak(code) {\n    if (code === codes.numberSign) {\n      effects.enter(types.atxHeadingSequence)\n      return sequenceFurther(code)\n    }\n\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.atxHeading)\n      // To do: interrupt like `markdown-rs`.\n      // // Feel free to interrupt.\n      // tokenizer.interrupt = false\n      return ok(code)\n    }\n\n    if (markdownSpace(code)) {\n      return factorySpace(effects, atBreak, types.whitespace)(code)\n    }\n\n    // To do: generate `data` tokens, add the `text` token later.\n    // Needs edit map, see: `markdown.rs`.\n    effects.enter(types.atxHeadingText)\n    return data(code)\n  }\n\n  /**\n   * In further sequence (after whitespace).\n   *\n   * Could be normal “visible” hashes in the heading or a final sequence.\n   *\n   * ```markdown\n   * > | ## aa ##\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceFurther(code) {\n    if (code === codes.numberSign) {\n      effects.consume(code)\n      return sequenceFurther\n    }\n\n    effects.exit(types.atxHeadingSequence)\n    return atBreak(code)\n  }\n\n  /**\n   * In text.\n   *\n   * ```markdown\n   * > | ## aa\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function data(code) {\n    if (\n      code === codes.eof ||\n      code === codes.numberSign ||\n      markdownLineEndingOrSpace(code)\n    ) {\n      effects.exit(types.atxHeadingText)\n      return atBreak(code)\n    }\n\n    effects.consume(code)\n    return data\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;CASC;;;AAUD;AAAA;AADA;AAPA;AAQA;AANA;AADA;;;;;;AAUO,MAAM,aAAa;IACxB,MAAM;IACN,SAAS;IACT,UAAU;AACZ;AAEA,qBAAqB,GACrB,SAAS,kBAAkB,MAAM,EAAE,OAAO;IACxC,IAAI,aAAa,OAAO,MAAM,GAAG;IACjC,IAAI,eAAe;IACnB,kBAAkB,GAClB,IAAI;IACJ,kBAAkB,GAClB,IAAI;IAEJ,0CAA0C;IAC1C,IAAI,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC,IAAI,KAAK,2JAAA,CAAA,QAAK,CAAC,UAAU,EAAE;QACrD,gBAAgB;IAClB;IAEA,0CAA0C;IAC1C,IACE,aAAa,IAAI,gBACjB,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,IAAI,KAAK,2JAAA,CAAA,QAAK,CAAC,UAAU,EAC/C;QACA,cAAc;IAChB;IAEA,IACE,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,IAAI,KAAK,2JAAA,CAAA,QAAK,CAAC,kBAAkB,IACvD,CAAC,iBAAiB,aAAa,KAC5B,aAAa,IAAI,gBAChB,MAAM,CAAC,aAAa,EAAE,CAAC,EAAE,CAAC,IAAI,KAAK,2JAAA,CAAA,QAAK,CAAC,UAAU,AAAC,GACxD;QACA,cAAc,eAAe,MAAM,aAAa,IAAI;IACtD;IAEA,IAAI,aAAa,cAAc;QAC7B,UAAU;YACR,MAAM,2JAAA,CAAA,QAAK,CAAC,cAAc;YAC1B,OAAO,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC,KAAK;YACpC,KAAK,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,GAAG;QAChC;QACA,OAAO;YACL,MAAM,2JAAA,CAAA,QAAK,CAAC,SAAS;YACrB,OAAO,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC,KAAK;YACpC,KAAK,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,GAAG;YAC9B,aAAa,+JAAA,CAAA,YAAS,CAAC,eAAe;QACxC;QAEA,CAAA,GAAA,4JAAA,CAAA,SAAM,AAAD,EAAE,QAAQ,cAAc,aAAa,eAAe,GAAG;YAC1D;gBAAC;gBAAS;gBAAS;aAAQ;YAC3B;gBAAC;gBAAS;gBAAM;aAAQ;YACxB;gBAAC;gBAAQ;gBAAM;aAAQ;YACvB;gBAAC;gBAAQ;gBAAS;aAAQ;SAC3B;IACH;IAEA,OAAO;AACT;AAEA;;;;CAIC,GACD,SAAS,mBAAmB,OAAO,EAAE,EAAE,EAAE,GAAG;IAC1C,IAAI,OAAO;IAEX,OAAO;;IAEP;;;;;;;;;GASC,GACD,SAAS,MAAM,IAAI;QACjB,0CAA0C;QAC1C,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,UAAU;QAC9B,OAAO,OAAO;IAChB;IAEA;;;;;;;;;GASC,GACD,SAAS,OAAO,IAAI;QAClB,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,SAAS,2JAAA,CAAA,QAAK,CAAC,UAAU,EAAE;QAClC,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,kBAAkB;QACtC,OAAO,aAAa;IACtB;IAEA;;;;;;;;;GASC,GACD,SAAS,aAAa,IAAI;QACxB,IACE,SAAS,2JAAA,CAAA,QAAK,CAAC,UAAU,IACzB,SAAS,+JAAA,CAAA,YAAS,CAAC,6BAA6B,EAChD;YACA,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,2BAA2B;QAC3B,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,IAAI,CAAA,GAAA,8JAAA,CAAA,4BAAyB,AAAD,EAAE,OAAO;YACzD,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,kBAAkB;YACrC,OAAO,QAAQ;QACjB;QAEA,OAAO,IAAI;IACb;IAEA;;;;;;;;;GASC,GACD,SAAS,QAAQ,IAAI;QACnB,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,UAAU,EAAE;YAC7B,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,kBAAkB;YACtC,OAAO,gBAAgB;QACzB;QAEA,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,IAAI,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAClD,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,UAAU;YAC7B,uCAAuC;YACvC,6BAA6B;YAC7B,8BAA8B;YAC9B,OAAO,GAAG;QACZ;QAEA,IAAI,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE,OAAO;YACvB,OAAO,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EAAE,SAAS,SAAS,2JAAA,CAAA,QAAK,CAAC,UAAU,EAAE;QAC1D;QAEA,6DAA6D;QAC7D,sCAAsC;QACtC,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,cAAc;QAClC,OAAO,KAAK;IACd;IAEA;;;;;;;;;;;GAWC,GACD,SAAS,gBAAgB,IAAI;QAC3B,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,UAAU,EAAE;YAC7B,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,kBAAkB;QACrC,OAAO,QAAQ;IACjB;IAEA;;;;;;;;;GASC,GACD,SAAS,KAAK,IAAI;QAChB,IACE,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,IAClB,SAAS,2JAAA,CAAA,QAAK,CAAC,UAAU,IACzB,CAAA,GAAA,8JAAA,CAAA,4BAAyB,AAAD,EAAE,OAC1B;YACA,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,cAAc;YACjC,OAAO,QAAQ;QACjB;QAEA,QAAQ,OAAO,CAAC;QAChB,OAAO;IACT;AACF", "ignoreList": [0]}}, {"offset": {"line": 1151, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1157, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/node_modules/micromark-core-commonmark/dev/lib/setext-underline.js"], "sourcesContent": ["/**\n * @import {\n *   Code,\n *   Construct,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding, markdownSpace} from 'micromark-util-character'\nimport {codes, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const setextUnderline = {\n  name: 'setextUnderline',\n  resolveTo: resolveToSetextUnderline,\n  tokenize: tokenizeSetextUnderline\n}\n\n/** @type {Resolver} */\nfunction resolveToSetextUnderline(events, context) {\n  // To do: resolve like `markdown-rs`.\n  let index = events.length\n  /** @type {number | undefined} */\n  let content\n  /** @type {number | undefined} */\n  let text\n  /** @type {number | undefined} */\n  let definition\n\n  // Find the opening of the content.\n  // It’ll always exist: we don’t tokenize if it isn’t there.\n  while (index--) {\n    if (events[index][0] === 'enter') {\n      if (events[index][1].type === types.content) {\n        content = index\n        break\n      }\n\n      if (events[index][1].type === types.paragraph) {\n        text = index\n      }\n    }\n    // Exit\n    else {\n      if (events[index][1].type === types.content) {\n        // Remove the content end (if needed we’ll add it later)\n        events.splice(index, 1)\n      }\n\n      if (!definition && events[index][1].type === types.definition) {\n        definition = index\n      }\n    }\n  }\n\n  assert(text !== undefined, 'expected a `text` index to be found')\n  assert(content !== undefined, 'expected a `text` index to be found')\n  assert(events[content][2] === context, 'enter context should be same')\n  assert(\n    events[events.length - 1][2] === context,\n    'enter context should be same'\n  )\n  const heading = {\n    type: types.setextHeading,\n    start: {...events[content][1].start},\n    end: {...events[events.length - 1][1].end}\n  }\n\n  // Change the paragraph to setext heading text.\n  events[text][1].type = types.setextHeadingText\n\n  // If we have definitions in the content, we’ll keep on having content,\n  // but we need move it.\n  if (definition) {\n    events.splice(text, 0, ['enter', heading, context])\n    events.splice(definition + 1, 0, ['exit', events[content][1], context])\n    events[content][1].end = {...events[definition][1].end}\n  } else {\n    events[content][1] = heading\n  }\n\n  // Add the heading exit at the end.\n  events.push(['exit', heading, context])\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeSetextUnderline(effects, ok, nok) {\n  const self = this\n  /** @type {NonNullable<Code>} */\n  let marker\n\n  return start\n\n  /**\n   * At start of heading (setext) underline.\n   *\n   * ```markdown\n   *   | aa\n   * > | ==\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    let index = self.events.length\n    /** @type {boolean | undefined} */\n    let paragraph\n\n    assert(\n      code === codes.dash || code === codes.equalsTo,\n      'expected `=` or `-`'\n    )\n\n    // Find an opening.\n    while (index--) {\n      // Skip enter/exit of line ending, line prefix, and content.\n      // We can now either have a definition or a paragraph.\n      if (\n        self.events[index][1].type !== types.lineEnding &&\n        self.events[index][1].type !== types.linePrefix &&\n        self.events[index][1].type !== types.content\n      ) {\n        paragraph = self.events[index][1].type === types.paragraph\n        break\n      }\n    }\n\n    // To do: handle lazy/pierce like `markdown-rs`.\n    // To do: parse indent like `markdown-rs`.\n    if (!self.parser.lazy[self.now().line] && (self.interrupt || paragraph)) {\n      effects.enter(types.setextHeadingLine)\n      marker = code\n      return before(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After optional whitespace, at `-` or `=`.\n   *\n   * ```markdown\n   *   | aa\n   * > | ==\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function before(code) {\n    effects.enter(types.setextHeadingLineSequence)\n    return inside(code)\n  }\n\n  /**\n   * In sequence.\n   *\n   * ```markdown\n   *   | aa\n   * > | ==\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    if (code === marker) {\n      effects.consume(code)\n      return inside\n    }\n\n    effects.exit(types.setextHeadingLineSequence)\n\n    return markdownSpace(code)\n      ? factorySpace(effects, after, types.lineSuffix)(code)\n      : after(code)\n  }\n\n  /**\n   * After sequence, after optional whitespace.\n   *\n   * ```markdown\n   *   | aa\n   * > | ==\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.setextHeadingLine)\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;CASC;;;AAKD;AAHA;AAGA;AADA;AADA;;;;;AAKO,MAAM,kBAAkB;IAC7B,MAAM;IACN,WAAW;IACX,UAAU;AACZ;AAEA,qBAAqB,GACrB,SAAS,yBAAyB,MAAM,EAAE,OAAO;IAC/C,qCAAqC;IACrC,IAAI,QAAQ,OAAO,MAAM;IACzB,+BAA+B,GAC/B,IAAI;IACJ,+BAA+B,GAC/B,IAAI;IACJ,+BAA+B,GAC/B,IAAI;IAEJ,mCAAmC;IACnC,2DAA2D;IAC3D,MAAO,QAAS;QACd,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,KAAK,SAAS;YAChC,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,2JAAA,CAAA,QAAK,CAAC,OAAO,EAAE;gBAC3C,UAAU;gBACV;YACF;YAEA,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,2JAAA,CAAA,QAAK,CAAC,SAAS,EAAE;gBAC7C,OAAO;YACT;QACF,OAEK;YACH,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,2JAAA,CAAA,QAAK,CAAC,OAAO,EAAE;gBAC3C,wDAAwD;gBACxD,OAAO,MAAM,CAAC,OAAO;YACvB;YAEA,IAAI,CAAC,cAAc,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,2JAAA,CAAA,QAAK,CAAC,UAAU,EAAE;gBAC7D,aAAa;YACf;QACF;IACF;IAEA,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,SAAS,WAAW;IAC3B,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,YAAY,WAAW;IAC9B,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,MAAM,CAAC,QAAQ,CAAC,EAAE,KAAK,SAAS;IACvC,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EACH,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,CAAC,EAAE,KAAK,SACjC;IAEF,MAAM,UAAU;QACd,MAAM,2JAAA,CAAA,QAAK,CAAC,aAAa;QACzB,OAAO;YAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,KAAK;QAAA;QACnC,KAAK;YAAC,GAAG,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG;QAAA;IAC3C;IAEA,+CAA+C;IAC/C,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,GAAG,2JAAA,CAAA,QAAK,CAAC,iBAAiB;IAE9C,uEAAuE;IACvE,uBAAuB;IACvB,IAAI,YAAY;QACd,OAAO,MAAM,CAAC,MAAM,GAAG;YAAC;YAAS;YAAS;SAAQ;QAClD,OAAO,MAAM,CAAC,aAAa,GAAG,GAAG;YAAC;YAAQ,MAAM,CAAC,QAAQ,CAAC,EAAE;YAAE;SAAQ;QACtE,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,GAAG;YAAC,GAAG,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,GAAG;QAAA;IACxD,OAAO;QACL,MAAM,CAAC,QAAQ,CAAC,EAAE,GAAG;IACvB;IAEA,mCAAmC;IACnC,OAAO,IAAI,CAAC;QAAC;QAAQ;QAAS;KAAQ;IAEtC,OAAO;AACT;AAEA;;;;CAIC,GACD,SAAS,wBAAwB,OAAO,EAAE,EAAE,EAAE,GAAG;IAC/C,MAAM,OAAO,IAAI;IACjB,8BAA8B,GAC9B,IAAI;IAEJ,OAAO;;IAEP;;;;;;;;;;GAUC,GACD,SAAS,MAAM,IAAI;QACjB,IAAI,QAAQ,KAAK,MAAM,CAAC,MAAM;QAC9B,gCAAgC,GAChC,IAAI;QAEJ,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EACH,SAAS,2JAAA,CAAA,QAAK,CAAC,IAAI,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,QAAQ,EAC9C;QAGF,mBAAmB;QACnB,MAAO,QAAS;YACd,4DAA4D;YAC5D,sDAAsD;YACtD,IACE,KAAK,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,2JAAA,CAAA,QAAK,CAAC,UAAU,IAC/C,KAAK,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,2JAAA,CAAA,QAAK,CAAC,UAAU,IAC/C,KAAK,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,2JAAA,CAAA,QAAK,CAAC,OAAO,EAC5C;gBACA,YAAY,KAAK,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,2JAAA,CAAA,QAAK,CAAC,SAAS;gBAC1D;YACF;QACF;QAEA,gDAAgD;QAChD,0CAA0C;QAC1C,IAAI,CAAC,KAAK,MAAM,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,SAAS,IAAI,SAAS,GAAG;YACvE,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,iBAAiB;YACrC,SAAS;YACT,OAAO,OAAO;QAChB;QAEA,OAAO,IAAI;IACb;IAEA;;;;;;;;;;GAUC,GACD,SAAS,OAAO,IAAI;QAClB,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,yBAAyB;QAC7C,OAAO,OAAO;IAChB;IAEA;;;;;;;;;;GAUC,GACD,SAAS,OAAO,IAAI;QAClB,IAAI,SAAS,QAAQ;YACnB,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,yBAAyB;QAE5C,OAAO,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE,QACjB,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EAAE,SAAS,OAAO,2JAAA,CAAA,QAAK,CAAC,UAAU,EAAE,QAC/C,MAAM;IACZ;IAEA;;;;;;;;;;GAUC,GACD,SAAS,MAAM,IAAI;QACjB,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,IAAI,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAClD,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,iBAAiB;YACpC,OAAO,GAAG;QACZ;QAEA,OAAO,IAAI;IACb;AACF", "ignoreList": [0]}}, {"offset": {"line": 1343, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1349, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/node_modules/micromark-core-commonmark/dev/lib/html-flow.js"], "sourcesContent": ["/**\n * @import {\n *   Code,\n *   Construct,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {\n  asciiAlphanumeric,\n  asciiAlpha,\n  markdownLineEndingOrSpace,\n  markdownLineEnding,\n  markdownSpace\n} from 'micromark-util-character'\nimport {htmlBlockNames, htmlRawNames} from 'micromark-util-html-tag-name'\nimport {codes, constants, types} from 'micromark-util-symbol'\nimport {blankLine} from './blank-line.js'\n\n/** @type {Construct} */\nexport const htmlFlow = {\n  concrete: true,\n  name: 'htmlFlow',\n  resolveTo: resolveToHtmlFlow,\n  tokenize: tokenizeHtmlFlow\n}\n\n/** @type {Construct} */\nconst blankLineBefore = {partial: true, tokenize: tokenizeBlankLineBefore}\nconst nonLazyContinuationStart = {\n  partial: true,\n  tokenize: tokenizeNonLazyContinuationStart\n}\n\n/** @type {Resolver} */\nfunction resolveToHtmlFlow(events) {\n  let index = events.length\n\n  while (index--) {\n    if (\n      events[index][0] === 'enter' &&\n      events[index][1].type === types.htmlFlow\n    ) {\n      break\n    }\n  }\n\n  if (index > 1 && events[index - 2][1].type === types.linePrefix) {\n    // Add the prefix start to the HTML token.\n    events[index][1].start = events[index - 2][1].start\n    // Add the prefix start to the HTML line token.\n    events[index + 1][1].start = events[index - 2][1].start\n    // Remove the line prefix.\n    events.splice(index - 2, 2)\n  }\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeHtmlFlow(effects, ok, nok) {\n  const self = this\n  /** @type {number} */\n  let marker\n  /** @type {boolean} */\n  let closingTag\n  /** @type {string} */\n  let buffer\n  /** @type {number} */\n  let index\n  /** @type {Code} */\n  let markerB\n\n  return start\n\n  /**\n   * Start of HTML (flow).\n   *\n   * ```markdown\n   * > | <x />\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // To do: parse indent like `markdown-rs`.\n    return before(code)\n  }\n\n  /**\n   * At `<`, after optional whitespace.\n   *\n   * ```markdown\n   * > | <x />\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function before(code) {\n    assert(code === codes.lessThan, 'expected `<`')\n    effects.enter(types.htmlFlow)\n    effects.enter(types.htmlFlowData)\n    effects.consume(code)\n    return open\n  }\n\n  /**\n   * After `<`, at tag name or other stuff.\n   *\n   * ```markdown\n   * > | <x />\n   *      ^\n   * > | <!doctype>\n   *      ^\n   * > | <!--xxx-->\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    if (code === codes.exclamationMark) {\n      effects.consume(code)\n      return declarationOpen\n    }\n\n    if (code === codes.slash) {\n      effects.consume(code)\n      closingTag = true\n      return tagCloseStart\n    }\n\n    if (code === codes.questionMark) {\n      effects.consume(code)\n      marker = constants.htmlInstruction\n      // To do:\n      // tokenizer.concrete = true\n      // To do: use `markdown-rs` style interrupt.\n      // While we’re in an instruction instead of a declaration, we’re on a `?`\n      // right now, so we do need to search for `>`, similar to declarations.\n      return self.interrupt ? ok : continuationDeclarationInside\n    }\n\n    // ASCII alphabetical.\n    if (asciiAlpha(code)) {\n      assert(code !== null) // Always the case.\n      effects.consume(code)\n      buffer = String.fromCharCode(code)\n      return tagName\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `<!`, at declaration, comment, or CDATA.\n   *\n   * ```markdown\n   * > | <!doctype>\n   *       ^\n   * > | <!--xxx-->\n   *       ^\n   * > | <![CDATA[>&<]]>\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function declarationOpen(code) {\n    if (code === codes.dash) {\n      effects.consume(code)\n      marker = constants.htmlComment\n      return commentOpenInside\n    }\n\n    if (code === codes.leftSquareBracket) {\n      effects.consume(code)\n      marker = constants.htmlCdata\n      index = 0\n      return cdataOpenInside\n    }\n\n    // ASCII alphabetical.\n    if (asciiAlpha(code)) {\n      effects.consume(code)\n      marker = constants.htmlDeclaration\n      // // Do not form containers.\n      // tokenizer.concrete = true\n      return self.interrupt ? ok : continuationDeclarationInside\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `<!-`, inside a comment, at another `-`.\n   *\n   * ```markdown\n   * > | <!--xxx-->\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function commentOpenInside(code) {\n    if (code === codes.dash) {\n      effects.consume(code)\n      // // Do not form containers.\n      // tokenizer.concrete = true\n      return self.interrupt ? ok : continuationDeclarationInside\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `<![`, inside CDATA, expecting `CDATA[`.\n   *\n   * ```markdown\n   * > | <![CDATA[>&<]]>\n   *        ^^^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function cdataOpenInside(code) {\n    const value = constants.cdataOpeningString\n\n    if (code === value.charCodeAt(index++)) {\n      effects.consume(code)\n\n      if (index === value.length) {\n        // // Do not form containers.\n        // tokenizer.concrete = true\n        return self.interrupt ? ok : continuation\n      }\n\n      return cdataOpenInside\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `</`, in closing tag, at tag name.\n   *\n   * ```markdown\n   * > | </x>\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagCloseStart(code) {\n    if (asciiAlpha(code)) {\n      assert(code !== null) // Always the case.\n      effects.consume(code)\n      buffer = String.fromCharCode(code)\n      return tagName\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In tag name.\n   *\n   * ```markdown\n   * > | <ab>\n   *      ^^\n   * > | </ab>\n   *       ^^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagName(code) {\n    if (\n      code === codes.eof ||\n      code === codes.slash ||\n      code === codes.greaterThan ||\n      markdownLineEndingOrSpace(code)\n    ) {\n      const slash = code === codes.slash\n      const name = buffer.toLowerCase()\n\n      if (!slash && !closingTag && htmlRawNames.includes(name)) {\n        marker = constants.htmlRaw\n        // // Do not form containers.\n        // tokenizer.concrete = true\n        return self.interrupt ? ok(code) : continuation(code)\n      }\n\n      if (htmlBlockNames.includes(buffer.toLowerCase())) {\n        marker = constants.htmlBasic\n\n        if (slash) {\n          effects.consume(code)\n          return basicSelfClosing\n        }\n\n        // // Do not form containers.\n        // tokenizer.concrete = true\n        return self.interrupt ? ok(code) : continuation(code)\n      }\n\n      marker = constants.htmlComplete\n      // Do not support complete HTML when interrupting.\n      return self.interrupt && !self.parser.lazy[self.now().line]\n        ? nok(code)\n        : closingTag\n          ? completeClosingTagAfter(code)\n          : completeAttributeNameBefore(code)\n    }\n\n    // ASCII alphanumerical and `-`.\n    if (code === codes.dash || asciiAlphanumeric(code)) {\n      effects.consume(code)\n      buffer += String.fromCharCode(code)\n      return tagName\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After closing slash of a basic tag name.\n   *\n   * ```markdown\n   * > | <div/>\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function basicSelfClosing(code) {\n    if (code === codes.greaterThan) {\n      effects.consume(code)\n      // // Do not form containers.\n      // tokenizer.concrete = true\n      return self.interrupt ? ok : continuation\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After closing slash of a complete tag name.\n   *\n   * ```markdown\n   * > | <x/>\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeClosingTagAfter(code) {\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return completeClosingTagAfter\n    }\n\n    return completeEnd(code)\n  }\n\n  /**\n   * At an attribute name.\n   *\n   * At first, this state is used after a complete tag name, after whitespace,\n   * where it expects optional attributes or the end of the tag.\n   * It is also reused after attributes, when expecting more optional\n   * attributes.\n   *\n   * ```markdown\n   * > | <a />\n   *        ^\n   * > | <a :b>\n   *        ^\n   * > | <a _b>\n   *        ^\n   * > | <a b>\n   *        ^\n   * > | <a >\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeNameBefore(code) {\n    if (code === codes.slash) {\n      effects.consume(code)\n      return completeEnd\n    }\n\n    // ASCII alphanumerical and `:` and `_`.\n    if (code === codes.colon || code === codes.underscore || asciiAlpha(code)) {\n      effects.consume(code)\n      return completeAttributeName\n    }\n\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return completeAttributeNameBefore\n    }\n\n    return completeEnd(code)\n  }\n\n  /**\n   * In attribute name.\n   *\n   * ```markdown\n   * > | <a :b>\n   *         ^\n   * > | <a _b>\n   *         ^\n   * > | <a b>\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeName(code) {\n    // ASCII alphanumerical and `-`, `.`, `:`, and `_`.\n    if (\n      code === codes.dash ||\n      code === codes.dot ||\n      code === codes.colon ||\n      code === codes.underscore ||\n      asciiAlphanumeric(code)\n    ) {\n      effects.consume(code)\n      return completeAttributeName\n    }\n\n    return completeAttributeNameAfter(code)\n  }\n\n  /**\n   * After attribute name, at an optional initializer, the end of the tag, or\n   * whitespace.\n   *\n   * ```markdown\n   * > | <a b>\n   *         ^\n   * > | <a b=c>\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeNameAfter(code) {\n    if (code === codes.equalsTo) {\n      effects.consume(code)\n      return completeAttributeValueBefore\n    }\n\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return completeAttributeNameAfter\n    }\n\n    return completeAttributeNameBefore(code)\n  }\n\n  /**\n   * Before unquoted, double quoted, or single quoted attribute value, allowing\n   * whitespace.\n   *\n   * ```markdown\n   * > | <a b=c>\n   *          ^\n   * > | <a b=\"c\">\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeValueBefore(code) {\n    if (\n      code === codes.eof ||\n      code === codes.lessThan ||\n      code === codes.equalsTo ||\n      code === codes.greaterThan ||\n      code === codes.graveAccent\n    ) {\n      return nok(code)\n    }\n\n    if (code === codes.quotationMark || code === codes.apostrophe) {\n      effects.consume(code)\n      markerB = code\n      return completeAttributeValueQuoted\n    }\n\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return completeAttributeValueBefore\n    }\n\n    return completeAttributeValueUnquoted(code)\n  }\n\n  /**\n   * In double or single quoted attribute value.\n   *\n   * ```markdown\n   * > | <a b=\"c\">\n   *           ^\n   * > | <a b='c'>\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeValueQuoted(code) {\n    if (code === markerB) {\n      effects.consume(code)\n      markerB = null\n      return completeAttributeValueQuotedAfter\n    }\n\n    if (code === codes.eof || markdownLineEnding(code)) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return completeAttributeValueQuoted\n  }\n\n  /**\n   * In unquoted attribute value.\n   *\n   * ```markdown\n   * > | <a b=c>\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeValueUnquoted(code) {\n    if (\n      code === codes.eof ||\n      code === codes.quotationMark ||\n      code === codes.apostrophe ||\n      code === codes.slash ||\n      code === codes.lessThan ||\n      code === codes.equalsTo ||\n      code === codes.greaterThan ||\n      code === codes.graveAccent ||\n      markdownLineEndingOrSpace(code)\n    ) {\n      return completeAttributeNameAfter(code)\n    }\n\n    effects.consume(code)\n    return completeAttributeValueUnquoted\n  }\n\n  /**\n   * After double or single quoted attribute value, before whitespace or the\n   * end of the tag.\n   *\n   * ```markdown\n   * > | <a b=\"c\">\n   *            ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAttributeValueQuotedAfter(code) {\n    if (\n      code === codes.slash ||\n      code === codes.greaterThan ||\n      markdownSpace(code)\n    ) {\n      return completeAttributeNameBefore(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In certain circumstances of a complete tag where only an `>` is allowed.\n   *\n   * ```markdown\n   * > | <a b=\"c\">\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeEnd(code) {\n    if (code === codes.greaterThan) {\n      effects.consume(code)\n      return completeAfter\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `>` in a complete tag.\n   *\n   * ```markdown\n   * > | <x>\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function completeAfter(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      // // Do not form containers.\n      // tokenizer.concrete = true\n      return continuation(code)\n    }\n\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return completeAfter\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In continuation of any HTML kind.\n   *\n   * ```markdown\n   * > | <!--xxx-->\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuation(code) {\n    if (code === codes.dash && marker === constants.htmlComment) {\n      effects.consume(code)\n      return continuationCommentInside\n    }\n\n    if (code === codes.lessThan && marker === constants.htmlRaw) {\n      effects.consume(code)\n      return continuationRawTagOpen\n    }\n\n    if (code === codes.greaterThan && marker === constants.htmlDeclaration) {\n      effects.consume(code)\n      return continuationClose\n    }\n\n    if (code === codes.questionMark && marker === constants.htmlInstruction) {\n      effects.consume(code)\n      return continuationDeclarationInside\n    }\n\n    if (code === codes.rightSquareBracket && marker === constants.htmlCdata) {\n      effects.consume(code)\n      return continuationCdataInside\n    }\n\n    if (\n      markdownLineEnding(code) &&\n      (marker === constants.htmlBasic || marker === constants.htmlComplete)\n    ) {\n      effects.exit(types.htmlFlowData)\n      return effects.check(\n        blankLineBefore,\n        continuationAfter,\n        continuationStart\n      )(code)\n    }\n\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.htmlFlowData)\n      return continuationStart(code)\n    }\n\n    effects.consume(code)\n    return continuation\n  }\n\n  /**\n   * In continuation, at eol.\n   *\n   * ```markdown\n   * > | <x>\n   *        ^\n   *   | asd\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationStart(code) {\n    return effects.check(\n      nonLazyContinuationStart,\n      continuationStartNonLazy,\n      continuationAfter\n    )(code)\n  }\n\n  /**\n   * In continuation, at eol, before non-lazy content.\n   *\n   * ```markdown\n   * > | <x>\n   *        ^\n   *   | asd\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationStartNonLazy(code) {\n    assert(markdownLineEnding(code))\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    return continuationBefore\n  }\n\n  /**\n   * In continuation, before non-lazy content.\n   *\n   * ```markdown\n   *   | <x>\n   * > | asd\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationBefore(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      return continuationStart(code)\n    }\n\n    effects.enter(types.htmlFlowData)\n    return continuation(code)\n  }\n\n  /**\n   * In comment continuation, after one `-`, expecting another.\n   *\n   * ```markdown\n   * > | <!--xxx-->\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationCommentInside(code) {\n    if (code === codes.dash) {\n      effects.consume(code)\n      return continuationDeclarationInside\n    }\n\n    return continuation(code)\n  }\n\n  /**\n   * In raw continuation, after `<`, at `/`.\n   *\n   * ```markdown\n   * > | <script>console.log(1)</script>\n   *                            ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationRawTagOpen(code) {\n    if (code === codes.slash) {\n      effects.consume(code)\n      buffer = ''\n      return continuationRawEndTag\n    }\n\n    return continuation(code)\n  }\n\n  /**\n   * In raw continuation, after `</`, in a raw tag name.\n   *\n   * ```markdown\n   * > | <script>console.log(1)</script>\n   *                             ^^^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationRawEndTag(code) {\n    if (code === codes.greaterThan) {\n      const name = buffer.toLowerCase()\n\n      if (htmlRawNames.includes(name)) {\n        effects.consume(code)\n        return continuationClose\n      }\n\n      return continuation(code)\n    }\n\n    if (asciiAlpha(code) && buffer.length < constants.htmlRawSizeMax) {\n      assert(code !== null) // Always the case.\n      effects.consume(code)\n      buffer += String.fromCharCode(code)\n      return continuationRawEndTag\n    }\n\n    return continuation(code)\n  }\n\n  /**\n   * In cdata continuation, after `]`, expecting `]>`.\n   *\n   * ```markdown\n   * > | <![CDATA[>&<]]>\n   *                  ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationCdataInside(code) {\n    if (code === codes.rightSquareBracket) {\n      effects.consume(code)\n      return continuationDeclarationInside\n    }\n\n    return continuation(code)\n  }\n\n  /**\n   * In declaration or instruction continuation, at `>`.\n   *\n   * ```markdown\n   * > | <!-->\n   *         ^\n   * > | <?>\n   *       ^\n   * > | <!q>\n   *        ^\n   * > | <!--ab-->\n   *             ^\n   * > | <![CDATA[>&<]]>\n   *                   ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationDeclarationInside(code) {\n    if (code === codes.greaterThan) {\n      effects.consume(code)\n      return continuationClose\n    }\n\n    // More dashes.\n    if (code === codes.dash && marker === constants.htmlComment) {\n      effects.consume(code)\n      return continuationDeclarationInside\n    }\n\n    return continuation(code)\n  }\n\n  /**\n   * In closed continuation: everything we get until the eol/eof is part of it.\n   *\n   * ```markdown\n   * > | <!doctype>\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationClose(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.htmlFlowData)\n      return continuationAfter(code)\n    }\n\n    effects.consume(code)\n    return continuationClose\n  }\n\n  /**\n   * Done.\n   *\n   * ```markdown\n   * > | <!doctype>\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function continuationAfter(code) {\n    effects.exit(types.htmlFlow)\n    // // Feel free to interrupt.\n    // tokenizer.interrupt = false\n    // // No longer concrete.\n    // tokenizer.concrete = false\n    return ok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeNonLazyContinuationStart(effects, ok, nok) {\n  const self = this\n\n  return start\n\n  /**\n   * At eol, before continuation.\n   *\n   * ```markdown\n   * > | * ```js\n   *            ^\n   *   | b\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if (markdownLineEnding(code)) {\n      effects.enter(types.lineEnding)\n      effects.consume(code)\n      effects.exit(types.lineEnding)\n      return after\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * A continuation.\n   *\n   * ```markdown\n   *   | * ```js\n   * > | b\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    return self.parser.lazy[self.now().line] ? nok(code) : ok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeBlankLineBefore(effects, ok, nok) {\n  return start\n\n  /**\n   * Before eol, expecting blank line.\n   *\n   * ```markdown\n   * > | <div>\n   *          ^\n   *   |\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(markdownLineEnding(code), 'expected a line ending')\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    return effects.attempt(blankLine, ok, nok)\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;CASC;;;AAWD;AATA;AASA;AARA;AAQA;AADA;AAEA;;;;;;AAGO,MAAM,WAAW;IACtB,UAAU;IACV,MAAM;IACN,WAAW;IACX,UAAU;AACZ;AAEA,sBAAsB,GACtB,MAAM,kBAAkB;IAAC,SAAS;IAAM,UAAU;AAAuB;AACzE,MAAM,2BAA2B;IAC/B,SAAS;IACT,UAAU;AACZ;AAEA,qBAAqB,GACrB,SAAS,kBAAkB,MAAM;IAC/B,IAAI,QAAQ,OAAO,MAAM;IAEzB,MAAO,QAAS;QACd,IACE,MAAM,CAAC,MAAM,CAAC,EAAE,KAAK,WACrB,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,2JAAA,CAAA,QAAK,CAAC,QAAQ,EACxC;YACA;QACF;IACF;IAEA,IAAI,QAAQ,KAAK,MAAM,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,IAAI,KAAK,2JAAA,CAAA,QAAK,CAAC,UAAU,EAAE;QAC/D,0CAA0C;QAC1C,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,KAAK;QACnD,+CAA+C;QAC/C,MAAM,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,KAAK,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,KAAK;QACvD,0BAA0B;QAC1B,OAAO,MAAM,CAAC,QAAQ,GAAG;IAC3B;IAEA,OAAO;AACT;AAEA;;;;CAIC,GACD,SAAS,iBAAiB,OAAO,EAAE,EAAE,EAAE,GAAG;IACxC,MAAM,OAAO,IAAI;IACjB,mBAAmB,GACnB,IAAI;IACJ,oBAAoB,GACpB,IAAI;IACJ,mBAAmB,GACnB,IAAI;IACJ,mBAAmB,GACnB,IAAI;IACJ,iBAAiB,GACjB,IAAI;IAEJ,OAAO;;IAEP;;;;;;;;;GASC,GACD,SAAS,MAAM,IAAI;QACjB,0CAA0C;QAC1C,OAAO,OAAO;IAChB;IAEA;;;;;;;;;GASC,GACD,SAAS,OAAO,IAAI;QAClB,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,SAAS,2JAAA,CAAA,QAAK,CAAC,QAAQ,EAAE;QAChC,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,QAAQ;QAC5B,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,YAAY;QAChC,QAAQ,OAAO,CAAC;QAChB,OAAO;IACT;IAEA;;;;;;;;;;;;;GAaC,GACD,SAAS,KAAK,IAAI;QAChB,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,eAAe,EAAE;YAClC,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,KAAK,EAAE;YACxB,QAAQ,OAAO,CAAC;YAChB,aAAa;YACb,OAAO;QACT;QAEA,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,YAAY,EAAE;YAC/B,QAAQ,OAAO,CAAC;YAChB,SAAS,+JAAA,CAAA,YAAS,CAAC,eAAe;YAClC,SAAS;YACT,4BAA4B;YAC5B,4CAA4C;YAC5C,yEAAyE;YACzE,uEAAuE;YACvE,OAAO,KAAK,SAAS,GAAG,KAAK;QAC/B;QAEA,sBAAsB;QACtB,IAAI,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD,EAAE,OAAO;YACpB,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,SAAS,MAAM,mBAAmB;;YACzC,QAAQ,OAAO,CAAC;YAChB,SAAS,OAAO,YAAY,CAAC;YAC7B,OAAO;QACT;QAEA,OAAO,IAAI;IACb;IAEA;;;;;;;;;;;;;GAaC,GACD,SAAS,gBAAgB,IAAI;QAC3B,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,IAAI,EAAE;YACvB,QAAQ,OAAO,CAAC;YAChB,SAAS,+JAAA,CAAA,YAAS,CAAC,WAAW;YAC9B,OAAO;QACT;QAEA,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,iBAAiB,EAAE;YACpC,QAAQ,OAAO,CAAC;YAChB,SAAS,+JAAA,CAAA,YAAS,CAAC,SAAS;YAC5B,QAAQ;YACR,OAAO;QACT;QAEA,sBAAsB;QACtB,IAAI,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD,EAAE,OAAO;YACpB,QAAQ,OAAO,CAAC;YAChB,SAAS,+JAAA,CAAA,YAAS,CAAC,eAAe;YAClC,6BAA6B;YAC7B,4BAA4B;YAC5B,OAAO,KAAK,SAAS,GAAG,KAAK;QAC/B;QAEA,OAAO,IAAI;IACb;IAEA;;;;;;;;;GASC,GACD,SAAS,kBAAkB,IAAI;QAC7B,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,IAAI,EAAE;YACvB,QAAQ,OAAO,CAAC;YAChB,6BAA6B;YAC7B,4BAA4B;YAC5B,OAAO,KAAK,SAAS,GAAG,KAAK;QAC/B;QAEA,OAAO,IAAI;IACb;IAEA;;;;;;;;;GASC,GACD,SAAS,gBAAgB,IAAI;QAC3B,MAAM,QAAQ,+JAAA,CAAA,YAAS,CAAC,kBAAkB;QAE1C,IAAI,SAAS,MAAM,UAAU,CAAC,UAAU;YACtC,QAAQ,OAAO,CAAC;YAEhB,IAAI,UAAU,MAAM,MAAM,EAAE;gBAC1B,6BAA6B;gBAC7B,4BAA4B;gBAC5B,OAAO,KAAK,SAAS,GAAG,KAAK;YAC/B;YAEA,OAAO;QACT;QAEA,OAAO,IAAI;IACb;IAEA;;;;;;;;;GASC,GACD,SAAS,cAAc,IAAI;QACzB,IAAI,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD,EAAE,OAAO;YACpB,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,SAAS,MAAM,mBAAmB;;YACzC,QAAQ,OAAO,CAAC;YAChB,SAAS,OAAO,YAAY,CAAC;YAC7B,OAAO;QACT;QAEA,OAAO,IAAI;IACb;IAEA;;;;;;;;;;;GAWC,GACD,SAAS,QAAQ,IAAI;QACnB,IACE,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,IAClB,SAAS,2JAAA,CAAA,QAAK,CAAC,KAAK,IACpB,SAAS,2JAAA,CAAA,QAAK,CAAC,WAAW,IAC1B,CAAA,GAAA,8JAAA,CAAA,4BAAyB,AAAD,EAAE,OAC1B;YACA,MAAM,QAAQ,SAAS,2JAAA,CAAA,QAAK,CAAC,KAAK;YAClC,MAAM,OAAO,OAAO,WAAW;YAE/B,IAAI,CAAC,SAAS,CAAC,cAAc,iKAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,OAAO;gBACxD,SAAS,+JAAA,CAAA,YAAS,CAAC,OAAO;gBAC1B,6BAA6B;gBAC7B,4BAA4B;gBAC5B,OAAO,KAAK,SAAS,GAAG,GAAG,QAAQ,aAAa;YAClD;YAEA,IAAI,iKAAA,CAAA,iBAAc,CAAC,QAAQ,CAAC,OAAO,WAAW,KAAK;gBACjD,SAAS,+JAAA,CAAA,YAAS,CAAC,SAAS;gBAE5B,IAAI,OAAO;oBACT,QAAQ,OAAO,CAAC;oBAChB,OAAO;gBACT;gBAEA,6BAA6B;gBAC7B,4BAA4B;gBAC5B,OAAO,KAAK,SAAS,GAAG,GAAG,QAAQ,aAAa;YAClD;YAEA,SAAS,+JAAA,CAAA,YAAS,CAAC,YAAY;YAC/B,kDAAkD;YAClD,OAAO,KAAK,SAAS,IAAI,CAAC,KAAK,MAAM,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,GACvD,IAAI,QACJ,aACE,wBAAwB,QACxB,4BAA4B;QACpC;QAEA,gCAAgC;QAChC,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,IAAI,IAAI,CAAA,GAAA,8JAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO;YAClD,QAAQ,OAAO,CAAC;YAChB,UAAU,OAAO,YAAY,CAAC;YAC9B,OAAO;QACT;QAEA,OAAO,IAAI;IACb;IAEA;;;;;;;;;GASC,GACD,SAAS,iBAAiB,IAAI;QAC5B,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,WAAW,EAAE;YAC9B,QAAQ,OAAO,CAAC;YAChB,6BAA6B;YAC7B,4BAA4B;YAC5B,OAAO,KAAK,SAAS,GAAG,KAAK;QAC/B;QAEA,OAAO,IAAI;IACb;IAEA;;;;;;;;;GASC,GACD,SAAS,wBAAwB,IAAI;QACnC,IAAI,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE,OAAO;YACvB,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,OAAO,YAAY;IACrB;IAEA;;;;;;;;;;;;;;;;;;;;;;GAsBC,GACD,SAAS,4BAA4B,IAAI;QACvC,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,KAAK,EAAE;YACxB,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,wCAAwC;QACxC,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,KAAK,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,UAAU,IAAI,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD,EAAE,OAAO;YACzE,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,IAAI,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE,OAAO;YACvB,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,OAAO,YAAY;IACrB;IAEA;;;;;;;;;;;;;GAaC,GACD,SAAS,sBAAsB,IAAI;QACjC,mDAAmD;QACnD,IACE,SAAS,2JAAA,CAAA,QAAK,CAAC,IAAI,IACnB,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,IAClB,SAAS,2JAAA,CAAA,QAAK,CAAC,KAAK,IACpB,SAAS,2JAAA,CAAA,QAAK,CAAC,UAAU,IACzB,CAAA,GAAA,8JAAA,CAAA,oBAAiB,AAAD,EAAE,OAClB;YACA,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,OAAO,2BAA2B;IACpC;IAEA;;;;;;;;;;;;GAYC,GACD,SAAS,2BAA2B,IAAI;QACtC,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,QAAQ,EAAE;YAC3B,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,IAAI,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE,OAAO;YACvB,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,OAAO,4BAA4B;IACrC;IAEA;;;;;;;;;;;;GAYC,GACD,SAAS,6BAA6B,IAAI;QACxC,IACE,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,IAClB,SAAS,2JAAA,CAAA,QAAK,CAAC,QAAQ,IACvB,SAAS,2JAAA,CAAA,QAAK,CAAC,QAAQ,IACvB,SAAS,2JAAA,CAAA,QAAK,CAAC,WAAW,IAC1B,SAAS,2JAAA,CAAA,QAAK,CAAC,WAAW,EAC1B;YACA,OAAO,IAAI;QACb;QAEA,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,aAAa,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,UAAU,EAAE;YAC7D,QAAQ,OAAO,CAAC;YAChB,UAAU;YACV,OAAO;QACT;QAEA,IAAI,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE,OAAO;YACvB,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,OAAO,+BAA+B;IACxC;IAEA;;;;;;;;;;;GAWC,GACD,SAAS,6BAA6B,IAAI;QACxC,IAAI,SAAS,SAAS;YACpB,QAAQ,OAAO,CAAC;YAChB,UAAU;YACV,OAAO;QACT;QAEA,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,IAAI,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAClD,OAAO,IAAI;QACb;QAEA,QAAQ,OAAO,CAAC;QAChB,OAAO;IACT;IAEA;;;;;;;;;GASC,GACD,SAAS,+BAA+B,IAAI;QAC1C,IACE,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,IAClB,SAAS,2JAAA,CAAA,QAAK,CAAC,aAAa,IAC5B,SAAS,2JAAA,CAAA,QAAK,CAAC,UAAU,IACzB,SAAS,2JAAA,CAAA,QAAK,CAAC,KAAK,IACpB,SAAS,2JAAA,CAAA,QAAK,CAAC,QAAQ,IACvB,SAAS,2JAAA,CAAA,QAAK,CAAC,QAAQ,IACvB,SAAS,2JAAA,CAAA,QAAK,CAAC,WAAW,IAC1B,SAAS,2JAAA,CAAA,QAAK,CAAC,WAAW,IAC1B,CAAA,GAAA,8JAAA,CAAA,4BAAyB,AAAD,EAAE,OAC1B;YACA,OAAO,2BAA2B;QACpC;QAEA,QAAQ,OAAO,CAAC;QAChB,OAAO;IACT;IAEA;;;;;;;;;;GAUC,GACD,SAAS,kCAAkC,IAAI;QAC7C,IACE,SAAS,2JAAA,CAAA,QAAK,CAAC,KAAK,IACpB,SAAS,2JAAA,CAAA,QAAK,CAAC,WAAW,IAC1B,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE,OACd;YACA,OAAO,4BAA4B;QACrC;QAEA,OAAO,IAAI;IACb;IAEA;;;;;;;;;GASC,GACD,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,WAAW,EAAE;YAC9B,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,OAAO,IAAI;IACb;IAEA;;;;;;;;;GASC,GACD,SAAS,cAAc,IAAI;QACzB,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,IAAI,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAClD,6BAA6B;YAC7B,4BAA4B;YAC5B,OAAO,aAAa;QACtB;QAEA,IAAI,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE,OAAO;YACvB,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,OAAO,IAAI;IACb;IAEA;;;;;;;;;GASC,GACD,SAAS,aAAa,IAAI;QACxB,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,IAAI,IAAI,WAAW,+JAAA,CAAA,YAAS,CAAC,WAAW,EAAE;YAC3D,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,QAAQ,IAAI,WAAW,+JAAA,CAAA,YAAS,CAAC,OAAO,EAAE;YAC3D,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,WAAW,IAAI,WAAW,+JAAA,CAAA,YAAS,CAAC,eAAe,EAAE;YACtE,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,YAAY,IAAI,WAAW,+JAAA,CAAA,YAAS,CAAC,eAAe,EAAE;YACvE,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,kBAAkB,IAAI,WAAW,+JAAA,CAAA,YAAS,CAAC,SAAS,EAAE;YACvE,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,IACE,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,SACnB,CAAC,WAAW,+JAAA,CAAA,YAAS,CAAC,SAAS,IAAI,WAAW,+JAAA,CAAA,YAAS,CAAC,YAAY,GACpE;YACA,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,YAAY;YAC/B,OAAO,QAAQ,KAAK,CAClB,iBACA,mBACA,mBACA;QACJ;QAEA,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,IAAI,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAClD,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,YAAY;YAC/B,OAAO,kBAAkB;QAC3B;QAEA,QAAQ,OAAO,CAAC;QAChB,OAAO;IACT;IAEA;;;;;;;;;;GAUC,GACD,SAAS,kBAAkB,IAAI;QAC7B,OAAO,QAAQ,KAAK,CAClB,0BACA,0BACA,mBACA;IACJ;IAEA;;;;;;;;;;GAUC,GACD,SAAS,yBAAyB,IAAI;QACpC,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE;QAC1B,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,UAAU;QAC9B,QAAQ,OAAO,CAAC;QAChB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,UAAU;QAC7B,OAAO;IACT;IAEA;;;;;;;;;;GAUC,GACD,SAAS,mBAAmB,IAAI;QAC9B,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,IAAI,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAClD,OAAO,kBAAkB;QAC3B;QAEA,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,YAAY;QAChC,OAAO,aAAa;IACtB;IAEA;;;;;;;;;GASC,GACD,SAAS,0BAA0B,IAAI;QACrC,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,IAAI,EAAE;YACvB,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,OAAO,aAAa;IACtB;IAEA;;;;;;;;;GASC,GACD,SAAS,uBAAuB,IAAI;QAClC,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,KAAK,EAAE;YACxB,QAAQ,OAAO,CAAC;YAChB,SAAS;YACT,OAAO;QACT;QAEA,OAAO,aAAa;IACtB;IAEA;;;;;;;;;GASC,GACD,SAAS,sBAAsB,IAAI;QACjC,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,WAAW,EAAE;YAC9B,MAAM,OAAO,OAAO,WAAW;YAE/B,IAAI,iKAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,OAAO;gBAC/B,QAAQ,OAAO,CAAC;gBAChB,OAAO;YACT;YAEA,OAAO,aAAa;QACtB;QAEA,IAAI,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD,EAAE,SAAS,OAAO,MAAM,GAAG,+JAAA,CAAA,YAAS,CAAC,cAAc,EAAE;YAChE,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,SAAS,MAAM,mBAAmB;;YACzC,QAAQ,OAAO,CAAC;YAChB,UAAU,OAAO,YAAY,CAAC;YAC9B,OAAO;QACT;QAEA,OAAO,aAAa;IACtB;IAEA;;;;;;;;;GASC,GACD,SAAS,wBAAwB,IAAI;QACnC,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,kBAAkB,EAAE;YACrC,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,OAAO,aAAa;IACtB;IAEA;;;;;;;;;;;;;;;;;GAiBC,GACD,SAAS,8BAA8B,IAAI;QACzC,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,WAAW,EAAE;YAC9B,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,eAAe;QACf,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,IAAI,IAAI,WAAW,+JAAA,CAAA,YAAS,CAAC,WAAW,EAAE;YAC3D,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,OAAO,aAAa;IACtB;IAEA;;;;;;;;;GASC,GACD,SAAS,kBAAkB,IAAI;QAC7B,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,IAAI,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAClD,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,YAAY;YAC/B,OAAO,kBAAkB;QAC3B;QAEA,QAAQ,OAAO,CAAC;QAChB,OAAO;IACT;IAEA;;;;;;;;;GASC,GACD,SAAS,kBAAkB,IAAI;QAC7B,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,QAAQ;QAC3B,6BAA6B;QAC7B,8BAA8B;QAC9B,yBAAyB;QACzB,6BAA6B;QAC7B,OAAO,GAAG;IACZ;AACF;AAEA;;;;CAIC,GACD,SAAS,iCAAiC,OAAO,EAAE,EAAE,EAAE,GAAG;IACxD,MAAM,OAAO,IAAI;IAEjB,OAAO;;IAEP;;;;;;;;;;GAUC,GACD,SAAS,MAAM,IAAI;QACjB,IAAI,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAC5B,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,UAAU;YAC9B,QAAQ,OAAO,CAAC;YAChB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,UAAU;YAC7B,OAAO;QACT;QAEA,OAAO,IAAI;IACb;IAEA;;;;;;;;;;GAUC,GACD,SAAS,MAAM,IAAI;QACjB,OAAO,KAAK,MAAM,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,QAAQ,GAAG;IAC5D;AACF;AAEA;;;;CAIC,GACD,SAAS,wBAAwB,OAAO,EAAE,EAAE,EAAE,GAAG;IAC/C,OAAO;;IAEP;;;;;;;;;;GAUC,GACD,SAAS,MAAM,IAAI;QACjB,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;QACjC,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,UAAU;QAC9B,QAAQ,OAAO,CAAC;QAChB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,UAAU;QAC7B,OAAO,QAAQ,OAAO,CAAC,8KAAA,CAAA,YAAS,EAAE,IAAI;IACxC;AACF", "ignoreList": [0]}}, {"offset": {"line": 2155, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2161, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/node_modules/micromark-core-commonmark/dev/lib/code-fenced.js"], "sourcesContent": ["/**\n * @import {\n *   Code,\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding, markdownSpace} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nconst nonLazyContinuation = {\n  partial: true,\n  tokenize: tokenizeNonLazyContinuation\n}\n\n/** @type {Construct} */\nexport const codeFenced = {\n  concrete: true,\n  name: 'codeFenced',\n  tokenize: tokenizeCodeFenced\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeCodeFenced(effects, ok, nok) {\n  const self = this\n  /** @type {Construct} */\n  const closeStart = {partial: true, tokenize: tokenizeCloseStart}\n  let initialPrefix = 0\n  let sizeOpen = 0\n  /** @type {NonNullable<Code>} */\n  let marker\n\n  return start\n\n  /**\n   * Start of code.\n   *\n   * ```markdown\n   * > | ~~~js\n   *     ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    // To do: parse whitespace like `markdown-rs`.\n    return beforeSequenceOpen(code)\n  }\n\n  /**\n   * In opening fence, after prefix, at sequence.\n   *\n   * ```markdown\n   * > | ~~~js\n   *     ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function beforeSequenceOpen(code) {\n    assert(\n      code === codes.graveAccent || code === codes.tilde,\n      'expected `` ` `` or `~`'\n    )\n\n    const tail = self.events[self.events.length - 1]\n    initialPrefix =\n      tail && tail[1].type === types.linePrefix\n        ? tail[2].sliceSerialize(tail[1], true).length\n        : 0\n\n    marker = code\n    effects.enter(types.codeFenced)\n    effects.enter(types.codeFencedFence)\n    effects.enter(types.codeFencedFenceSequence)\n    return sequenceOpen(code)\n  }\n\n  /**\n   * In opening fence sequence.\n   *\n   * ```markdown\n   * > | ~~~js\n   *      ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceOpen(code) {\n    if (code === marker) {\n      sizeOpen++\n      effects.consume(code)\n      return sequenceOpen\n    }\n\n    if (sizeOpen < constants.codeFencedSequenceSizeMin) {\n      return nok(code)\n    }\n\n    effects.exit(types.codeFencedFenceSequence)\n    return markdownSpace(code)\n      ? factorySpace(effects, infoBefore, types.whitespace)(code)\n      : infoBefore(code)\n  }\n\n  /**\n   * In opening fence, after the sequence (and optional whitespace), before info.\n   *\n   * ```markdown\n   * > | ~~~js\n   *        ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function infoBefore(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.codeFencedFence)\n      return self.interrupt\n        ? ok(code)\n        : effects.check(nonLazyContinuation, atNonLazyBreak, after)(code)\n    }\n\n    effects.enter(types.codeFencedFenceInfo)\n    effects.enter(types.chunkString, {contentType: constants.contentTypeString})\n    return info(code)\n  }\n\n  /**\n   * In info.\n   *\n   * ```markdown\n   * > | ~~~js\n   *        ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function info(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.chunkString)\n      effects.exit(types.codeFencedFenceInfo)\n      return infoBefore(code)\n    }\n\n    if (markdownSpace(code)) {\n      effects.exit(types.chunkString)\n      effects.exit(types.codeFencedFenceInfo)\n      return factorySpace(effects, metaBefore, types.whitespace)(code)\n    }\n\n    if (code === codes.graveAccent && code === marker) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return info\n  }\n\n  /**\n   * In opening fence, after info and whitespace, before meta.\n   *\n   * ```markdown\n   * > | ~~~js eval\n   *           ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function metaBefore(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      return infoBefore(code)\n    }\n\n    effects.enter(types.codeFencedFenceMeta)\n    effects.enter(types.chunkString, {contentType: constants.contentTypeString})\n    return meta(code)\n  }\n\n  /**\n   * In meta.\n   *\n   * ```markdown\n   * > | ~~~js eval\n   *           ^\n   *   | alert(1)\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function meta(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.chunkString)\n      effects.exit(types.codeFencedFenceMeta)\n      return infoBefore(code)\n    }\n\n    if (code === codes.graveAccent && code === marker) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return meta\n  }\n\n  /**\n   * At eol/eof in code, before a non-lazy closing fence or content.\n   *\n   * ```markdown\n   * > | ~~~js\n   *          ^\n   * > | alert(1)\n   *             ^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function atNonLazyBreak(code) {\n    assert(markdownLineEnding(code), 'expected eol')\n    return effects.attempt(closeStart, after, contentBefore)(code)\n  }\n\n  /**\n   * Before code content, not a closing fence, at eol.\n   *\n   * ```markdown\n   *   | ~~~js\n   * > | alert(1)\n   *             ^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function contentBefore(code) {\n    assert(markdownLineEnding(code), 'expected eol')\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    return contentStart\n  }\n\n  /**\n   * Before code content, not a closing fence.\n   *\n   * ```markdown\n   *   | ~~~js\n   * > | alert(1)\n   *     ^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function contentStart(code) {\n    return initialPrefix > 0 && markdownSpace(code)\n      ? factorySpace(\n          effects,\n          beforeContentChunk,\n          types.linePrefix,\n          initialPrefix + 1\n        )(code)\n      : beforeContentChunk(code)\n  }\n\n  /**\n   * Before code content, after optional prefix.\n   *\n   * ```markdown\n   *   | ~~~js\n   * > | alert(1)\n   *     ^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function beforeContentChunk(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      return effects.check(nonLazyContinuation, atNonLazyBreak, after)(code)\n    }\n\n    effects.enter(types.codeFlowValue)\n    return contentChunk(code)\n  }\n\n  /**\n   * In code content.\n   *\n   * ```markdown\n   *   | ~~~js\n   * > | alert(1)\n   *     ^^^^^^^^\n   *   | ~~~\n   * ```\n   *\n   * @type {State}\n   */\n  function contentChunk(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      effects.exit(types.codeFlowValue)\n      return beforeContentChunk(code)\n    }\n\n    effects.consume(code)\n    return contentChunk\n  }\n\n  /**\n   * After code.\n   *\n   * ```markdown\n   *   | ~~~js\n   *   | alert(1)\n   * > | ~~~\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    effects.exit(types.codeFenced)\n    return ok(code)\n  }\n\n  /**\n   * @this {TokenizeContext}\n   *   Context.\n   * @type {Tokenizer}\n   */\n  function tokenizeCloseStart(effects, ok, nok) {\n    let size = 0\n\n    return startBefore\n\n    /**\n     *\n     *\n     * @type {State}\n     */\n    function startBefore(code) {\n      assert(markdownLineEnding(code), 'expected eol')\n      effects.enter(types.lineEnding)\n      effects.consume(code)\n      effects.exit(types.lineEnding)\n      return start\n    }\n\n    /**\n     * Before closing fence, at optional whitespace.\n     *\n     * ```markdown\n     *   | ~~~js\n     *   | alert(1)\n     * > | ~~~\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n    function start(code) {\n      // Always populated by defaults.\n      assert(\n        self.parser.constructs.disable.null,\n        'expected `disable.null` to be populated'\n      )\n\n      // To do: `enter` here or in next state?\n      effects.enter(types.codeFencedFence)\n      return markdownSpace(code)\n        ? factorySpace(\n            effects,\n            beforeSequenceClose,\n            types.linePrefix,\n            self.parser.constructs.disable.null.includes('codeIndented')\n              ? undefined\n              : constants.tabSize\n          )(code)\n        : beforeSequenceClose(code)\n    }\n\n    /**\n     * In closing fence, after optional whitespace, at sequence.\n     *\n     * ```markdown\n     *   | ~~~js\n     *   | alert(1)\n     * > | ~~~\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n    function beforeSequenceClose(code) {\n      if (code === marker) {\n        effects.enter(types.codeFencedFenceSequence)\n        return sequenceClose(code)\n      }\n\n      return nok(code)\n    }\n\n    /**\n     * In closing fence sequence.\n     *\n     * ```markdown\n     *   | ~~~js\n     *   | alert(1)\n     * > | ~~~\n     *     ^\n     * ```\n     *\n     * @type {State}\n     */\n    function sequenceClose(code) {\n      if (code === marker) {\n        size++\n        effects.consume(code)\n        return sequenceClose\n      }\n\n      if (size >= sizeOpen) {\n        effects.exit(types.codeFencedFenceSequence)\n        return markdownSpace(code)\n          ? factorySpace(effects, sequenceCloseAfter, types.whitespace)(code)\n          : sequenceCloseAfter(code)\n      }\n\n      return nok(code)\n    }\n\n    /**\n     * After closing fence sequence, after optional whitespace.\n     *\n     * ```markdown\n     *   | ~~~js\n     *   | alert(1)\n     * > | ~~~\n     *        ^\n     * ```\n     *\n     * @type {State}\n     */\n    function sequenceCloseAfter(code) {\n      if (code === codes.eof || markdownLineEnding(code)) {\n        effects.exit(types.codeFencedFence)\n        return ok(code)\n      }\n\n      return nok(code)\n    }\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeNonLazyContinuation(effects, ok, nok) {\n  const self = this\n\n  return start\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function start(code) {\n    if (code === codes.eof) {\n      return nok(code)\n    }\n\n    assert(markdownLineEnding(code), 'expected eol')\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    return lineStart\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function lineStart(code) {\n    return self.parser.lazy[self.now().line] ? nok(code) : ok(code)\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;AAED;AAGA;AAAA;AAAA;AADA;AADA;;;;;AAIA,sBAAsB,GACtB,MAAM,sBAAsB;IAC1B,SAAS;IACT,UAAU;AACZ;AAGO,MAAM,aAAa;IACxB,UAAU;IACV,MAAM;IACN,UAAU;AACZ;AAEA;;;;CAIC,GACD,SAAS,mBAAmB,OAAO,EAAE,EAAE,EAAE,GAAG;IAC1C,MAAM,OAAO,IAAI;IACjB,sBAAsB,GACtB,MAAM,aAAa;QAAC,SAAS;QAAM,UAAU;IAAkB;IAC/D,IAAI,gBAAgB;IACpB,IAAI,WAAW;IACf,8BAA8B,GAC9B,IAAI;IAEJ,OAAO;;IAEP;;;;;;;;;;;GAWC,GACD,SAAS,MAAM,IAAI;QACjB,8CAA8C;QAC9C,OAAO,mBAAmB;IAC5B;IAEA;;;;;;;;;;;GAWC,GACD,SAAS,mBAAmB,IAAI;QAC9B,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EACH,SAAS,2JAAA,CAAA,QAAK,CAAC,WAAW,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,KAAK,EAClD;QAGF,MAAM,OAAO,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC,MAAM,GAAG,EAAE;QAChD,gBACE,QAAQ,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,2JAAA,CAAA,QAAK,CAAC,UAAU,GACrC,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,MAAM,GAC5C;QAEN,SAAS;QACT,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,UAAU;QAC9B,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,eAAe;QACnC,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,uBAAuB;QAC3C,OAAO,aAAa;IACtB;IAEA;;;;;;;;;;;GAWC,GACD,SAAS,aAAa,IAAI;QACxB,IAAI,SAAS,QAAQ;YACnB;YACA,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,IAAI,WAAW,+JAAA,CAAA,YAAS,CAAC,yBAAyB,EAAE;YAClD,OAAO,IAAI;QACb;QAEA,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,uBAAuB;QAC1C,OAAO,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE,QACjB,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EAAE,SAAS,YAAY,2JAAA,CAAA,QAAK,CAAC,UAAU,EAAE,QACpD,WAAW;IACjB;IAEA;;;;;;;;;;;GAWC,GACD,SAAS,WAAW,IAAI;QACtB,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,IAAI,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAClD,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,eAAe;YAClC,OAAO,KAAK,SAAS,GACjB,GAAG,QACH,QAAQ,KAAK,CAAC,qBAAqB,gBAAgB,OAAO;QAChE;QAEA,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,mBAAmB;QACvC,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,WAAW,EAAE;YAAC,aAAa,+JAAA,CAAA,YAAS,CAAC,iBAAiB;QAAA;QAC1E,OAAO,KAAK;IACd;IAEA;;;;;;;;;;;GAWC,GACD,SAAS,KAAK,IAAI;QAChB,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,IAAI,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAClD,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,WAAW;YAC9B,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,mBAAmB;YACtC,OAAO,WAAW;QACpB;QAEA,IAAI,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE,OAAO;YACvB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,WAAW;YAC9B,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,mBAAmB;YACtC,OAAO,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EAAE,SAAS,YAAY,2JAAA,CAAA,QAAK,CAAC,UAAU,EAAE;QAC7D;QAEA,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,WAAW,IAAI,SAAS,QAAQ;YACjD,OAAO,IAAI;QACb;QAEA,QAAQ,OAAO,CAAC;QAChB,OAAO;IACT;IAEA;;;;;;;;;;;GAWC,GACD,SAAS,WAAW,IAAI;QACtB,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,IAAI,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAClD,OAAO,WAAW;QACpB;QAEA,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,mBAAmB;QACvC,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,WAAW,EAAE;YAAC,aAAa,+JAAA,CAAA,YAAS,CAAC,iBAAiB;QAAA;QAC1E,OAAO,KAAK;IACd;IAEA;;;;;;;;;;;GAWC,GACD,SAAS,KAAK,IAAI;QAChB,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,IAAI,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAClD,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,WAAW;YAC9B,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,mBAAmB;YACtC,OAAO,WAAW;QACpB;QAEA,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,WAAW,IAAI,SAAS,QAAQ;YACjD,OAAO,IAAI;QACb;QAEA,QAAQ,OAAO,CAAC;QAChB,OAAO;IACT;IAEA;;;;;;;;;;;;GAYC,GACD,SAAS,eAAe,IAAI;QAC1B,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;QACjC,OAAO,QAAQ,OAAO,CAAC,YAAY,OAAO,eAAe;IAC3D;IAEA;;;;;;;;;;;GAWC,GACD,SAAS,cAAc,IAAI;QACzB,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;QACjC,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,UAAU;QAC9B,QAAQ,OAAO,CAAC;QAChB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,UAAU;QAC7B,OAAO;IACT;IAEA;;;;;;;;;;;GAWC,GACD,SAAS,aAAa,IAAI;QACxB,OAAO,gBAAgB,KAAK,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE,QACtC,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EACT,SACA,oBACA,2JAAA,CAAA,QAAK,CAAC,UAAU,EAChB,gBAAgB,GAChB,QACF,mBAAmB;IACzB;IAEA;;;;;;;;;;;GAWC,GACD,SAAS,mBAAmB,IAAI;QAC9B,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,IAAI,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAClD,OAAO,QAAQ,KAAK,CAAC,qBAAqB,gBAAgB,OAAO;QACnE;QAEA,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,aAAa;QACjC,OAAO,aAAa;IACtB;IAEA;;;;;;;;;;;GAWC,GACD,SAAS,aAAa,IAAI;QACxB,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,IAAI,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAClD,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,aAAa;YAChC,OAAO,mBAAmB;QAC5B;QAEA,QAAQ,OAAO,CAAC;QAChB,OAAO;IACT;IAEA;;;;;;;;;;;GAWC,GACD,SAAS,MAAM,IAAI;QACjB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,UAAU;QAC7B,OAAO,GAAG;IACZ;IAEA;;;;GAIC,GACD,SAAS,mBAAmB,OAAO,EAAE,EAAE,EAAE,GAAG;QAC1C,IAAI,OAAO;QAEX,OAAO;;QAEP;;;;KAIC,GACD,SAAS,YAAY,IAAI;YACvB,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YACjC,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,UAAU;YAC9B,QAAQ,OAAO,CAAC;YAChB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,UAAU;YAC7B,OAAO;QACT;QAEA;;;;;;;;;;;KAWC,GACD,SAAS,MAAM,IAAI;YACjB,gCAAgC;YAChC,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EACH,KAAK,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,EACnC;YAGF,wCAAwC;YACxC,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,eAAe;YACnC,OAAO,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE,QACjB,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EACT,SACA,qBACA,2JAAA,CAAA,QAAK,CAAC,UAAU,EAChB,KAAK,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,kBACzC,YACA,+JAAA,CAAA,YAAS,CAAC,OAAO,EACrB,QACF,oBAAoB;QAC1B;QAEA;;;;;;;;;;;KAWC,GACD,SAAS,oBAAoB,IAAI;YAC/B,IAAI,SAAS,QAAQ;gBACnB,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,uBAAuB;gBAC3C,OAAO,cAAc;YACvB;YAEA,OAAO,IAAI;QACb;QAEA;;;;;;;;;;;KAWC,GACD,SAAS,cAAc,IAAI;YACzB,IAAI,SAAS,QAAQ;gBACnB;gBACA,QAAQ,OAAO,CAAC;gBAChB,OAAO;YACT;YAEA,IAAI,QAAQ,UAAU;gBACpB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,uBAAuB;gBAC1C,OAAO,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE,QACjB,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EAAE,SAAS,oBAAoB,2JAAA,CAAA,QAAK,CAAC,UAAU,EAAE,QAC5D,mBAAmB;YACzB;YAEA,OAAO,IAAI;QACb;QAEA;;;;;;;;;;;KAWC,GACD,SAAS,mBAAmB,IAAI;YAC9B,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,IAAI,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;gBAClD,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,eAAe;gBAClC,OAAO,GAAG;YACZ;YAEA,OAAO,IAAI;QACb;IACF;AACF;AAEA;;;;CAIC,GACD,SAAS,4BAA4B,OAAO,EAAE,EAAE,EAAE,GAAG;IACnD,MAAM,OAAO,IAAI;IAEjB,OAAO;;IAEP;;;;GAIC,GACD,SAAS,MAAM,IAAI;QACjB,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,EAAE;YACtB,OAAO,IAAI;QACb;QAEA,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;QACjC,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,UAAU;QAC9B,QAAQ,OAAO,CAAC;QAChB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,UAAU;QAC7B,OAAO;IACT;IAEA;;;;GAIC,GACD,SAAS,UAAU,IAAI;QACrB,OAAO,KAAK,MAAM,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,QAAQ,GAAG;IAC5D;AACF", "ignoreList": [0]}}, {"offset": {"line": 2587, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2593, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/node_modules/micromark-core-commonmark/dev/lib/character-reference.js"], "sourcesContent": ["/**\n * @import {\n *   Code,\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {decodeNamedCharacterReference} from 'decode-named-character-reference'\nimport {\n  asciiAlphanumeric,\n  asciiDigit,\n  asciiHexDigit\n} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const characterReference = {\n  name: 'characterReference',\n  tokenize: tokenizeCharacterReference\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeCharacterReference(effects, ok, nok) {\n  const self = this\n  let size = 0\n  /** @type {number} */\n  let max\n  /** @type {(code: Code) => boolean} */\n  let test\n\n  return start\n\n  /**\n   * Start of character reference.\n   *\n   * ```markdown\n   * > | a&amp;b\n   *      ^\n   * > | a&#123;b\n   *      ^\n   * > | a&#x9;b\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.ampersand, 'expected `&`')\n    effects.enter(types.characterReference)\n    effects.enter(types.characterReferenceMarker)\n    effects.consume(code)\n    effects.exit(types.characterReferenceMarker)\n    return open\n  }\n\n  /**\n   * After `&`, at `#` for numeric references or alphanumeric for named\n   * references.\n   *\n   * ```markdown\n   * > | a&amp;b\n   *       ^\n   * > | a&#123;b\n   *       ^\n   * > | a&#x9;b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    if (code === codes.numberSign) {\n      effects.enter(types.characterReferenceMarkerNumeric)\n      effects.consume(code)\n      effects.exit(types.characterReferenceMarkerNumeric)\n      return numeric\n    }\n\n    effects.enter(types.characterReferenceValue)\n    max = constants.characterReferenceNamedSizeMax\n    test = asciiAlphanumeric\n    return value(code)\n  }\n\n  /**\n   * After `#`, at `x` for hexadecimals or digit for decimals.\n   *\n   * ```markdown\n   * > | a&#123;b\n   *        ^\n   * > | a&#x9;b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function numeric(code) {\n    if (code === codes.uppercaseX || code === codes.lowercaseX) {\n      effects.enter(types.characterReferenceMarkerHexadecimal)\n      effects.consume(code)\n      effects.exit(types.characterReferenceMarkerHexadecimal)\n      effects.enter(types.characterReferenceValue)\n      max = constants.characterReferenceHexadecimalSizeMax\n      test = asciiHexDigit\n      return value\n    }\n\n    effects.enter(types.characterReferenceValue)\n    max = constants.characterReferenceDecimalSizeMax\n    test = asciiDigit\n    return value(code)\n  }\n\n  /**\n   * After markers (`&#x`, `&#`, or `&`), in value, before `;`.\n   *\n   * The character reference kind defines what and how many characters are\n   * allowed.\n   *\n   * ```markdown\n   * > | a&amp;b\n   *       ^^^\n   * > | a&#123;b\n   *        ^^^\n   * > | a&#x9;b\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function value(code) {\n    if (code === codes.semicolon && size) {\n      const token = effects.exit(types.characterReferenceValue)\n\n      if (\n        test === asciiAlphanumeric &&\n        !decodeNamedCharacterReference(self.sliceSerialize(token))\n      ) {\n        return nok(code)\n      }\n\n      // To do: `markdown-rs` uses a different name:\n      // `CharacterReferenceMarkerSemi`.\n      effects.enter(types.characterReferenceMarker)\n      effects.consume(code)\n      effects.exit(types.characterReferenceMarker)\n      effects.exit(types.characterReference)\n      return ok\n    }\n\n    if (test(code) && size++ < max) {\n      effects.consume(code)\n      return value\n    }\n\n    return nok(code)\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;AAED;AAOA;AAAA;AAAA;AALA;AADA;;;;;AASO,MAAM,qBAAqB;IAChC,MAAM;IACN,UAAU;AACZ;AAEA;;;;CAIC,GACD,SAAS,2BAA2B,OAAO,EAAE,EAAE,EAAE,GAAG;IAClD,MAAM,OAAO,IAAI;IACjB,IAAI,OAAO;IACX,mBAAmB,GACnB,IAAI;IACJ,oCAAoC,GACpC,IAAI;IAEJ,OAAO;;IAEP;;;;;;;;;;;;;GAaC,GACD,SAAS,MAAM,IAAI;QACjB,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,SAAS,2JAAA,CAAA,QAAK,CAAC,SAAS,EAAE;QACjC,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,kBAAkB;QACtC,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,wBAAwB;QAC5C,QAAQ,OAAO,CAAC;QAChB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,wBAAwB;QAC3C,OAAO;IACT;IAEA;;;;;;;;;;;;;;GAcC,GACD,SAAS,KAAK,IAAI;QAChB,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,UAAU,EAAE;YAC7B,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,+BAA+B;YACnD,QAAQ,OAAO,CAAC;YAChB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,+BAA+B;YAClD,OAAO;QACT;QAEA,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,uBAAuB;QAC3C,MAAM,+JAAA,CAAA,YAAS,CAAC,8BAA8B;QAC9C,OAAO,8JAAA,CAAA,oBAAiB;QACxB,OAAO,MAAM;IACf;IAEA;;;;;;;;;;;GAWC,GACD,SAAS,QAAQ,IAAI;QACnB,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,UAAU,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,UAAU,EAAE;YAC1D,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,mCAAmC;YACvD,QAAQ,OAAO,CAAC;YAChB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,mCAAmC;YACtD,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,uBAAuB;YAC3C,MAAM,+JAAA,CAAA,YAAS,CAAC,oCAAoC;YACpD,OAAO,8JAAA,CAAA,gBAAa;YACpB,OAAO;QACT;QAEA,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,uBAAuB;QAC3C,MAAM,+JAAA,CAAA,YAAS,CAAC,gCAAgC;QAChD,OAAO,8JAAA,CAAA,aAAU;QACjB,OAAO,MAAM;IACf;IAEA;;;;;;;;;;;;;;;;GAgBC,GACD,SAAS,MAAM,IAAI;QACjB,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,SAAS,IAAI,MAAM;YACpC,MAAM,QAAQ,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,uBAAuB;YAExD,IACE,SAAS,8JAAA,CAAA,oBAAiB,IAC1B,CAAC,CAAA,GAAA,kKAAA,CAAA,gCAA6B,AAAD,EAAE,KAAK,cAAc,CAAC,SACnD;gBACA,OAAO,IAAI;YACb;YAEA,8CAA8C;YAC9C,kCAAkC;YAClC,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,wBAAwB;YAC5C,QAAQ,OAAO,CAAC;YAChB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,wBAAwB;YAC3C,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,kBAAkB;YACrC,OAAO;QACT;QAEA,IAAI,KAAK,SAAS,SAAS,KAAK;YAC9B,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,OAAO,IAAI;IACb;AACF", "ignoreList": [0]}}, {"offset": {"line": 2739, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2745, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/node_modules/micromark-core-commonmark/dev/lib/character-escape.js"], "sourcesContent": ["/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {asciiPunctuation} from 'micromark-util-character'\nimport {codes, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const characterEscape = {\n  name: 'characterEscape',\n  tokenize: tokenizeCharacterEscape\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeCharacterEscape(effects, ok, nok) {\n  return start\n\n  /**\n   * Start of character escape.\n   *\n   * ```markdown\n   * > | a\\*b\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.backslash, 'expected `\\\\`')\n    effects.enter(types.characterEscape)\n    effects.enter(types.escapeMarker)\n    effects.consume(code)\n    effects.exit(types.escapeMarker)\n    return inside\n  }\n\n  /**\n   * After `\\`, at punctuation.\n   *\n   * ```markdown\n   * > | a\\*b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    // ASCII punctuation.\n    if (asciiPunctuation(code)) {\n      effects.enter(types.characterEscapeValue)\n      effects.consume(code)\n      effects.exit(types.characterEscapeValue)\n      effects.exit(types.characterEscape)\n      return ok\n    }\n\n    return nok(code)\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AAED;AAEA;AAAA;AADA;;;;AAIO,MAAM,kBAAkB;IAC7B,MAAM;IACN,UAAU;AACZ;AAEA;;;;CAIC,GACD,SAAS,wBAAwB,OAAO,EAAE,EAAE,EAAE,GAAG;IAC/C,OAAO;;IAEP;;;;;;;;;GASC,GACD,SAAS,MAAM,IAAI;QACjB,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,SAAS,2JAAA,CAAA,QAAK,CAAC,SAAS,EAAE;QACjC,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,eAAe;QACnC,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,YAAY;QAChC,QAAQ,OAAO,CAAC;QAChB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,YAAY;QAC/B,OAAO;IACT;IAEA;;;;;;;;;GASC,GACD,SAAS,OAAO,IAAI;QAClB,qBAAqB;QACrB,IAAI,CAAA,GAAA,8JAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO;YAC1B,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,oBAAoB;YACxC,QAAQ,OAAO,CAAC;YAChB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,oBAAoB;YACvC,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,eAAe;YAClC,OAAO;QACT;QAEA,OAAO,IAAI;IACb;AACF", "ignoreList": [0]}}, {"offset": {"line": 2811, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2817, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/node_modules/micromark-core-commonmark/dev/lib/line-ending.js"], "sourcesContent": ["/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const lineEnding = {name: 'lineEnding', tokenize: tokenizeLineEnding}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeLineEnding(effects, ok) {\n  return start\n\n  /** @type {State} */\n  function start(code) {\n    assert(markdownLineEnding(code), 'expected eol')\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    return factorySpace(effects, ok, types.linePrefix)\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AAED;AAEA;AACA;AAFA;;;;;AAKO,MAAM,aAAa;IAAC,MAAM;IAAc,UAAU;AAAkB;AAE3E;;;;CAIC,GACD,SAAS,mBAAmB,OAAO,EAAE,EAAE;IACrC,OAAO;;IAEP,kBAAkB,GAClB,SAAS,MAAM,IAAI;QACjB,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;QACjC,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,UAAU;QAC9B,QAAQ,OAAO,CAAC;QAChB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,UAAU;QAC7B,OAAO,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EAAE,SAAS,IAAI,2JAAA,CAAA,QAAK,CAAC,UAAU;IACnD;AACF", "ignoreList": [0]}}, {"offset": {"line": 2854, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2860, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/node_modules/micromark-core-commonmark/dev/lib/label-end.js"], "sourcesContent": ["/**\n * @import {\n *   Construct,\n *   Event,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer,\n *   Token\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factoryDestination} from 'micromark-factory-destination'\nimport {factoryLabel} from 'micromark-factory-label'\nimport {factoryTitle} from 'micromark-factory-title'\nimport {factoryWhitespace} from 'micromark-factory-whitespace'\nimport {markdownLineEndingOrSpace} from 'micromark-util-character'\nimport {push, splice} from 'micromark-util-chunked'\nimport {normalizeIdentifier} from 'micromark-util-normalize-identifier'\nimport {resolveAll} from 'micromark-util-resolve-all'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const labelEnd = {\n  name: 'labelEnd',\n  resolveAll: resolveAllLabelEnd,\n  resolveTo: resolveToLabelEnd,\n  tokenize: tokenizeLabelEnd\n}\n\n/** @type {Construct} */\nconst resourceConstruct = {tokenize: tokenizeResource}\n/** @type {Construct} */\nconst referenceFullConstruct = {tokenize: tokenizeReferenceFull}\n/** @type {Construct} */\nconst referenceCollapsedConstruct = {tokenize: tokenizeReferenceCollapsed}\n\n/** @type {Resolver} */\nfunction resolveAllLabelEnd(events) {\n  let index = -1\n  /** @type {Array<Event>} */\n  const newEvents = []\n  while (++index < events.length) {\n    const token = events[index][1]\n    newEvents.push(events[index])\n\n    if (\n      token.type === types.labelImage ||\n      token.type === types.labelLink ||\n      token.type === types.labelEnd\n    ) {\n      // Remove the marker.\n      const offset = token.type === types.labelImage ? 4 : 2\n      token.type = types.data\n      index += offset\n    }\n  }\n\n  // If the events are equal, we don't have to copy newEvents to events\n  if (events.length !== newEvents.length) {\n    splice(events, 0, events.length, newEvents)\n  }\n\n  return events\n}\n\n/** @type {Resolver} */\nfunction resolveToLabelEnd(events, context) {\n  let index = events.length\n  let offset = 0\n  /** @type {Token} */\n  let token\n  /** @type {number | undefined} */\n  let open\n  /** @type {number | undefined} */\n  let close\n  /** @type {Array<Event>} */\n  let media\n\n  // Find an opening.\n  while (index--) {\n    token = events[index][1]\n\n    if (open) {\n      // If we see another link, or inactive link label, we’ve been here before.\n      if (\n        token.type === types.link ||\n        (token.type === types.labelLink && token._inactive)\n      ) {\n        break\n      }\n\n      // Mark other link openings as inactive, as we can’t have links in\n      // links.\n      if (events[index][0] === 'enter' && token.type === types.labelLink) {\n        token._inactive = true\n      }\n    } else if (close) {\n      if (\n        events[index][0] === 'enter' &&\n        (token.type === types.labelImage || token.type === types.labelLink) &&\n        !token._balanced\n      ) {\n        open = index\n\n        if (token.type !== types.labelLink) {\n          offset = 2\n          break\n        }\n      }\n    } else if (token.type === types.labelEnd) {\n      close = index\n    }\n  }\n\n  assert(open !== undefined, '`open` is supposed to be found')\n  assert(close !== undefined, '`close` is supposed to be found')\n\n  const group = {\n    type: events[open][1].type === types.labelLink ? types.link : types.image,\n    start: {...events[open][1].start},\n    end: {...events[events.length - 1][1].end}\n  }\n\n  const label = {\n    type: types.label,\n    start: {...events[open][1].start},\n    end: {...events[close][1].end}\n  }\n\n  const text = {\n    type: types.labelText,\n    start: {...events[open + offset + 2][1].end},\n    end: {...events[close - 2][1].start}\n  }\n\n  media = [\n    ['enter', group, context],\n    ['enter', label, context]\n  ]\n\n  // Opening marker.\n  media = push(media, events.slice(open + 1, open + offset + 3))\n\n  // Text open.\n  media = push(media, [['enter', text, context]])\n\n  // Always populated by defaults.\n  assert(\n    context.parser.constructs.insideSpan.null,\n    'expected `insideSpan.null` to be populated'\n  )\n  // Between.\n  media = push(\n    media,\n    resolveAll(\n      context.parser.constructs.insideSpan.null,\n      events.slice(open + offset + 4, close - 3),\n      context\n    )\n  )\n\n  // Text close, marker close, label close.\n  media = push(media, [\n    ['exit', text, context],\n    events[close - 2],\n    events[close - 1],\n    ['exit', label, context]\n  ])\n\n  // Reference, resource, or so.\n  media = push(media, events.slice(close + 1))\n\n  // Media close.\n  media = push(media, [['exit', group, context]])\n\n  splice(events, open, events.length, media)\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeLabelEnd(effects, ok, nok) {\n  const self = this\n  let index = self.events.length\n  /** @type {Token} */\n  let labelStart\n  /** @type {boolean} */\n  let defined\n\n  // Find an opening.\n  while (index--) {\n    if (\n      (self.events[index][1].type === types.labelImage ||\n        self.events[index][1].type === types.labelLink) &&\n      !self.events[index][1]._balanced\n    ) {\n      labelStart = self.events[index][1]\n      break\n    }\n  }\n\n  return start\n\n  /**\n   * Start of label end.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *       ^\n   * > | [a][b] c\n   *       ^\n   * > | [a][] b\n   *       ^\n   * > | [a] b\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.rightSquareBracket, 'expected `]`')\n\n    // If there is not an okay opening.\n    if (!labelStart) {\n      return nok(code)\n    }\n\n    // If the corresponding label (link) start is marked as inactive,\n    // it means we’d be wrapping a link, like this:\n    //\n    // ```markdown\n    // > | a [b [c](d) e](f) g.\n    //                  ^\n    // ```\n    //\n    // We can’t have that, so it’s just balanced brackets.\n    if (labelStart._inactive) {\n      return labelEndNok(code)\n    }\n\n    defined = self.parser.defined.includes(\n      normalizeIdentifier(\n        self.sliceSerialize({start: labelStart.end, end: self.now()})\n      )\n    )\n    effects.enter(types.labelEnd)\n    effects.enter(types.labelMarker)\n    effects.consume(code)\n    effects.exit(types.labelMarker)\n    effects.exit(types.labelEnd)\n    return after\n  }\n\n  /**\n   * After `]`.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *       ^\n   * > | [a][b] c\n   *       ^\n   * > | [a][] b\n   *       ^\n   * > | [a] b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    // Note: `markdown-rs` also parses GFM footnotes here, which for us is in\n    // an extension.\n\n    // Resource (`[asd](fgh)`)?\n    if (code === codes.leftParenthesis) {\n      return effects.attempt(\n        resourceConstruct,\n        labelEndOk,\n        defined ? labelEndOk : labelEndNok\n      )(code)\n    }\n\n    // Full (`[asd][fgh]`) or collapsed (`[asd][]`) reference?\n    if (code === codes.leftSquareBracket) {\n      return effects.attempt(\n        referenceFullConstruct,\n        labelEndOk,\n        defined ? referenceNotFull : labelEndNok\n      )(code)\n    }\n\n    // Shortcut (`[asd]`) reference?\n    return defined ? labelEndOk(code) : labelEndNok(code)\n  }\n\n  /**\n   * After `]`, at `[`, but not at a full reference.\n   *\n   * > 👉 **Note**: we only get here if the label is defined.\n   *\n   * ```markdown\n   * > | [a][] b\n   *        ^\n   * > | [a] b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceNotFull(code) {\n    return effects.attempt(\n      referenceCollapsedConstruct,\n      labelEndOk,\n      labelEndNok\n    )(code)\n  }\n\n  /**\n   * Done, we found something.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *           ^\n   * > | [a][b] c\n   *           ^\n   * > | [a][] b\n   *          ^\n   * > | [a] b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelEndOk(code) {\n    // Note: `markdown-rs` does a bunch of stuff here.\n    return ok(code)\n  }\n\n  /**\n   * Done, it’s nothing.\n   *\n   * There was an okay opening, but we didn’t match anything.\n   *\n   * ```markdown\n   * > | [a](b c\n   *        ^\n   * > | [a][b c\n   *        ^\n   * > | [a] b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function labelEndNok(code) {\n    labelStart._balanced = true\n    return nok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeResource(effects, ok, nok) {\n  return resourceStart\n\n  /**\n   * At a resource.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceStart(code) {\n    assert(code === codes.leftParenthesis, 'expected left paren')\n    effects.enter(types.resource)\n    effects.enter(types.resourceMarker)\n    effects.consume(code)\n    effects.exit(types.resourceMarker)\n    return resourceBefore\n  }\n\n  /**\n   * In resource, after `(`, at optional whitespace.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceBefore(code) {\n    return markdownLineEndingOrSpace(code)\n      ? factoryWhitespace(effects, resourceOpen)(code)\n      : resourceOpen(code)\n  }\n\n  /**\n   * In resource, after optional whitespace, at `)` or a destination.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceOpen(code) {\n    if (code === codes.rightParenthesis) {\n      return resourceEnd(code)\n    }\n\n    return factoryDestination(\n      effects,\n      resourceDestinationAfter,\n      resourceDestinationMissing,\n      types.resourceDestination,\n      types.resourceDestinationLiteral,\n      types.resourceDestinationLiteralMarker,\n      types.resourceDestinationRaw,\n      types.resourceDestinationString,\n      constants.linkResourceDestinationBalanceMax\n    )(code)\n  }\n\n  /**\n   * In resource, after destination, at optional whitespace.\n   *\n   * ```markdown\n   * > | [a](b) c\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceDestinationAfter(code) {\n    return markdownLineEndingOrSpace(code)\n      ? factoryWhitespace(effects, resourceBetween)(code)\n      : resourceEnd(code)\n  }\n\n  /**\n   * At invalid destination.\n   *\n   * ```markdown\n   * > | [a](<<) b\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceDestinationMissing(code) {\n    return nok(code)\n  }\n\n  /**\n   * In resource, after destination and whitespace, at `(` or title.\n   *\n   * ```markdown\n   * > | [a](b ) c\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceBetween(code) {\n    if (\n      code === codes.quotationMark ||\n      code === codes.apostrophe ||\n      code === codes.leftParenthesis\n    ) {\n      return factoryTitle(\n        effects,\n        resourceTitleAfter,\n        nok,\n        types.resourceTitle,\n        types.resourceTitleMarker,\n        types.resourceTitleString\n      )(code)\n    }\n\n    return resourceEnd(code)\n  }\n\n  /**\n   * In resource, after title, at optional whitespace.\n   *\n   * ```markdown\n   * > | [a](b \"c\") d\n   *              ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceTitleAfter(code) {\n    return markdownLineEndingOrSpace(code)\n      ? factoryWhitespace(effects, resourceEnd)(code)\n      : resourceEnd(code)\n  }\n\n  /**\n   * In resource, at `)`.\n   *\n   * ```markdown\n   * > | [a](b) d\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function resourceEnd(code) {\n    if (code === codes.rightParenthesis) {\n      effects.enter(types.resourceMarker)\n      effects.consume(code)\n      effects.exit(types.resourceMarker)\n      effects.exit(types.resource)\n      return ok\n    }\n\n    return nok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeReferenceFull(effects, ok, nok) {\n  const self = this\n\n  return referenceFull\n\n  /**\n   * In a reference (full), at the `[`.\n   *\n   * ```markdown\n   * > | [a][b] d\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceFull(code) {\n    assert(code === codes.leftSquareBracket, 'expected left bracket')\n    return factoryLabel.call(\n      self,\n      effects,\n      referenceFullAfter,\n      referenceFullMissing,\n      types.reference,\n      types.referenceMarker,\n      types.referenceString\n    )(code)\n  }\n\n  /**\n   * In a reference (full), after `]`.\n   *\n   * ```markdown\n   * > | [a][b] d\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceFullAfter(code) {\n    return self.parser.defined.includes(\n      normalizeIdentifier(\n        self.sliceSerialize(self.events[self.events.length - 1][1]).slice(1, -1)\n      )\n    )\n      ? ok(code)\n      : nok(code)\n  }\n\n  /**\n   * In reference (full) that was missing.\n   *\n   * ```markdown\n   * > | [a][b d\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceFullMissing(code) {\n    return nok(code)\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeReferenceCollapsed(effects, ok, nok) {\n  return referenceCollapsedStart\n\n  /**\n   * In reference (collapsed), at `[`.\n   *\n   * > 👉 **Note**: we only get here if the label is defined.\n   *\n   * ```markdown\n   * > | [a][] d\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function referenceCollapsedStart(code) {\n    // We only attempt a collapsed label if there’s a `[`.\n    assert(code === codes.leftSquareBracket, 'expected left bracket')\n    effects.enter(types.reference)\n    effects.enter(types.referenceMarker)\n    effects.consume(code)\n    effects.exit(types.referenceMarker)\n    return referenceCollapsedOpen\n  }\n\n  /**\n   * In reference (collapsed), at `]`.\n   *\n   * > 👉 **Note**: we only get here if the label is defined.\n   *\n   * ```markdown\n   * > | [a][] d\n   *         ^\n   * ```\n   *\n   *  @type {State}\n   */\n  function referenceCollapsedOpen(code) {\n    if (code === codes.rightSquareBracket) {\n      effects.enter(types.referenceMarker)\n      effects.consume(code)\n      effects.exit(types.referenceMarker)\n      effects.exit(types.reference)\n      return ok\n    }\n\n    return nok(code)\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;AAWD;AAHA;AANA;AAQA;AACA;AAFA;AAFA;AADA;AAHA;AAQA;AANA;AADA;;;;;;;;;;;AAUO,MAAM,WAAW;IACtB,MAAM;IACN,YAAY;IACZ,WAAW;IACX,UAAU;AACZ;AAEA,sBAAsB,GACtB,MAAM,oBAAoB;IAAC,UAAU;AAAgB;AACrD,sBAAsB,GACtB,MAAM,yBAAyB;IAAC,UAAU;AAAqB;AAC/D,sBAAsB,GACtB,MAAM,8BAA8B;IAAC,UAAU;AAA0B;AAEzE,qBAAqB,GACrB,SAAS,mBAAmB,MAAM;IAChC,IAAI,QAAQ,CAAC;IACb,yBAAyB,GACzB,MAAM,YAAY,EAAE;IACpB,MAAO,EAAE,QAAQ,OAAO,MAAM,CAAE;QAC9B,MAAM,QAAQ,MAAM,CAAC,MAAM,CAAC,EAAE;QAC9B,UAAU,IAAI,CAAC,MAAM,CAAC,MAAM;QAE5B,IACE,MAAM,IAAI,KAAK,2JAAA,CAAA,QAAK,CAAC,UAAU,IAC/B,MAAM,IAAI,KAAK,2JAAA,CAAA,QAAK,CAAC,SAAS,IAC9B,MAAM,IAAI,KAAK,2JAAA,CAAA,QAAK,CAAC,QAAQ,EAC7B;YACA,qBAAqB;YACrB,MAAM,SAAS,MAAM,IAAI,KAAK,2JAAA,CAAA,QAAK,CAAC,UAAU,GAAG,IAAI;YACrD,MAAM,IAAI,GAAG,2JAAA,CAAA,QAAK,CAAC,IAAI;YACvB,SAAS;QACX;IACF;IAEA,qEAAqE;IACrE,IAAI,OAAO,MAAM,KAAK,UAAU,MAAM,EAAE;QACtC,CAAA,GAAA,4JAAA,CAAA,SAAM,AAAD,EAAE,QAAQ,GAAG,OAAO,MAAM,EAAE;IACnC;IAEA,OAAO;AACT;AAEA,qBAAqB,GACrB,SAAS,kBAAkB,MAAM,EAAE,OAAO;IACxC,IAAI,QAAQ,OAAO,MAAM;IACzB,IAAI,SAAS;IACb,kBAAkB,GAClB,IAAI;IACJ,+BAA+B,GAC/B,IAAI;IACJ,+BAA+B,GAC/B,IAAI;IACJ,yBAAyB,GACzB,IAAI;IAEJ,mBAAmB;IACnB,MAAO,QAAS;QACd,QAAQ,MAAM,CAAC,MAAM,CAAC,EAAE;QAExB,IAAI,MAAM;YACR,0EAA0E;YAC1E,IACE,MAAM,IAAI,KAAK,2JAAA,CAAA,QAAK,CAAC,IAAI,IACxB,MAAM,IAAI,KAAK,2JAAA,CAAA,QAAK,CAAC,SAAS,IAAI,MAAM,SAAS,EAClD;gBACA;YACF;YAEA,kEAAkE;YAClE,SAAS;YACT,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,KAAK,WAAW,MAAM,IAAI,KAAK,2JAAA,CAAA,QAAK,CAAC,SAAS,EAAE;gBAClE,MAAM,SAAS,GAAG;YACpB;QACF,OAAO,IAAI,OAAO;YAChB,IACE,MAAM,CAAC,MAAM,CAAC,EAAE,KAAK,WACrB,CAAC,MAAM,IAAI,KAAK,2JAAA,CAAA,QAAK,CAAC,UAAU,IAAI,MAAM,IAAI,KAAK,2JAAA,CAAA,QAAK,CAAC,SAAS,KAClE,CAAC,MAAM,SAAS,EAChB;gBACA,OAAO;gBAEP,IAAI,MAAM,IAAI,KAAK,2JAAA,CAAA,QAAK,CAAC,SAAS,EAAE;oBAClC,SAAS;oBACT;gBACF;YACF;QACF,OAAO,IAAI,MAAM,IAAI,KAAK,2JAAA,CAAA,QAAK,CAAC,QAAQ,EAAE;YACxC,QAAQ;QACV;IACF;IAEA,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,SAAS,WAAW;IAC3B,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,UAAU,WAAW;IAE5B,MAAM,QAAQ;QACZ,MAAM,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,2JAAA,CAAA,QAAK,CAAC,SAAS,GAAG,2JAAA,CAAA,QAAK,CAAC,IAAI,GAAG,2JAAA,CAAA,QAAK,CAAC,KAAK;QACzE,OAAO;YAAC,GAAG,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK;QAAA;QAChC,KAAK;YAAC,GAAG,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG;QAAA;IAC3C;IAEA,MAAM,QAAQ;QACZ,MAAM,2JAAA,CAAA,QAAK,CAAC,KAAK;QACjB,OAAO;YAAC,GAAG,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK;QAAA;QAChC,KAAK;YAAC,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG;QAAA;IAC/B;IAEA,MAAM,OAAO;QACX,MAAM,2JAAA,CAAA,QAAK,CAAC,SAAS;QACrB,OAAO;YAAC,GAAG,MAAM,CAAC,OAAO,SAAS,EAAE,CAAC,EAAE,CAAC,GAAG;QAAA;QAC3C,KAAK;YAAC,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,KAAK;QAAA;IACrC;IAEA,QAAQ;QACN;YAAC;YAAS;YAAO;SAAQ;QACzB;YAAC;YAAS;YAAO;SAAQ;KAC1B;IAED,kBAAkB;IAClB,QAAQ,CAAA,GAAA,4JAAA,CAAA,OAAI,AAAD,EAAE,OAAO,OAAO,KAAK,CAAC,OAAO,GAAG,OAAO,SAAS;IAE3D,aAAa;IACb,QAAQ,CAAA,GAAA,4JAAA,CAAA,OAAI,AAAD,EAAE,OAAO;QAAC;YAAC;YAAS;YAAM;SAAQ;KAAC;IAE9C,gCAAgC;IAChC,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EACH,QAAQ,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,EACzC;IAEF,WAAW;IACX,QAAQ,CAAA,GAAA,4JAAA,CAAA,OAAI,AAAD,EACT,OACA,CAAA,GAAA,4JAAA,CAAA,aAAU,AAAD,EACP,QAAQ,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,EACzC,OAAO,KAAK,CAAC,OAAO,SAAS,GAAG,QAAQ,IACxC;IAIJ,yCAAyC;IACzC,QAAQ,CAAA,GAAA,4JAAA,CAAA,OAAI,AAAD,EAAE,OAAO;QAClB;YAAC;YAAQ;YAAM;SAAQ;QACvB,MAAM,CAAC,QAAQ,EAAE;QACjB,MAAM,CAAC,QAAQ,EAAE;QACjB;YAAC;YAAQ;YAAO;SAAQ;KACzB;IAED,8BAA8B;IAC9B,QAAQ,CAAA,GAAA,4JAAA,CAAA,OAAI,AAAD,EAAE,OAAO,OAAO,KAAK,CAAC,QAAQ;IAEzC,eAAe;IACf,QAAQ,CAAA,GAAA,4JAAA,CAAA,OAAI,AAAD,EAAE,OAAO;QAAC;YAAC;YAAQ;YAAO;SAAQ;KAAC;IAE9C,CAAA,GAAA,4JAAA,CAAA,SAAM,AAAD,EAAE,QAAQ,MAAM,OAAO,MAAM,EAAE;IAEpC,OAAO;AACT;AAEA;;;;CAIC,GACD,SAAS,iBAAiB,OAAO,EAAE,EAAE,EAAE,GAAG;IACxC,MAAM,OAAO,IAAI;IACjB,IAAI,QAAQ,KAAK,MAAM,CAAC,MAAM;IAC9B,kBAAkB,GAClB,IAAI;IACJ,oBAAoB,GACpB,IAAI;IAEJ,mBAAmB;IACnB,MAAO,QAAS;QACd,IACE,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,2JAAA,CAAA,QAAK,CAAC,UAAU,IAC9C,KAAK,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,2JAAA,CAAA,QAAK,CAAC,SAAS,KAChD,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,EAChC;YACA,aAAa,KAAK,MAAM,CAAC,MAAM,CAAC,EAAE;YAClC;QACF;IACF;IAEA,OAAO;;IAEP;;;;;;;;;;;;;;GAcC,GACD,SAAS,MAAM,IAAI;QACjB,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,SAAS,2JAAA,CAAA,QAAK,CAAC,kBAAkB,EAAE;QAE1C,mCAAmC;QACnC,IAAI,CAAC,YAAY;YACf,OAAO,IAAI;QACb;QAEA,iEAAiE;QACjE,+CAA+C;QAC/C,EAAE;QACF,cAAc;QACd,2BAA2B;QAC3B,qBAAqB;QACrB,MAAM;QACN,EAAE;QACF,sDAAsD;QACtD,IAAI,WAAW,SAAS,EAAE;YACxB,OAAO,YAAY;QACrB;QAEA,UAAU,KAAK,MAAM,CAAC,OAAO,CAAC,QAAQ,CACpC,CAAA,GAAA,4KAAA,CAAA,sBAAmB,AAAD,EAChB,KAAK,cAAc,CAAC;YAAC,OAAO,WAAW,GAAG;YAAE,KAAK,KAAK,GAAG;QAAE;QAG/D,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,QAAQ;QAC5B,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,WAAW;QAC/B,QAAQ,OAAO,CAAC;QAChB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,WAAW;QAC9B,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,QAAQ;QAC3B,OAAO;IACT;IAEA;;;;;;;;;;;;;;;GAeC,GACD,SAAS,MAAM,IAAI;QACjB,yEAAyE;QACzE,gBAAgB;QAEhB,2BAA2B;QAC3B,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,eAAe,EAAE;YAClC,OAAO,QAAQ,OAAO,CACpB,mBACA,YACA,UAAU,aAAa,aACvB;QACJ;QAEA,0DAA0D;QAC1D,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,iBAAiB,EAAE;YACpC,OAAO,QAAQ,OAAO,CACpB,wBACA,YACA,UAAU,mBAAmB,aAC7B;QACJ;QAEA,gCAAgC;QAChC,OAAO,UAAU,WAAW,QAAQ,YAAY;IAClD;IAEA;;;;;;;;;;;;;GAaC,GACD,SAAS,iBAAiB,IAAI;QAC5B,OAAO,QAAQ,OAAO,CACpB,6BACA,YACA,aACA;IACJ;IAEA;;;;;;;;;;;;;;;GAeC,GACD,SAAS,WAAW,IAAI;QACtB,kDAAkD;QAClD,OAAO,GAAG;IACZ;IAEA;;;;;;;;;;;;;;;GAeC,GACD,SAAS,YAAY,IAAI;QACvB,WAAW,SAAS,GAAG;QACvB,OAAO,IAAI;IACb;AACF;AAEA;;;;CAIC,GACD,SAAS,iBAAiB,OAAO,EAAE,EAAE,EAAE,GAAG;IACxC,OAAO;;IAEP;;;;;;;;;GASC,GACD,SAAS,cAAc,IAAI;QACzB,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,SAAS,2JAAA,CAAA,QAAK,CAAC,eAAe,EAAE;QACvC,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,QAAQ;QAC5B,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,cAAc;QAClC,QAAQ,OAAO,CAAC;QAChB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,cAAc;QACjC,OAAO;IACT;IAEA;;;;;;;;;GASC,GACD,SAAS,eAAe,IAAI;QAC1B,OAAO,CAAA,GAAA,8JAAA,CAAA,4BAAyB,AAAD,EAAE,QAC7B,CAAA,GAAA,kKAAA,CAAA,oBAAiB,AAAD,EAAE,SAAS,cAAc,QACzC,aAAa;IACnB;IAEA;;;;;;;;;GASC,GACD,SAAS,aAAa,IAAI;QACxB,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,gBAAgB,EAAE;YACnC,OAAO,YAAY;QACrB;QAEA,OAAO,CAAA,GAAA,mKAAA,CAAA,qBAAkB,AAAD,EACtB,SACA,0BACA,4BACA,2JAAA,CAAA,QAAK,CAAC,mBAAmB,EACzB,2JAAA,CAAA,QAAK,CAAC,0BAA0B,EAChC,2JAAA,CAAA,QAAK,CAAC,gCAAgC,EACtC,2JAAA,CAAA,QAAK,CAAC,sBAAsB,EAC5B,2JAAA,CAAA,QAAK,CAAC,yBAAyB,EAC/B,+JAAA,CAAA,YAAS,CAAC,iCAAiC,EAC3C;IACJ;IAEA;;;;;;;;;GASC,GACD,SAAS,yBAAyB,IAAI;QACpC,OAAO,CAAA,GAAA,8JAAA,CAAA,4BAAyB,AAAD,EAAE,QAC7B,CAAA,GAAA,kKAAA,CAAA,oBAAiB,AAAD,EAAE,SAAS,iBAAiB,QAC5C,YAAY;IAClB;IAEA;;;;;;;;;GASC,GACD,SAAS,2BAA2B,IAAI;QACtC,OAAO,IAAI;IACb;IAEA;;;;;;;;;GASC,GACD,SAAS,gBAAgB,IAAI;QAC3B,IACE,SAAS,2JAAA,CAAA,QAAK,CAAC,aAAa,IAC5B,SAAS,2JAAA,CAAA,QAAK,CAAC,UAAU,IACzB,SAAS,2JAAA,CAAA,QAAK,CAAC,eAAe,EAC9B;YACA,OAAO,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EAChB,SACA,oBACA,KACA,2JAAA,CAAA,QAAK,CAAC,aAAa,EACnB,2JAAA,CAAA,QAAK,CAAC,mBAAmB,EACzB,2JAAA,CAAA,QAAK,CAAC,mBAAmB,EACzB;QACJ;QAEA,OAAO,YAAY;IACrB;IAEA;;;;;;;;;GASC,GACD,SAAS,mBAAmB,IAAI;QAC9B,OAAO,CAAA,GAAA,8JAAA,CAAA,4BAAyB,AAAD,EAAE,QAC7B,CAAA,GAAA,kKAAA,CAAA,oBAAiB,AAAD,EAAE,SAAS,aAAa,QACxC,YAAY;IAClB;IAEA;;;;;;;;;GASC,GACD,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,gBAAgB,EAAE;YACnC,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,cAAc;YAClC,QAAQ,OAAO,CAAC;YAChB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,cAAc;YACjC,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,QAAQ;YAC3B,OAAO;QACT;QAEA,OAAO,IAAI;IACb;AACF;AAEA;;;;CAIC,GACD,SAAS,sBAAsB,OAAO,EAAE,EAAE,EAAE,GAAG;IAC7C,MAAM,OAAO,IAAI;IAEjB,OAAO;;IAEP;;;;;;;;;GASC,GACD,SAAS,cAAc,IAAI;QACzB,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,SAAS,2JAAA,CAAA,QAAK,CAAC,iBAAiB,EAAE;QACzC,OAAO,6JAAA,CAAA,eAAY,CAAC,IAAI,CACtB,MACA,SACA,oBACA,sBACA,2JAAA,CAAA,QAAK,CAAC,SAAS,EACf,2JAAA,CAAA,QAAK,CAAC,eAAe,EACrB,2JAAA,CAAA,QAAK,CAAC,eAAe,EACrB;IACJ;IAEA;;;;;;;;;GASC,GACD,SAAS,mBAAmB,IAAI;QAC9B,OAAO,KAAK,MAAM,CAAC,OAAO,CAAC,QAAQ,CACjC,CAAA,GAAA,4KAAA,CAAA,sBAAmB,AAAD,EAChB,KAAK,cAAc,CAAC,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC,MAAM,GAAG,EAAE,CAAC,EAAE,EAAE,KAAK,CAAC,GAAG,CAAC,OAGtE,GAAG,QACH,IAAI;IACV;IAEA;;;;;;;;;GASC,GACD,SAAS,qBAAqB,IAAI;QAChC,OAAO,IAAI;IACb;AACF;AAEA;;;;CAIC,GACD,SAAS,2BAA2B,OAAO,EAAE,EAAE,EAAE,GAAG;IAClD,OAAO;;IAEP;;;;;;;;;;;GAWC,GACD,SAAS,wBAAwB,IAAI;QACnC,sDAAsD;QACtD,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,SAAS,2JAAA,CAAA,QAAK,CAAC,iBAAiB,EAAE;QACzC,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,SAAS;QAC7B,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,eAAe;QACnC,QAAQ,OAAO,CAAC;QAChB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,eAAe;QAClC,OAAO;IACT;IAEA;;;;;;;;;;;GAWC,GACD,SAAS,uBAAuB,IAAI;QAClC,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,kBAAkB,EAAE;YACrC,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,eAAe;YACnC,QAAQ,OAAO,CAAC;YAChB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,eAAe;YAClC,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,SAAS;YAC5B,OAAO;QACT;QAEA,OAAO,IAAI;IACb;AACF", "ignoreList": [0]}}, {"offset": {"line": 3406, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3412, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/node_modules/micromark-core-commonmark/dev/lib/label-start-image.js"], "sourcesContent": ["/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {codes, types} from 'micromark-util-symbol'\nimport {labelEnd} from './label-end.js'\n\n/** @type {Construct} */\nexport const labelStartImage = {\n  name: 'labelStartImage',\n  resolveAll: labelEnd.resolveAll,\n  tokenize: tokenizeLabelStartImage\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeLabelStartImage(effects, ok, nok) {\n  const self = this\n\n  return start\n\n  /**\n   * Start of label (image) start.\n   *\n   * ```markdown\n   * > | a ![b] c\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.exclamationMark, 'expected `!`')\n    effects.enter(types.labelImage)\n    effects.enter(types.labelImageMarker)\n    effects.consume(code)\n    effects.exit(types.labelImageMarker)\n    return open\n  }\n\n  /**\n   * After `!`, at `[`.\n   *\n   * ```markdown\n   * > | a ![b] c\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    if (code === codes.leftSquareBracket) {\n      effects.enter(types.labelMarker)\n      effects.consume(code)\n      effects.exit(types.labelMarker)\n      effects.exit(types.labelImage)\n      return after\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `![`.\n   *\n   * ```markdown\n   * > | a ![b] c\n   *         ^\n   * ```\n   *\n   * This is needed in because, when GFM footnotes are enabled, images never\n   * form when started with a `^`.\n   * Instead, links form:\n   *\n   * ```markdown\n   * ![^a](b)\n   *\n   * ![^a][b]\n   *\n   * [b]: c\n   * ```\n   *\n   * ```html\n   * <p>!<a href=\\\"b\\\">^a</a></p>\n   * <p>!<a href=\\\"c\\\">^a</a></p>\n   * ```\n   *\n   * @type {State}\n   */\n  function after(code) {\n    // To do: use a new field to do this, this is still needed for\n    // `micromark-extension-gfm-footnote`, but the `label-start-link`\n    // behavior isn’t.\n    // Hidden footnotes hook.\n    /* c8 ignore next 3 */\n    return code === codes.caret &&\n      '_hiddenFootnoteSupport' in self.parser.constructs\n      ? nok(code)\n      : ok(code)\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AAID;AAFA;AACA;AAAA;;;;AAIO,MAAM,kBAAkB;IAC7B,MAAM;IACN,YAAY,6KAAA,CAAA,WAAQ,CAAC,UAAU;IAC/B,UAAU;AACZ;AAEA;;;;CAIC,GACD,SAAS,wBAAwB,OAAO,EAAE,EAAE,EAAE,GAAG;IAC/C,MAAM,OAAO,IAAI;IAEjB,OAAO;;IAEP;;;;;;;;;GASC,GACD,SAAS,MAAM,IAAI;QACjB,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,SAAS,2JAAA,CAAA,QAAK,CAAC,eAAe,EAAE;QACvC,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,UAAU;QAC9B,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,gBAAgB;QACpC,QAAQ,OAAO,CAAC;QAChB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,gBAAgB;QACnC,OAAO;IACT;IAEA;;;;;;;;;GASC,GACD,SAAS,KAAK,IAAI;QAChB,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,iBAAiB,EAAE;YACpC,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,WAAW;YAC/B,QAAQ,OAAO,CAAC;YAChB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,WAAW;YAC9B,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,UAAU;YAC7B,OAAO;QACT;QAEA,OAAO,IAAI;IACb;IAEA;;;;;;;;;;;;;;;;;;;;;;;;;;GA0BC,GACD,SAAS,MAAM,IAAI;QACjB,8DAA8D;QAC9D,iEAAiE;QACjE,kBAAkB;QAClB,yBAAyB;QACzB,oBAAoB,GACpB,OAAO,SAAS,2JAAA,CAAA,QAAK,CAAC,KAAK,IACzB,4BAA4B,KAAK,MAAM,CAAC,UAAU,GAChD,IAAI,QACJ,GAAG;IACT;AACF", "ignoreList": [0]}}, {"offset": {"line": 3512, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3518, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/node_modules/micromark-core-commonmark/dev/lib/attention.js"], "sourcesContent": ["/**\n * @import {\n *   Code,\n *   Construct,\n *   Event,\n *   Point,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer,\n *   Token\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {push, splice} from 'micromark-util-chunked'\nimport {classify<PERSON><PERSON>cter} from 'micromark-util-classify-character'\nimport {resolveAll} from 'micromark-util-resolve-all'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const attention = {\n  name: 'attention',\n  resolveAll: resolveAllAttention,\n  tokenize: tokenizeAttention\n}\n\n/**\n * Take all events and resolve attention to emphasis or strong.\n *\n * @type {Resolver}\n */\n// eslint-disable-next-line complexity\nfunction resolveAllAttention(events, context) {\n  let index = -1\n  /** @type {number} */\n  let open\n  /** @type {Token} */\n  let group\n  /** @type {Token} */\n  let text\n  /** @type {Token} */\n  let openingSequence\n  /** @type {Token} */\n  let closingSequence\n  /** @type {number} */\n  let use\n  /** @type {Array<Event>} */\n  let nextEvents\n  /** @type {number} */\n  let offset\n\n  // Walk through all events.\n  //\n  // Note: performance of this is fine on an mb of normal markdown, but it’s\n  // a bottleneck for malicious stuff.\n  while (++index < events.length) {\n    // Find a token that can close.\n    if (\n      events[index][0] === 'enter' &&\n      events[index][1].type === 'attentionSequence' &&\n      events[index][1]._close\n    ) {\n      open = index\n\n      // Now walk back to find an opener.\n      while (open--) {\n        // Find a token that can open the closer.\n        if (\n          events[open][0] === 'exit' &&\n          events[open][1].type === 'attentionSequence' &&\n          events[open][1]._open &&\n          // If the markers are the same:\n          context.sliceSerialize(events[open][1]).charCodeAt(0) ===\n            context.sliceSerialize(events[index][1]).charCodeAt(0)\n        ) {\n          // If the opening can close or the closing can open,\n          // and the close size *is not* a multiple of three,\n          // but the sum of the opening and closing size *is* multiple of three,\n          // then don’t match.\n          if (\n            (events[open][1]._close || events[index][1]._open) &&\n            (events[index][1].end.offset - events[index][1].start.offset) % 3 &&\n            !(\n              (events[open][1].end.offset -\n                events[open][1].start.offset +\n                events[index][1].end.offset -\n                events[index][1].start.offset) %\n              3\n            )\n          ) {\n            continue\n          }\n\n          // Number of markers to use from the sequence.\n          use =\n            events[open][1].end.offset - events[open][1].start.offset > 1 &&\n            events[index][1].end.offset - events[index][1].start.offset > 1\n              ? 2\n              : 1\n\n          const start = {...events[open][1].end}\n          const end = {...events[index][1].start}\n          movePoint(start, -use)\n          movePoint(end, use)\n\n          openingSequence = {\n            type: use > 1 ? types.strongSequence : types.emphasisSequence,\n            start,\n            end: {...events[open][1].end}\n          }\n          closingSequence = {\n            type: use > 1 ? types.strongSequence : types.emphasisSequence,\n            start: {...events[index][1].start},\n            end\n          }\n          text = {\n            type: use > 1 ? types.strongText : types.emphasisText,\n            start: {...events[open][1].end},\n            end: {...events[index][1].start}\n          }\n          group = {\n            type: use > 1 ? types.strong : types.emphasis,\n            start: {...openingSequence.start},\n            end: {...closingSequence.end}\n          }\n\n          events[open][1].end = {...openingSequence.start}\n          events[index][1].start = {...closingSequence.end}\n\n          nextEvents = []\n\n          // If there are more markers in the opening, add them before.\n          if (events[open][1].end.offset - events[open][1].start.offset) {\n            nextEvents = push(nextEvents, [\n              ['enter', events[open][1], context],\n              ['exit', events[open][1], context]\n            ])\n          }\n\n          // Opening.\n          nextEvents = push(nextEvents, [\n            ['enter', group, context],\n            ['enter', openingSequence, context],\n            ['exit', openingSequence, context],\n            ['enter', text, context]\n          ])\n\n          // Always populated by defaults.\n          assert(\n            context.parser.constructs.insideSpan.null,\n            'expected `insideSpan` to be populated'\n          )\n\n          // Between.\n          nextEvents = push(\n            nextEvents,\n            resolveAll(\n              context.parser.constructs.insideSpan.null,\n              events.slice(open + 1, index),\n              context\n            )\n          )\n\n          // Closing.\n          nextEvents = push(nextEvents, [\n            ['exit', text, context],\n            ['enter', closingSequence, context],\n            ['exit', closingSequence, context],\n            ['exit', group, context]\n          ])\n\n          // If there are more markers in the closing, add them after.\n          if (events[index][1].end.offset - events[index][1].start.offset) {\n            offset = 2\n            nextEvents = push(nextEvents, [\n              ['enter', events[index][1], context],\n              ['exit', events[index][1], context]\n            ])\n          } else {\n            offset = 0\n          }\n\n          splice(events, open - 1, index - open + 3, nextEvents)\n\n          index = open + nextEvents.length - offset - 2\n          break\n        }\n      }\n    }\n  }\n\n  // Remove remaining sequences.\n  index = -1\n\n  while (++index < events.length) {\n    if (events[index][1].type === 'attentionSequence') {\n      events[index][1].type = 'data'\n    }\n  }\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeAttention(effects, ok) {\n  const attentionMarkers = this.parser.constructs.attentionMarkers.null\n  const previous = this.previous\n  const before = classifyCharacter(previous)\n\n  /** @type {NonNullable<Code>} */\n  let marker\n\n  return start\n\n  /**\n   * Before a sequence.\n   *\n   * ```markdown\n   * > | **\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(\n      code === codes.asterisk || code === codes.underscore,\n      'expected asterisk or underscore'\n    )\n    marker = code\n    effects.enter('attentionSequence')\n    return inside(code)\n  }\n\n  /**\n   * In a sequence.\n   *\n   * ```markdown\n   * > | **\n   *     ^^\n   * ```\n   *\n   * @type {State}\n   */\n  function inside(code) {\n    if (code === marker) {\n      effects.consume(code)\n      return inside\n    }\n\n    const token = effects.exit('attentionSequence')\n\n    // To do: next major: move this to resolver, just like `markdown-rs`.\n    const after = classifyCharacter(code)\n\n    // Always populated by defaults.\n    assert(attentionMarkers, 'expected `attentionMarkers` to be populated')\n\n    const open =\n      !after ||\n      (after === constants.characterGroupPunctuation && before) ||\n      attentionMarkers.includes(code)\n    const close =\n      !before ||\n      (before === constants.characterGroupPunctuation && after) ||\n      attentionMarkers.includes(previous)\n\n    token._open = Boolean(\n      marker === codes.asterisk ? open : open && (before || !close)\n    )\n    token._close = Boolean(\n      marker === codes.asterisk ? close : close && (after || !open)\n    )\n    return ok(code)\n  }\n}\n\n/**\n * Move a point a bit.\n *\n * Note: `move` only works inside lines! It’s not possible to move past other\n * chunks (replacement characters, tabs, or line endings).\n *\n * @param {Point} point\n *   Point.\n * @param {number} offset\n *   Amount to move.\n * @returns {undefined}\n *   Nothing.\n */\nfunction movePoint(point, offset) {\n  point.column += offset\n  point.offset += offset\n  point._bufferIndex += offset\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;CAYC;;;AAMD;AAHA;AADA;AAGA;AADA;AAEA;AAAA;;;;;;AAGO,MAAM,YAAY;IACvB,MAAM;IACN,YAAY;IACZ,UAAU;AACZ;AAEA;;;;CAIC,GACD,sCAAsC;AACtC,SAAS,oBAAoB,MAAM,EAAE,OAAO;IAC1C,IAAI,QAAQ,CAAC;IACb,mBAAmB,GACnB,IAAI;IACJ,kBAAkB,GAClB,IAAI;IACJ,kBAAkB,GAClB,IAAI;IACJ,kBAAkB,GAClB,IAAI;IACJ,kBAAkB,GAClB,IAAI;IACJ,mBAAmB,GACnB,IAAI;IACJ,yBAAyB,GACzB,IAAI;IACJ,mBAAmB,GACnB,IAAI;IAEJ,2BAA2B;IAC3B,EAAE;IACF,0EAA0E;IAC1E,oCAAoC;IACpC,MAAO,EAAE,QAAQ,OAAO,MAAM,CAAE;QAC9B,+BAA+B;QAC/B,IACE,MAAM,CAAC,MAAM,CAAC,EAAE,KAAK,WACrB,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,uBAC1B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EACvB;YACA,OAAO;YAEP,mCAAmC;YACnC,MAAO,OAAQ;gBACb,yCAAyC;gBACzC,IACE,MAAM,CAAC,KAAK,CAAC,EAAE,KAAK,UACpB,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,uBACzB,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,IACrB,+BAA+B;gBAC/B,QAAQ,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE,UAAU,CAAC,OACjD,QAAQ,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,UAAU,CAAC,IACtD;oBACA,oDAAoD;oBACpD,mDAAmD;oBACnD,sEAAsE;oBACtE,oBAAoB;oBACpB,IACE,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,KACjD,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,IAAI,KAChE,CAAC,CACC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,GACzB,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,GAC5B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,GAC3B,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,IAC/B,CACF,GACA;wBACA;oBACF;oBAEA,8CAA8C;oBAC9C,MACE,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,GAAG,KAC5D,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,GAAG,IAC1D,IACA;oBAEN,MAAM,QAAQ;wBAAC,GAAG,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG;oBAAA;oBACrC,MAAM,MAAM;wBAAC,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK;oBAAA;oBACtC,UAAU,OAAO,CAAC;oBAClB,UAAU,KAAK;oBAEf,kBAAkB;wBAChB,MAAM,MAAM,IAAI,2JAAA,CAAA,QAAK,CAAC,cAAc,GAAG,2JAAA,CAAA,QAAK,CAAC,gBAAgB;wBAC7D;wBACA,KAAK;4BAAC,GAAG,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG;wBAAA;oBAC9B;oBACA,kBAAkB;wBAChB,MAAM,MAAM,IAAI,2JAAA,CAAA,QAAK,CAAC,cAAc,GAAG,2JAAA,CAAA,QAAK,CAAC,gBAAgB;wBAC7D,OAAO;4BAAC,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK;wBAAA;wBACjC;oBACF;oBACA,OAAO;wBACL,MAAM,MAAM,IAAI,2JAAA,CAAA,QAAK,CAAC,UAAU,GAAG,2JAAA,CAAA,QAAK,CAAC,YAAY;wBACrD,OAAO;4BAAC,GAAG,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG;wBAAA;wBAC9B,KAAK;4BAAC,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK;wBAAA;oBACjC;oBACA,QAAQ;wBACN,MAAM,MAAM,IAAI,2JAAA,CAAA,QAAK,CAAC,MAAM,GAAG,2JAAA,CAAA,QAAK,CAAC,QAAQ;wBAC7C,OAAO;4BAAC,GAAG,gBAAgB,KAAK;wBAAA;wBAChC,KAAK;4BAAC,GAAG,gBAAgB,GAAG;wBAAA;oBAC9B;oBAEA,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,GAAG;wBAAC,GAAG,gBAAgB,KAAK;oBAAA;oBAC/C,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,GAAG;wBAAC,GAAG,gBAAgB,GAAG;oBAAA;oBAEhD,aAAa,EAAE;oBAEf,6DAA6D;oBAC7D,IAAI,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE;wBAC7D,aAAa,CAAA,GAAA,4JAAA,CAAA,OAAI,AAAD,EAAE,YAAY;4BAC5B;gCAAC;gCAAS,MAAM,CAAC,KAAK,CAAC,EAAE;gCAAE;6BAAQ;4BACnC;gCAAC;gCAAQ,MAAM,CAAC,KAAK,CAAC,EAAE;gCAAE;6BAAQ;yBACnC;oBACH;oBAEA,WAAW;oBACX,aAAa,CAAA,GAAA,4JAAA,CAAA,OAAI,AAAD,EAAE,YAAY;wBAC5B;4BAAC;4BAAS;4BAAO;yBAAQ;wBACzB;4BAAC;4BAAS;4BAAiB;yBAAQ;wBACnC;4BAAC;4BAAQ;4BAAiB;yBAAQ;wBAClC;4BAAC;4BAAS;4BAAM;yBAAQ;qBACzB;oBAED,gCAAgC;oBAChC,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EACH,QAAQ,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,EACzC;oBAGF,WAAW;oBACX,aAAa,CAAA,GAAA,4JAAA,CAAA,OAAI,AAAD,EACd,YACA,CAAA,GAAA,4JAAA,CAAA,aAAU,AAAD,EACP,QAAQ,MAAM,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,EACzC,OAAO,KAAK,CAAC,OAAO,GAAG,QACvB;oBAIJ,WAAW;oBACX,aAAa,CAAA,GAAA,4JAAA,CAAA,OAAI,AAAD,EAAE,YAAY;wBAC5B;4BAAC;4BAAQ;4BAAM;yBAAQ;wBACvB;4BAAC;4BAAS;4BAAiB;yBAAQ;wBACnC;4BAAC;4BAAQ;4BAAiB;yBAAQ;wBAClC;4BAAC;4BAAQ;4BAAO;yBAAQ;qBACzB;oBAED,4DAA4D;oBAC5D,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE;wBAC/D,SAAS;wBACT,aAAa,CAAA,GAAA,4JAAA,CAAA,OAAI,AAAD,EAAE,YAAY;4BAC5B;gCAAC;gCAAS,MAAM,CAAC,MAAM,CAAC,EAAE;gCAAE;6BAAQ;4BACpC;gCAAC;gCAAQ,MAAM,CAAC,MAAM,CAAC,EAAE;gCAAE;6BAAQ;yBACpC;oBACH,OAAO;wBACL,SAAS;oBACX;oBAEA,CAAA,GAAA,4JAAA,CAAA,SAAM,AAAD,EAAE,QAAQ,OAAO,GAAG,QAAQ,OAAO,GAAG;oBAE3C,QAAQ,OAAO,WAAW,MAAM,GAAG,SAAS;oBAC5C;gBACF;YACF;QACF;IACF;IAEA,8BAA8B;IAC9B,QAAQ,CAAC;IAET,MAAO,EAAE,QAAQ,OAAO,MAAM,CAAE;QAC9B,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,qBAAqB;YACjD,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,GAAG;QAC1B;IACF;IAEA,OAAO;AACT;AAEA;;;;CAIC,GACD,SAAS,kBAAkB,OAAO,EAAE,EAAE;IACpC,MAAM,mBAAmB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,gBAAgB,CAAC,IAAI;IACrE,MAAM,WAAW,IAAI,CAAC,QAAQ;IAC9B,MAAM,SAAS,CAAA,GAAA,0KAAA,CAAA,oBAAiB,AAAD,EAAE;IAEjC,8BAA8B,GAC9B,IAAI;IAEJ,OAAO;;IAEP;;;;;;;;;GASC,GACD,SAAS,MAAM,IAAI;QACjB,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EACH,SAAS,2JAAA,CAAA,QAAK,CAAC,QAAQ,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,UAAU,EACpD;QAEF,SAAS;QACT,QAAQ,KAAK,CAAC;QACd,OAAO,OAAO;IAChB;IAEA;;;;;;;;;GASC,GACD,SAAS,OAAO,IAAI;QAClB,IAAI,SAAS,QAAQ;YACnB,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,MAAM,QAAQ,QAAQ,IAAI,CAAC;QAE3B,qEAAqE;QACrE,MAAM,QAAQ,CAAA,GAAA,0KAAA,CAAA,oBAAiB,AAAD,EAAE;QAEhC,gCAAgC;QAChC,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,kBAAkB;QAEzB,MAAM,OACJ,CAAC,SACA,UAAU,+JAAA,CAAA,YAAS,CAAC,yBAAyB,IAAI,UAClD,iBAAiB,QAAQ,CAAC;QAC5B,MAAM,QACJ,CAAC,UACA,WAAW,+JAAA,CAAA,YAAS,CAAC,yBAAyB,IAAI,SACnD,iBAAiB,QAAQ,CAAC;QAE5B,MAAM,KAAK,GAAG,QACZ,WAAW,2JAAA,CAAA,QAAK,CAAC,QAAQ,GAAG,OAAO,QAAQ,CAAC,UAAU,CAAC,KAAK;QAE9D,MAAM,MAAM,GAAG,QACb,WAAW,2JAAA,CAAA,QAAK,CAAC,QAAQ,GAAG,QAAQ,SAAS,CAAC,SAAS,CAAC,IAAI;QAE9D,OAAO,GAAG;IACZ;AACF;AAEA;;;;;;;;;;;;CAYC,GACD,SAAS,UAAU,KAAK,EAAE,MAAM;IAC9B,MAAM,MAAM,IAAI;IAChB,MAAM,MAAM,IAAI;IAChB,MAAM,YAAY,IAAI;AACxB", "ignoreList": [0]}}, {"offset": {"line": 3802, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3808, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/node_modules/micromark-core-commonmark/dev/lib/autolink.js"], "sourcesContent": ["/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {\n  asciiAlphanumeric,\n  asciiAlpha,\n  asciiAtext,\n  asciiControl\n} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const autolink = {name: 'autolink', tokenize: tokenizeAutolink}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeAutolink(effects, ok, nok) {\n  let size = 0\n\n  return start\n\n  /**\n   * Start of an autolink.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *      ^\n   * > | a<<EMAIL>>b\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.lessThan, 'expected `<`')\n    effects.enter(types.autolink)\n    effects.enter(types.autolinkMarker)\n    effects.consume(code)\n    effects.exit(types.autolinkMarker)\n    effects.enter(types.autolinkProtocol)\n    return open\n  }\n\n  /**\n   * After `<`, at protocol or atext.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *       ^\n   * > | a<<EMAIL>>b\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    if (asciiAlpha(code)) {\n      effects.consume(code)\n      return schemeOrEmailAtext\n    }\n\n    if (code === codes.atSign) {\n      return nok(code)\n    }\n\n    return emailAtext(code)\n  }\n\n  /**\n   * At second byte of protocol or atext.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *        ^\n   * > | a<<EMAIL>>b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function schemeOrEmailAtext(code) {\n    // ASCII alphanumeric and `+`, `-`, and `.`.\n    if (\n      code === codes.plusSign ||\n      code === codes.dash ||\n      code === codes.dot ||\n      asciiAlphanumeric(code)\n    ) {\n      // Count the previous alphabetical from `open` too.\n      size = 1\n      return schemeInsideOrEmailAtext(code)\n    }\n\n    return emailAtext(code)\n  }\n\n  /**\n   * In ambiguous protocol or atext.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *        ^\n   * > | a<<EMAIL>>b\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function schemeInsideOrEmailAtext(code) {\n    if (code === codes.colon) {\n      effects.consume(code)\n      size = 0\n      return urlInside\n    }\n\n    // ASCII alphanumeric and `+`, `-`, and `.`.\n    if (\n      (code === codes.plusSign ||\n        code === codes.dash ||\n        code === codes.dot ||\n        asciiAlphanumeric(code)) &&\n      size++ < constants.autolinkSchemeSizeMax\n    ) {\n      effects.consume(code)\n      return schemeInsideOrEmailAtext\n    }\n\n    size = 0\n    return emailAtext(code)\n  }\n\n  /**\n   * After protocol, in URL.\n   *\n   * ```markdown\n   * > | a<https://example.com>b\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function urlInside(code) {\n    if (code === codes.greaterThan) {\n      effects.exit(types.autolinkProtocol)\n      effects.enter(types.autolinkMarker)\n      effects.consume(code)\n      effects.exit(types.autolinkMarker)\n      effects.exit(types.autolink)\n      return ok\n    }\n\n    // ASCII control, space, or `<`.\n    if (\n      code === codes.eof ||\n      code === codes.space ||\n      code === codes.lessThan ||\n      asciiControl(code)\n    ) {\n      return nok(code)\n    }\n\n    effects.consume(code)\n    return urlInside\n  }\n\n  /**\n   * In email atext.\n   *\n   * ```markdown\n   * > | a<<EMAIL>>b\n   *              ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailAtext(code) {\n    if (code === codes.atSign) {\n      effects.consume(code)\n      return emailAtSignOrDot\n    }\n\n    if (asciiAtext(code)) {\n      effects.consume(code)\n      return emailAtext\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In label, after at-sign or dot.\n   *\n   * ```markdown\n   * > | a<<EMAIL>>b\n   *                 ^       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailAtSignOrDot(code) {\n    return asciiAlphanumeric(code) ? emailLabel(code) : nok(code)\n  }\n\n  /**\n   * In label, where `.` and `>` are allowed.\n   *\n   * ```markdown\n   * > | a<<EMAIL>>b\n   *                   ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailLabel(code) {\n    if (code === codes.dot) {\n      effects.consume(code)\n      size = 0\n      return emailAtSignOrDot\n    }\n\n    if (code === codes.greaterThan) {\n      // Exit, then change the token type.\n      effects.exit(types.autolinkProtocol).type = types.autolinkEmail\n      effects.enter(types.autolinkMarker)\n      effects.consume(code)\n      effects.exit(types.autolinkMarker)\n      effects.exit(types.autolink)\n      return ok\n    }\n\n    return emailValue(code)\n  }\n\n  /**\n   * In label, where `.` and `>` are *not* allowed.\n   *\n   * Though, this is also used in `emailLabel` to parse other values.\n   *\n   * ```markdown\n   * > | a<<EMAIL>>b\n   *                    ^\n   * ```\n   *\n   * @type {State}\n   */\n  function emailValue(code) {\n    // ASCII alphanumeric or `-`.\n    if (\n      (code === codes.dash || asciiAlphanumeric(code)) &&\n      size++ < constants.autolinkDomainSizeMax\n    ) {\n      const next = code === codes.dash ? emailValue : emailLabel\n      effects.consume(code)\n      return next\n    }\n\n    return nok(code)\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AAED;AAOA;AAAA;AANA;AAMA;;;;AAGO,MAAM,WAAW;IAAC,MAAM;IAAY,UAAU;AAAgB;AAErE;;;;CAIC,GACD,SAAS,iBAAiB,OAAO,EAAE,EAAE,EAAE,GAAG;IACxC,IAAI,OAAO;IAEX,OAAO;;IAEP;;;;;;;;;;;GAWC,GACD,SAAS,MAAM,IAAI;QACjB,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,SAAS,2JAAA,CAAA,QAAK,CAAC,QAAQ,EAAE;QAChC,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,QAAQ;QAC5B,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,cAAc;QAClC,QAAQ,OAAO,CAAC;QAChB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,cAAc;QACjC,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,gBAAgB;QACpC,OAAO;IACT;IAEA;;;;;;;;;;;GAWC,GACD,SAAS,KAAK,IAAI;QAChB,IAAI,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD,EAAE,OAAO;YACpB,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,MAAM,EAAE;YACzB,OAAO,IAAI;QACb;QAEA,OAAO,WAAW;IACpB;IAEA;;;;;;;;;;;GAWC,GACD,SAAS,mBAAmB,IAAI;QAC9B,4CAA4C;QAC5C,IACE,SAAS,2JAAA,CAAA,QAAK,CAAC,QAAQ,IACvB,SAAS,2JAAA,CAAA,QAAK,CAAC,IAAI,IACnB,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,IAClB,CAAA,GAAA,8JAAA,CAAA,oBAAiB,AAAD,EAAE,OAClB;YACA,mDAAmD;YACnD,OAAO;YACP,OAAO,yBAAyB;QAClC;QAEA,OAAO,WAAW;IACpB;IAEA;;;;;;;;;;;GAWC,GACD,SAAS,yBAAyB,IAAI;QACpC,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,KAAK,EAAE;YACxB,QAAQ,OAAO,CAAC;YAChB,OAAO;YACP,OAAO;QACT;QAEA,4CAA4C;QAC5C,IACE,CAAC,SAAS,2JAAA,CAAA,QAAK,CAAC,QAAQ,IACtB,SAAS,2JAAA,CAAA,QAAK,CAAC,IAAI,IACnB,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,IAClB,CAAA,GAAA,8JAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,KACzB,SAAS,+JAAA,CAAA,YAAS,CAAC,qBAAqB,EACxC;YACA,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,OAAO;QACP,OAAO,WAAW;IACpB;IAEA;;;;;;;;;GASC,GACD,SAAS,UAAU,IAAI;QACrB,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,WAAW,EAAE;YAC9B,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,gBAAgB;YACnC,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,cAAc;YAClC,QAAQ,OAAO,CAAC;YAChB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,cAAc;YACjC,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,QAAQ;YAC3B,OAAO;QACT;QAEA,gCAAgC;QAChC,IACE,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,IAClB,SAAS,2JAAA,CAAA,QAAK,CAAC,KAAK,IACpB,SAAS,2JAAA,CAAA,QAAK,CAAC,QAAQ,IACvB,CAAA,GAAA,8JAAA,CAAA,eAAY,AAAD,EAAE,OACb;YACA,OAAO,IAAI;QACb;QAEA,QAAQ,OAAO,CAAC;QAChB,OAAO;IACT;IAEA;;;;;;;;;GASC,GACD,SAAS,WAAW,IAAI;QACtB,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,MAAM,EAAE;YACzB,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,IAAI,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD,EAAE,OAAO;YACpB,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,OAAO,IAAI;IACb;IAEA;;;;;;;;;GASC,GACD,SAAS,iBAAiB,IAAI;QAC5B,OAAO,CAAA,GAAA,8JAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,WAAW,QAAQ,IAAI;IAC1D;IAEA;;;;;;;;;GASC,GACD,SAAS,WAAW,IAAI;QACtB,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,EAAE;YACtB,QAAQ,OAAO,CAAC;YAChB,OAAO;YACP,OAAO;QACT;QAEA,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,WAAW,EAAE;YAC9B,oCAAoC;YACpC,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,gBAAgB,EAAE,IAAI,GAAG,2JAAA,CAAA,QAAK,CAAC,aAAa;YAC/D,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,cAAc;YAClC,QAAQ,OAAO,CAAC;YAChB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,cAAc;YACjC,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,QAAQ;YAC3B,OAAO;QACT;QAEA,OAAO,WAAW;IACpB;IAEA;;;;;;;;;;;GAWC,GACD,SAAS,WAAW,IAAI;QACtB,6BAA6B;QAC7B,IACE,CAAC,SAAS,2JAAA,CAAA,QAAK,CAAC,IAAI,IAAI,CAAA,GAAA,8JAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,KAC/C,SAAS,+JAAA,CAAA,YAAS,CAAC,qBAAqB,EACxC;YACA,MAAM,OAAO,SAAS,2JAAA,CAAA,QAAK,CAAC,IAAI,GAAG,aAAa;YAChD,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,OAAO,IAAI;IACb;AACF", "ignoreList": [0]}}, {"offset": {"line": 4028, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4034, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/node_modules/micromark-core-commonmark/dev/lib/html-text.js"], "sourcesContent": ["/**\n * @import {\n *   Code,\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {\n  asciiAlphanumeric,\n  asciiAlpha,\n  markdownLineEndingOrSpace,\n  markdownLineEnding,\n  markdownSpace\n} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const htmlText = {name: 'htmlText', tokenize: tokenizeHtmlText}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeHtmlText(effects, ok, nok) {\n  const self = this\n  /** @type {NonNullable<Code> | undefined} */\n  let marker\n  /** @type {number} */\n  let index\n  /** @type {State} */\n  let returnState\n\n  return start\n\n  /**\n   * Start of HTML (text).\n   *\n   * ```markdown\n   * > | a <b> c\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.lessThan, 'expected `<`')\n    effects.enter(types.htmlText)\n    effects.enter(types.htmlTextData)\n    effects.consume(code)\n    return open\n  }\n\n  /**\n   * After `<`, at tag name or other stuff.\n   *\n   * ```markdown\n   * > | a <b> c\n   *        ^\n   * > | a <!doctype> c\n   *        ^\n   * > | a <!--b--> c\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function open(code) {\n    if (code === codes.exclamationMark) {\n      effects.consume(code)\n      return declarationOpen\n    }\n\n    if (code === codes.slash) {\n      effects.consume(code)\n      return tagCloseStart\n    }\n\n    if (code === codes.questionMark) {\n      effects.consume(code)\n      return instruction\n    }\n\n    // ASCII alphabetical.\n    if (asciiAlpha(code)) {\n      effects.consume(code)\n      return tagOpen\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `<!`, at declaration, comment, or CDATA.\n   *\n   * ```markdown\n   * > | a <!doctype> c\n   *         ^\n   * > | a <!--b--> c\n   *         ^\n   * > | a <![CDATA[>&<]]> c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function declarationOpen(code) {\n    if (code === codes.dash) {\n      effects.consume(code)\n      return commentOpenInside\n    }\n\n    if (code === codes.leftSquareBracket) {\n      effects.consume(code)\n      index = 0\n      return cdataOpenInside\n    }\n\n    if (asciiAlpha(code)) {\n      effects.consume(code)\n      return declaration\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In a comment, after `<!-`, at another `-`.\n   *\n   * ```markdown\n   * > | a <!--b--> c\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function commentOpenInside(code) {\n    if (code === codes.dash) {\n      effects.consume(code)\n      return commentEnd\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In comment.\n   *\n   * ```markdown\n   * > | a <!--b--> c\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function comment(code) {\n    if (code === codes.eof) {\n      return nok(code)\n    }\n\n    if (code === codes.dash) {\n      effects.consume(code)\n      return commentClose\n    }\n\n    if (markdownLineEnding(code)) {\n      returnState = comment\n      return lineEndingBefore(code)\n    }\n\n    effects.consume(code)\n    return comment\n  }\n\n  /**\n   * In comment, after `-`.\n   *\n   * ```markdown\n   * > | a <!--b--> c\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function commentClose(code) {\n    if (code === codes.dash) {\n      effects.consume(code)\n      return commentEnd\n    }\n\n    return comment(code)\n  }\n\n  /**\n   * In comment, after `--`.\n   *\n   * ```markdown\n   * > | a <!--b--> c\n   *              ^\n   * ```\n   *\n   * @type {State}\n   */\n  function commentEnd(code) {\n    return code === codes.greaterThan\n      ? end(code)\n      : code === codes.dash\n        ? commentClose(code)\n        : comment(code)\n  }\n\n  /**\n   * After `<![`, in CDATA, expecting `CDATA[`.\n   *\n   * ```markdown\n   * > | a <![CDATA[>&<]]> b\n   *          ^^^^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function cdataOpenInside(code) {\n    const value = constants.cdataOpeningString\n\n    if (code === value.charCodeAt(index++)) {\n      effects.consume(code)\n      return index === value.length ? cdata : cdataOpenInside\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In CDATA.\n   *\n   * ```markdown\n   * > | a <![CDATA[>&<]]> b\n   *                ^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function cdata(code) {\n    if (code === codes.eof) {\n      return nok(code)\n    }\n\n    if (code === codes.rightSquareBracket) {\n      effects.consume(code)\n      return cdataClose\n    }\n\n    if (markdownLineEnding(code)) {\n      returnState = cdata\n      return lineEndingBefore(code)\n    }\n\n    effects.consume(code)\n    return cdata\n  }\n\n  /**\n   * In CDATA, after `]`, at another `]`.\n   *\n   * ```markdown\n   * > | a <![CDATA[>&<]]> b\n   *                    ^\n   * ```\n   *\n   * @type {State}\n   */\n  function cdataClose(code) {\n    if (code === codes.rightSquareBracket) {\n      effects.consume(code)\n      return cdataEnd\n    }\n\n    return cdata(code)\n  }\n\n  /**\n   * In CDATA, after `]]`, at `>`.\n   *\n   * ```markdown\n   * > | a <![CDATA[>&<]]> b\n   *                     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function cdataEnd(code) {\n    if (code === codes.greaterThan) {\n      return end(code)\n    }\n\n    if (code === codes.rightSquareBracket) {\n      effects.consume(code)\n      return cdataEnd\n    }\n\n    return cdata(code)\n  }\n\n  /**\n   * In declaration.\n   *\n   * ```markdown\n   * > | a <!b> c\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function declaration(code) {\n    if (code === codes.eof || code === codes.greaterThan) {\n      return end(code)\n    }\n\n    if (markdownLineEnding(code)) {\n      returnState = declaration\n      return lineEndingBefore(code)\n    }\n\n    effects.consume(code)\n    return declaration\n  }\n\n  /**\n   * In instruction.\n   *\n   * ```markdown\n   * > | a <?b?> c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function instruction(code) {\n    if (code === codes.eof) {\n      return nok(code)\n    }\n\n    if (code === codes.questionMark) {\n      effects.consume(code)\n      return instructionClose\n    }\n\n    if (markdownLineEnding(code)) {\n      returnState = instruction\n      return lineEndingBefore(code)\n    }\n\n    effects.consume(code)\n    return instruction\n  }\n\n  /**\n   * In instruction, after `?`, at `>`.\n   *\n   * ```markdown\n   * > | a <?b?> c\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function instructionClose(code) {\n    return code === codes.greaterThan ? end(code) : instruction(code)\n  }\n\n  /**\n   * After `</`, in closing tag, at tag name.\n   *\n   * ```markdown\n   * > | a </b> c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagCloseStart(code) {\n    // ASCII alphabetical.\n    if (asciiAlpha(code)) {\n      effects.consume(code)\n      return tagClose\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * After `</x`, in a tag name.\n   *\n   * ```markdown\n   * > | a </b> c\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagClose(code) {\n    // ASCII alphanumerical and `-`.\n    if (code === codes.dash || asciiAlphanumeric(code)) {\n      effects.consume(code)\n      return tagClose\n    }\n\n    return tagCloseBetween(code)\n  }\n\n  /**\n   * In closing tag, after tag name.\n   *\n   * ```markdown\n   * > | a </b> c\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagCloseBetween(code) {\n    if (markdownLineEnding(code)) {\n      returnState = tagCloseBetween\n      return lineEndingBefore(code)\n    }\n\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return tagCloseBetween\n    }\n\n    return end(code)\n  }\n\n  /**\n   * After `<x`, in opening tag name.\n   *\n   * ```markdown\n   * > | a <b> c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpen(code) {\n    // ASCII alphanumerical and `-`.\n    if (code === codes.dash || asciiAlphanumeric(code)) {\n      effects.consume(code)\n      return tagOpen\n    }\n\n    if (\n      code === codes.slash ||\n      code === codes.greaterThan ||\n      markdownLineEndingOrSpace(code)\n    ) {\n      return tagOpenBetween(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In opening tag, after tag name.\n   *\n   * ```markdown\n   * > | a <b> c\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenBetween(code) {\n    if (code === codes.slash) {\n      effects.consume(code)\n      return end\n    }\n\n    // ASCII alphabetical and `:` and `_`.\n    if (code === codes.colon || code === codes.underscore || asciiAlpha(code)) {\n      effects.consume(code)\n      return tagOpenAttributeName\n    }\n\n    if (markdownLineEnding(code)) {\n      returnState = tagOpenBetween\n      return lineEndingBefore(code)\n    }\n\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return tagOpenBetween\n    }\n\n    return end(code)\n  }\n\n  /**\n   * In attribute name.\n   *\n   * ```markdown\n   * > | a <b c> d\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeName(code) {\n    // ASCII alphabetical and `-`, `.`, `:`, and `_`.\n    if (\n      code === codes.dash ||\n      code === codes.dot ||\n      code === codes.colon ||\n      code === codes.underscore ||\n      asciiAlphanumeric(code)\n    ) {\n      effects.consume(code)\n      return tagOpenAttributeName\n    }\n\n    return tagOpenAttributeNameAfter(code)\n  }\n\n  /**\n   * After attribute name, before initializer, the end of the tag, or\n   * whitespace.\n   *\n   * ```markdown\n   * > | a <b c> d\n   *           ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeNameAfter(code) {\n    if (code === codes.equalsTo) {\n      effects.consume(code)\n      return tagOpenAttributeValueBefore\n    }\n\n    if (markdownLineEnding(code)) {\n      returnState = tagOpenAttributeNameAfter\n      return lineEndingBefore(code)\n    }\n\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return tagOpenAttributeNameAfter\n    }\n\n    return tagOpenBetween(code)\n  }\n\n  /**\n   * Before unquoted, double quoted, or single quoted attribute value, allowing\n   * whitespace.\n   *\n   * ```markdown\n   * > | a <b c=d> e\n   *            ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeValueBefore(code) {\n    if (\n      code === codes.eof ||\n      code === codes.lessThan ||\n      code === codes.equalsTo ||\n      code === codes.greaterThan ||\n      code === codes.graveAccent\n    ) {\n      return nok(code)\n    }\n\n    if (code === codes.quotationMark || code === codes.apostrophe) {\n      effects.consume(code)\n      marker = code\n      return tagOpenAttributeValueQuoted\n    }\n\n    if (markdownLineEnding(code)) {\n      returnState = tagOpenAttributeValueBefore\n      return lineEndingBefore(code)\n    }\n\n    if (markdownSpace(code)) {\n      effects.consume(code)\n      return tagOpenAttributeValueBefore\n    }\n\n    effects.consume(code)\n    return tagOpenAttributeValueUnquoted\n  }\n\n  /**\n   * In double or single quoted attribute value.\n   *\n   * ```markdown\n   * > | a <b c=\"d\"> e\n   *             ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeValueQuoted(code) {\n    if (code === marker) {\n      effects.consume(code)\n      marker = undefined\n      return tagOpenAttributeValueQuotedAfter\n    }\n\n    if (code === codes.eof) {\n      return nok(code)\n    }\n\n    if (markdownLineEnding(code)) {\n      returnState = tagOpenAttributeValueQuoted\n      return lineEndingBefore(code)\n    }\n\n    effects.consume(code)\n    return tagOpenAttributeValueQuoted\n  }\n\n  /**\n   * In unquoted attribute value.\n   *\n   * ```markdown\n   * > | a <b c=d> e\n   *            ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeValueUnquoted(code) {\n    if (\n      code === codes.eof ||\n      code === codes.quotationMark ||\n      code === codes.apostrophe ||\n      code === codes.lessThan ||\n      code === codes.equalsTo ||\n      code === codes.graveAccent\n    ) {\n      return nok(code)\n    }\n\n    if (\n      code === codes.slash ||\n      code === codes.greaterThan ||\n      markdownLineEndingOrSpace(code)\n    ) {\n      return tagOpenBetween(code)\n    }\n\n    effects.consume(code)\n    return tagOpenAttributeValueUnquoted\n  }\n\n  /**\n   * After double or single quoted attribute value, before whitespace or the end\n   * of the tag.\n   *\n   * ```markdown\n   * > | a <b c=\"d\"> e\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function tagOpenAttributeValueQuotedAfter(code) {\n    if (\n      code === codes.slash ||\n      code === codes.greaterThan ||\n      markdownLineEndingOrSpace(code)\n    ) {\n      return tagOpenBetween(code)\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * In certain circumstances of a tag where only an `>` is allowed.\n   *\n   * ```markdown\n   * > | a <b c=\"d\"> e\n   *               ^\n   * ```\n   *\n   * @type {State}\n   */\n  function end(code) {\n    if (code === codes.greaterThan) {\n      effects.consume(code)\n      effects.exit(types.htmlTextData)\n      effects.exit(types.htmlText)\n      return ok\n    }\n\n    return nok(code)\n  }\n\n  /**\n   * At eol.\n   *\n   * > 👉 **Note**: we can’t have blank lines in text, so no need to worry about\n   * > empty tokens.\n   *\n   * ```markdown\n   * > | a <!--a\n   *            ^\n   *   | b-->\n   * ```\n   *\n   * @type {State}\n   */\n  function lineEndingBefore(code) {\n    assert(returnState, 'expected return state')\n    assert(markdownLineEnding(code), 'expected eol')\n    effects.exit(types.htmlTextData)\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    return lineEndingAfter\n  }\n\n  /**\n   * After eol, at optional whitespace.\n   *\n   * > 👉 **Note**: we can’t have blank lines in text, so no need to worry about\n   * > empty tokens.\n   *\n   * ```markdown\n   *   | a <!--a\n   * > | b-->\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function lineEndingAfter(code) {\n    // Always populated by defaults.\n    assert(\n      self.parser.constructs.disable.null,\n      'expected `disable.null` to be populated'\n    )\n    return markdownSpace(code)\n      ? factorySpace(\n          effects,\n          lineEndingAfterPrefix,\n          types.linePrefix,\n          self.parser.constructs.disable.null.includes('codeIndented')\n            ? undefined\n            : constants.tabSize\n        )(code)\n      : lineEndingAfterPrefix(code)\n  }\n\n  /**\n   * After eol, after optional whitespace.\n   *\n   * > 👉 **Note**: we can’t have blank lines in text, so no need to worry about\n   * > empty tokens.\n   *\n   * ```markdown\n   *   | a <!--a\n   * > | b-->\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function lineEndingAfterPrefix(code) {\n    effects.enter(types.htmlTextData)\n    return returnState(code)\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;AAED;AASA;AAAA;AAPA;AAOA;AARA;;;;;AAWO,MAAM,WAAW;IAAC,MAAM;IAAY,UAAU;AAAgB;AAErE;;;;CAIC,GACD,SAAS,iBAAiB,OAAO,EAAE,EAAE,EAAE,GAAG;IACxC,MAAM,OAAO,IAAI;IACjB,0CAA0C,GAC1C,IAAI;IACJ,mBAAmB,GACnB,IAAI;IACJ,kBAAkB,GAClB,IAAI;IAEJ,OAAO;;IAEP;;;;;;;;;GASC,GACD,SAAS,MAAM,IAAI;QACjB,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,SAAS,2JAAA,CAAA,QAAK,CAAC,QAAQ,EAAE;QAChC,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,QAAQ;QAC5B,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,YAAY;QAChC,QAAQ,OAAO,CAAC;QAChB,OAAO;IACT;IAEA;;;;;;;;;;;;;GAaC,GACD,SAAS,KAAK,IAAI;QAChB,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,eAAe,EAAE;YAClC,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,KAAK,EAAE;YACxB,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,YAAY,EAAE;YAC/B,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,sBAAsB;QACtB,IAAI,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD,EAAE,OAAO;YACpB,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,OAAO,IAAI;IACb;IAEA;;;;;;;;;;;;;GAaC,GACD,SAAS,gBAAgB,IAAI;QAC3B,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,IAAI,EAAE;YACvB,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,iBAAiB,EAAE;YACpC,QAAQ,OAAO,CAAC;YAChB,QAAQ;YACR,OAAO;QACT;QAEA,IAAI,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD,EAAE,OAAO;YACpB,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,OAAO,IAAI;IACb;IAEA;;;;;;;;;GASC,GACD,SAAS,kBAAkB,IAAI;QAC7B,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,IAAI,EAAE;YACvB,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,OAAO,IAAI;IACb;IAEA;;;;;;;;;GASC,GACD,SAAS,QAAQ,IAAI;QACnB,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,EAAE;YACtB,OAAO,IAAI;QACb;QAEA,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,IAAI,EAAE;YACvB,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,IAAI,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAC5B,cAAc;YACd,OAAO,iBAAiB;QAC1B;QAEA,QAAQ,OAAO,CAAC;QAChB,OAAO;IACT;IAEA;;;;;;;;;GASC,GACD,SAAS,aAAa,IAAI;QACxB,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,IAAI,EAAE;YACvB,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,OAAO,QAAQ;IACjB;IAEA;;;;;;;;;GASC,GACD,SAAS,WAAW,IAAI;QACtB,OAAO,SAAS,2JAAA,CAAA,QAAK,CAAC,WAAW,GAC7B,IAAI,QACJ,SAAS,2JAAA,CAAA,QAAK,CAAC,IAAI,GACjB,aAAa,QACb,QAAQ;IAChB;IAEA;;;;;;;;;GASC,GACD,SAAS,gBAAgB,IAAI;QAC3B,MAAM,QAAQ,+JAAA,CAAA,YAAS,CAAC,kBAAkB;QAE1C,IAAI,SAAS,MAAM,UAAU,CAAC,UAAU;YACtC,QAAQ,OAAO,CAAC;YAChB,OAAO,UAAU,MAAM,MAAM,GAAG,QAAQ;QAC1C;QAEA,OAAO,IAAI;IACb;IAEA;;;;;;;;;GASC,GACD,SAAS,MAAM,IAAI;QACjB,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,EAAE;YACtB,OAAO,IAAI;QACb;QAEA,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,kBAAkB,EAAE;YACrC,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,IAAI,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAC5B,cAAc;YACd,OAAO,iBAAiB;QAC1B;QAEA,QAAQ,OAAO,CAAC;QAChB,OAAO;IACT;IAEA;;;;;;;;;GASC,GACD,SAAS,WAAW,IAAI;QACtB,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,kBAAkB,EAAE;YACrC,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,OAAO,MAAM;IACf;IAEA;;;;;;;;;GASC,GACD,SAAS,SAAS,IAAI;QACpB,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,WAAW,EAAE;YAC9B,OAAO,IAAI;QACb;QAEA,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,kBAAkB,EAAE;YACrC,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,OAAO,MAAM;IACf;IAEA;;;;;;;;;GASC,GACD,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,WAAW,EAAE;YACpD,OAAO,IAAI;QACb;QAEA,IAAI,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAC5B,cAAc;YACd,OAAO,iBAAiB;QAC1B;QAEA,QAAQ,OAAO,CAAC;QAChB,OAAO;IACT;IAEA;;;;;;;;;GASC,GACD,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,EAAE;YACtB,OAAO,IAAI;QACb;QAEA,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,YAAY,EAAE;YAC/B,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,IAAI,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAC5B,cAAc;YACd,OAAO,iBAAiB;QAC1B;QAEA,QAAQ,OAAO,CAAC;QAChB,OAAO;IACT;IAEA;;;;;;;;;GASC,GACD,SAAS,iBAAiB,IAAI;QAC5B,OAAO,SAAS,2JAAA,CAAA,QAAK,CAAC,WAAW,GAAG,IAAI,QAAQ,YAAY;IAC9D;IAEA;;;;;;;;;GASC,GACD,SAAS,cAAc,IAAI;QACzB,sBAAsB;QACtB,IAAI,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD,EAAE,OAAO;YACpB,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,OAAO,IAAI;IACb;IAEA;;;;;;;;;GASC,GACD,SAAS,SAAS,IAAI;QACpB,gCAAgC;QAChC,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,IAAI,IAAI,CAAA,GAAA,8JAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO;YAClD,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,OAAO,gBAAgB;IACzB;IAEA;;;;;;;;;GASC,GACD,SAAS,gBAAgB,IAAI;QAC3B,IAAI,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAC5B,cAAc;YACd,OAAO,iBAAiB;QAC1B;QAEA,IAAI,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE,OAAO;YACvB,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,OAAO,IAAI;IACb;IAEA;;;;;;;;;GASC,GACD,SAAS,QAAQ,IAAI;QACnB,gCAAgC;QAChC,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,IAAI,IAAI,CAAA,GAAA,8JAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO;YAClD,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,IACE,SAAS,2JAAA,CAAA,QAAK,CAAC,KAAK,IACpB,SAAS,2JAAA,CAAA,QAAK,CAAC,WAAW,IAC1B,CAAA,GAAA,8JAAA,CAAA,4BAAyB,AAAD,EAAE,OAC1B;YACA,OAAO,eAAe;QACxB;QAEA,OAAO,IAAI;IACb;IAEA;;;;;;;;;GASC,GACD,SAAS,eAAe,IAAI;QAC1B,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,KAAK,EAAE;YACxB,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,sCAAsC;QACtC,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,KAAK,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,UAAU,IAAI,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD,EAAE,OAAO;YACzE,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,IAAI,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAC5B,cAAc;YACd,OAAO,iBAAiB;QAC1B;QAEA,IAAI,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE,OAAO;YACvB,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,OAAO,IAAI;IACb;IAEA;;;;;;;;;GASC,GACD,SAAS,qBAAqB,IAAI;QAChC,iDAAiD;QACjD,IACE,SAAS,2JAAA,CAAA,QAAK,CAAC,IAAI,IACnB,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,IAClB,SAAS,2JAAA,CAAA,QAAK,CAAC,KAAK,IACpB,SAAS,2JAAA,CAAA,QAAK,CAAC,UAAU,IACzB,CAAA,GAAA,8JAAA,CAAA,oBAAiB,AAAD,EAAE,OAClB;YACA,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,OAAO,0BAA0B;IACnC;IAEA;;;;;;;;;;GAUC,GACD,SAAS,0BAA0B,IAAI;QACrC,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,QAAQ,EAAE;YAC3B,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,IAAI,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAC5B,cAAc;YACd,OAAO,iBAAiB;QAC1B;QAEA,IAAI,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE,OAAO;YACvB,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,OAAO,eAAe;IACxB;IAEA;;;;;;;;;;GAUC,GACD,SAAS,4BAA4B,IAAI;QACvC,IACE,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,IAClB,SAAS,2JAAA,CAAA,QAAK,CAAC,QAAQ,IACvB,SAAS,2JAAA,CAAA,QAAK,CAAC,QAAQ,IACvB,SAAS,2JAAA,CAAA,QAAK,CAAC,WAAW,IAC1B,SAAS,2JAAA,CAAA,QAAK,CAAC,WAAW,EAC1B;YACA,OAAO,IAAI;QACb;QAEA,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,aAAa,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,UAAU,EAAE;YAC7D,QAAQ,OAAO,CAAC;YAChB,SAAS;YACT,OAAO;QACT;QAEA,IAAI,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAC5B,cAAc;YACd,OAAO,iBAAiB;QAC1B;QAEA,IAAI,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE,OAAO;YACvB,QAAQ,OAAO,CAAC;YAChB,OAAO;QACT;QAEA,QAAQ,OAAO,CAAC;QAChB,OAAO;IACT;IAEA;;;;;;;;;GASC,GACD,SAAS,4BAA4B,IAAI;QACvC,IAAI,SAAS,QAAQ;YACnB,QAAQ,OAAO,CAAC;YAChB,SAAS;YACT,OAAO;QACT;QAEA,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,EAAE;YACtB,OAAO,IAAI;QACb;QAEA,IAAI,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAC5B,cAAc;YACd,OAAO,iBAAiB;QAC1B;QAEA,QAAQ,OAAO,CAAC;QAChB,OAAO;IACT;IAEA;;;;;;;;;GASC,GACD,SAAS,8BAA8B,IAAI;QACzC,IACE,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,IAClB,SAAS,2JAAA,CAAA,QAAK,CAAC,aAAa,IAC5B,SAAS,2JAAA,CAAA,QAAK,CAAC,UAAU,IACzB,SAAS,2JAAA,CAAA,QAAK,CAAC,QAAQ,IACvB,SAAS,2JAAA,CAAA,QAAK,CAAC,QAAQ,IACvB,SAAS,2JAAA,CAAA,QAAK,CAAC,WAAW,EAC1B;YACA,OAAO,IAAI;QACb;QAEA,IACE,SAAS,2JAAA,CAAA,QAAK,CAAC,KAAK,IACpB,SAAS,2JAAA,CAAA,QAAK,CAAC,WAAW,IAC1B,CAAA,GAAA,8JAAA,CAAA,4BAAyB,AAAD,EAAE,OAC1B;YACA,OAAO,eAAe;QACxB;QAEA,QAAQ,OAAO,CAAC;QAChB,OAAO;IACT;IAEA;;;;;;;;;;GAUC,GACD,SAAS,iCAAiC,IAAI;QAC5C,IACE,SAAS,2JAAA,CAAA,QAAK,CAAC,KAAK,IACpB,SAAS,2JAAA,CAAA,QAAK,CAAC,WAAW,IAC1B,CAAA,GAAA,8JAAA,CAAA,4BAAyB,AAAD,EAAE,OAC1B;YACA,OAAO,eAAe;QACxB;QAEA,OAAO,IAAI;IACb;IAEA;;;;;;;;;GASC,GACD,SAAS,IAAI,IAAI;QACf,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,WAAW,EAAE;YAC9B,QAAQ,OAAO,CAAC;YAChB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,YAAY;YAC/B,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,QAAQ;YAC3B,OAAO;QACT;QAEA,OAAO,IAAI;IACb;IAEA;;;;;;;;;;;;;GAaC,GACD,SAAS,iBAAiB,IAAI;QAC5B,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,aAAa;QACpB,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;QACjC,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,YAAY;QAC/B,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,UAAU;QAC9B,QAAQ,OAAO,CAAC;QAChB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,UAAU;QAC7B,OAAO;IACT;IAEA;;;;;;;;;;;;;GAaC,GACD,SAAS,gBAAgB,IAAI;QAC3B,gCAAgC;QAChC,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EACH,KAAK,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,EACnC;QAEF,OAAO,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE,QACjB,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EACT,SACA,uBACA,2JAAA,CAAA,QAAK,CAAC,UAAU,EAChB,KAAK,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,kBACzC,YACA,+JAAA,CAAA,YAAS,CAAC,OAAO,EACrB,QACF,sBAAsB;IAC5B;IAEA;;;;;;;;;;;;;GAaC,GACD,SAAS,sBAAsB,IAAI;QACjC,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,YAAY;QAChC,OAAO,YAAY;IACrB;AACF", "ignoreList": [0]}}, {"offset": {"line": 4659, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4665, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/node_modules/micromark-core-commonmark/dev/lib/label-start-link.js"], "sourcesContent": ["/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {codes, types} from 'micromark-util-symbol'\nimport {labelEnd} from './label-end.js'\n\n/** @type {Construct} */\nexport const labelStartLink = {\n  name: 'labelStartLink',\n  resolveAll: labelEnd.resolveAll,\n  tokenize: tokenizeLabelStartLink\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeLabelStartLink(effects, ok, nok) {\n  const self = this\n\n  return start\n\n  /**\n   * Start of label (link) start.\n   *\n   * ```markdown\n   * > | a [b] c\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.leftSquareBracket, 'expected `[`')\n    effects.enter(types.labelLink)\n    effects.enter(types.labelMarker)\n    effects.consume(code)\n    effects.exit(types.labelMarker)\n    effects.exit(types.labelLink)\n    return after\n  }\n\n  /** @type {State} */\n  function after(code) {\n    // To do: this isn’t needed in `micromark-extension-gfm-footnote`,\n    // remove.\n    // Hidden footnotes hook.\n    /* c8 ignore next 3 */\n    return code === codes.caret &&\n      '_hiddenFootnoteSupport' in self.parser.constructs\n      ? nok(code)\n      : ok(code)\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AAID;AAFA;AACA;AAAA;;;;AAIO,MAAM,iBAAiB;IAC5B,MAAM;IACN,YAAY,6KAAA,CAAA,WAAQ,CAAC,UAAU;IAC/B,UAAU;AACZ;AAEA;;;;CAIC,GACD,SAAS,uBAAuB,OAAO,EAAE,EAAE,EAAE,GAAG;IAC9C,MAAM,OAAO,IAAI;IAEjB,OAAO;;IAEP;;;;;;;;;GASC,GACD,SAAS,MAAM,IAAI;QACjB,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,SAAS,2JAAA,CAAA,QAAK,CAAC,iBAAiB,EAAE;QACzC,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,SAAS;QAC7B,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,WAAW;QAC/B,QAAQ,OAAO,CAAC;QAChB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,WAAW;QAC9B,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,SAAS;QAC5B,OAAO;IACT;IAEA,kBAAkB,GAClB,SAAS,MAAM,IAAI;QACjB,kEAAkE;QAClE,UAAU;QACV,yBAAyB;QACzB,oBAAoB,GACpB,OAAO,SAAS,2JAAA,CAAA,QAAK,CAAC,KAAK,IACzB,4BAA4B,KAAK,MAAM,CAAC,UAAU,GAChD,IAAI,QACJ,GAAG;IACT;AACF", "ignoreList": [0]}}, {"offset": {"line": 4720, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4726, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/node_modules/micromark-core-commonmark/dev/lib/hard-break-escape.js"], "sourcesContent": ["/**\n * @import {\n *   Construct,\n *   State,\n *   TokenizeContext,\n *   Tokenizer\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {codes, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const hardBreakEscape = {\n  name: 'hardBreakEscape',\n  tokenize: tokenizeHardBreakEscape\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeHardBreakEscape(effects, ok, nok) {\n  return start\n\n  /**\n   * Start of a hard break (escape).\n   *\n   * ```markdown\n   * > | a\\\n   *      ^\n   *   | b\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.backslash, 'expected `\\\\`')\n    effects.enter(types.hardBreakEscape)\n    effects.consume(code)\n    return after\n  }\n\n  /**\n   * After `\\`, at eol.\n   *\n   * ```markdown\n   * > | a\\\n   *       ^\n   *   | b\n   * ```\n   *\n   *  @type {State}\n   */\n  function after(code) {\n    if (markdownLineEnding(code)) {\n      effects.exit(types.hardBreakEscape)\n      return ok(code)\n    }\n\n    return nok(code)\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AAED;AAEA;AAAA;AADA;;;;AAIO,MAAM,kBAAkB;IAC7B,MAAM;IACN,UAAU;AACZ;AAEA;;;;CAIC,GACD,SAAS,wBAAwB,OAAO,EAAE,EAAE,EAAE,GAAG;IAC/C,OAAO;;IAEP;;;;;;;;;;GAUC,GACD,SAAS,MAAM,IAAI;QACjB,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,SAAS,2JAAA,CAAA,QAAK,CAAC,SAAS,EAAE;QACjC,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,eAAe;QACnC,QAAQ,OAAO,CAAC;QAChB,OAAO;IACT;IAEA;;;;;;;;;;GAUC,GACD,SAAS,MAAM,IAAI;QACjB,IAAI,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAC5B,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,eAAe;YAClC,OAAO,GAAG;QACZ;QAEA,OAAO,IAAI;IACb;AACF", "ignoreList": [0]}}, {"offset": {"line": 4788, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4794, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/node_modules/micromark-core-commonmark/dev/lib/code-text.js"], "sourcesContent": ["/**\n * @import {\n *   Construct,\n *   Previous,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer,\n *   Token\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {codes, types} from 'micromark-util-symbol'\n\n/** @type {Construct} */\nexport const codeText = {\n  name: 'codeText',\n  previous,\n  resolve: resolveCodeText,\n  tokenize: tokenizeCodeText\n}\n\n// To do: next major: don’t resolve, like `markdown-rs`.\n/** @type {Resolver} */\nfunction resolveCodeText(events) {\n  let tailExitIndex = events.length - 4\n  let headEnterIndex = 3\n  /** @type {number} */\n  let index\n  /** @type {number | undefined} */\n  let enter\n\n  // If we start and end with an EOL or a space.\n  if (\n    (events[headEnterIndex][1].type === types.lineEnding ||\n      events[headEnterIndex][1].type === 'space') &&\n    (events[tailExitIndex][1].type === types.lineEnding ||\n      events[tailExitIndex][1].type === 'space')\n  ) {\n    index = headEnterIndex\n\n    // And we have data.\n    while (++index < tailExitIndex) {\n      if (events[index][1].type === types.codeTextData) {\n        // Then we have padding.\n        events[headEnterIndex][1].type = types.codeTextPadding\n        events[tailExitIndex][1].type = types.codeTextPadding\n        headEnterIndex += 2\n        tailExitIndex -= 2\n        break\n      }\n    }\n  }\n\n  // Merge adjacent spaces and data.\n  index = headEnterIndex - 1\n  tailExitIndex++\n\n  while (++index <= tailExitIndex) {\n    if (enter === undefined) {\n      if (\n        index !== tailExitIndex &&\n        events[index][1].type !== types.lineEnding\n      ) {\n        enter = index\n      }\n    } else if (\n      index === tailExitIndex ||\n      events[index][1].type === types.lineEnding\n    ) {\n      events[enter][1].type = types.codeTextData\n\n      if (index !== enter + 2) {\n        events[enter][1].end = events[index - 1][1].end\n        events.splice(enter + 2, index - enter - 2)\n        tailExitIndex -= index - enter - 2\n        index = enter + 2\n      }\n\n      enter = undefined\n    }\n  }\n\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Previous}\n */\nfunction previous(code) {\n  // If there is a previous code, there will always be a tail.\n  return (\n    code !== codes.graveAccent ||\n    this.events[this.events.length - 1][1].type === types.characterEscape\n  )\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeCodeText(effects, ok, nok) {\n  const self = this\n  let sizeOpen = 0\n  /** @type {number} */\n  let size\n  /** @type {Token} */\n  let token\n\n  return start\n\n  /**\n   * Start of code (text).\n   *\n   * ```markdown\n   * > | `a`\n   *     ^\n   * > | \\`a`\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function start(code) {\n    assert(code === codes.graveAccent, 'expected `` ` ``')\n    assert(previous.call(self, self.previous), 'expected correct previous')\n    effects.enter(types.codeText)\n    effects.enter(types.codeTextSequence)\n    return sequenceOpen(code)\n  }\n\n  /**\n   * In opening sequence.\n   *\n   * ```markdown\n   * > | `a`\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceOpen(code) {\n    if (code === codes.graveAccent) {\n      effects.consume(code)\n      sizeOpen++\n      return sequenceOpen\n    }\n\n    effects.exit(types.codeTextSequence)\n    return between(code)\n  }\n\n  /**\n   * Between something and something else.\n   *\n   * ```markdown\n   * > | `a`\n   *      ^^\n   * ```\n   *\n   * @type {State}\n   */\n  function between(code) {\n    // EOF.\n    if (code === codes.eof) {\n      return nok(code)\n    }\n\n    // To do: next major: don’t do spaces in resolve, but when compiling,\n    // like `markdown-rs`.\n    // Tabs don’t work, and virtual spaces don’t make sense.\n    if (code === codes.space) {\n      effects.enter('space')\n      effects.consume(code)\n      effects.exit('space')\n      return between\n    }\n\n    // Closing fence? Could also be data.\n    if (code === codes.graveAccent) {\n      token = effects.enter(types.codeTextSequence)\n      size = 0\n      return sequenceClose(code)\n    }\n\n    if (markdownLineEnding(code)) {\n      effects.enter(types.lineEnding)\n      effects.consume(code)\n      effects.exit(types.lineEnding)\n      return between\n    }\n\n    // Data.\n    effects.enter(types.codeTextData)\n    return data(code)\n  }\n\n  /**\n   * In data.\n   *\n   * ```markdown\n   * > | `a`\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function data(code) {\n    if (\n      code === codes.eof ||\n      code === codes.space ||\n      code === codes.graveAccent ||\n      markdownLineEnding(code)\n    ) {\n      effects.exit(types.codeTextData)\n      return between(code)\n    }\n\n    effects.consume(code)\n    return data\n  }\n\n  /**\n   * In closing sequence.\n   *\n   * ```markdown\n   * > | `a`\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function sequenceClose(code) {\n    // More.\n    if (code === codes.graveAccent) {\n      effects.consume(code)\n      size++\n      return sequenceClose\n    }\n\n    // Done!\n    if (size === sizeOpen) {\n      effects.exit(types.codeTextSequence)\n      effects.exit(types.codeText)\n      return ok(code)\n    }\n\n    // More or less accents: mark as data.\n    token.type = types.codeTextData\n    return data(code)\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;AAID;AAAA;AAFA;AACA;;;;AAIO,MAAM,WAAW;IACtB,MAAM;IACN;IACA,SAAS;IACT,UAAU;AACZ;AAEA,wDAAwD;AACxD,qBAAqB,GACrB,SAAS,gBAAgB,MAAM;IAC7B,IAAI,gBAAgB,OAAO,MAAM,GAAG;IACpC,IAAI,iBAAiB;IACrB,mBAAmB,GACnB,IAAI;IACJ,+BAA+B,GAC/B,IAAI;IAEJ,8CAA8C;IAC9C,IACE,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE,CAAC,IAAI,KAAK,2JAAA,CAAA,QAAK,CAAC,UAAU,IAClD,MAAM,CAAC,eAAe,CAAC,EAAE,CAAC,IAAI,KAAK,OAAO,KAC5C,CAAC,MAAM,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,KAAK,2JAAA,CAAA,QAAK,CAAC,UAAU,IACjD,MAAM,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,KAAK,OAAO,GAC3C;QACA,QAAQ;QAER,oBAAoB;QACpB,MAAO,EAAE,QAAQ,cAAe;YAC9B,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,2JAAA,CAAA,QAAK,CAAC,YAAY,EAAE;gBAChD,wBAAwB;gBACxB,MAAM,CAAC,eAAe,CAAC,EAAE,CAAC,IAAI,GAAG,2JAAA,CAAA,QAAK,CAAC,eAAe;gBACtD,MAAM,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,GAAG,2JAAA,CAAA,QAAK,CAAC,eAAe;gBACrD,kBAAkB;gBAClB,iBAAiB;gBACjB;YACF;QACF;IACF;IAEA,kCAAkC;IAClC,QAAQ,iBAAiB;IACzB;IAEA,MAAO,EAAE,SAAS,cAAe;QAC/B,IAAI,UAAU,WAAW;YACvB,IACE,UAAU,iBACV,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,2JAAA,CAAA,QAAK,CAAC,UAAU,EAC1C;gBACA,QAAQ;YACV;QACF,OAAO,IACL,UAAU,iBACV,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,2JAAA,CAAA,QAAK,CAAC,UAAU,EAC1C;YACA,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,GAAG,2JAAA,CAAA,QAAK,CAAC,YAAY;YAE1C,IAAI,UAAU,QAAQ,GAAG;gBACvB,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,GAAG;gBAC/C,OAAO,MAAM,CAAC,QAAQ,GAAG,QAAQ,QAAQ;gBACzC,iBAAiB,QAAQ,QAAQ;gBACjC,QAAQ,QAAQ;YAClB;YAEA,QAAQ;QACV;IACF;IAEA,OAAO;AACT;AAEA;;;;CAIC,GACD,SAAS,SAAS,IAAI;IACpB,4DAA4D;IAC5D,OACE,SAAS,2JAAA,CAAA,QAAK,CAAC,WAAW,IAC1B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,EAAE,CAAC,EAAE,CAAC,IAAI,KAAK,2JAAA,CAAA,QAAK,CAAC,eAAe;AAEzE;AAEA;;;;CAIC,GACD,SAAS,iBAAiB,OAAO,EAAE,EAAE,EAAE,GAAG;IACxC,MAAM,OAAO,IAAI;IACjB,IAAI,WAAW;IACf,mBAAmB,GACnB,IAAI;IACJ,kBAAkB,GAClB,IAAI;IAEJ,OAAO;;IAEP;;;;;;;;;;;GAWC,GACD,SAAS,MAAM,IAAI;QACjB,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,SAAS,2JAAA,CAAA,QAAK,CAAC,WAAW,EAAE;QACnC,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,SAAS,IAAI,CAAC,MAAM,KAAK,QAAQ,GAAG;QAC3C,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,QAAQ;QAC5B,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,gBAAgB;QACpC,OAAO,aAAa;IACtB;IAEA;;;;;;;;;GASC,GACD,SAAS,aAAa,IAAI;QACxB,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,WAAW,EAAE;YAC9B,QAAQ,OAAO,CAAC;YAChB;YACA,OAAO;QACT;QAEA,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,gBAAgB;QACnC,OAAO,QAAQ;IACjB;IAEA;;;;;;;;;GASC,GACD,SAAS,QAAQ,IAAI;QACnB,OAAO;QACP,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,EAAE;YACtB,OAAO,IAAI;QACb;QAEA,qEAAqE;QACrE,sBAAsB;QACtB,wDAAwD;QACxD,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,KAAK,EAAE;YACxB,QAAQ,KAAK,CAAC;YACd,QAAQ,OAAO,CAAC;YAChB,QAAQ,IAAI,CAAC;YACb,OAAO;QACT;QAEA,qCAAqC;QACrC,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,WAAW,EAAE;YAC9B,QAAQ,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,gBAAgB;YAC5C,OAAO;YACP,OAAO,cAAc;QACvB;QAEA,IAAI,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAC5B,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,UAAU;YAC9B,QAAQ,OAAO,CAAC;YAChB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,UAAU;YAC7B,OAAO;QACT;QAEA,QAAQ;QACR,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,YAAY;QAChC,OAAO,KAAK;IACd;IAEA;;;;;;;;;GASC,GACD,SAAS,KAAK,IAAI;QAChB,IACE,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,IAClB,SAAS,2JAAA,CAAA,QAAK,CAAC,KAAK,IACpB,SAAS,2JAAA,CAAA,QAAK,CAAC,WAAW,IAC1B,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OACnB;YACA,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,YAAY;YAC/B,OAAO,QAAQ;QACjB;QAEA,QAAQ,OAAO,CAAC;QAChB,OAAO;IACT;IAEA;;;;;;;;;GASC,GACD,SAAS,cAAc,IAAI;QACzB,QAAQ;QACR,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,WAAW,EAAE;YAC9B,QAAQ,OAAO,CAAC;YAChB;YACA,OAAO;QACT;QAEA,QAAQ;QACR,IAAI,SAAS,UAAU;YACrB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,gBAAgB;YACnC,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,QAAQ;YAC3B,OAAO,GAAG;QACZ;QAEA,sCAAsC;QACtC,MAAM,IAAI,GAAG,2JAAA,CAAA,QAAK,CAAC,YAAY;QAC/B,OAAO,KAAK;IACd;AACF", "ignoreList": [0]}}, {"offset": {"line": 5000, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5006, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/node_modules/micromark-core-commonmark/dev/lib/content.js"], "sourcesContent": ["/**\n * @import {\n *   Construct,\n *   Resolver,\n *   State,\n *   TokenizeContext,\n *   Tokenizer,\n *   Token\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {subtokenize} from 'micromark-util-subtokenize'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/**\n * No name because it must not be turned off.\n * @type {Construct}\n */\nexport const content = {resolve: resolveContent, tokenize: tokenizeContent}\n\n/** @type {Construct} */\nconst continuationConstruct = {partial: true, tokenize: tokenizeContinuation}\n\n/**\n * Content is transparent: it’s parsed right now. That way, definitions are also\n * parsed right now: before text in paragraphs (specifically, media) are parsed.\n *\n * @type {Resolver}\n */\nfunction resolveContent(events) {\n  subtokenize(events)\n  return events\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeContent(effects, ok) {\n  /** @type {Token | undefined} */\n  let previous\n\n  return chunkStart\n\n  /**\n   * Before a content chunk.\n   *\n   * ```markdown\n   * > | abc\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function chunkStart(code) {\n    assert(\n      code !== codes.eof && !markdownLineEnding(code),\n      'expected no eof or eol'\n    )\n\n    effects.enter(types.content)\n    previous = effects.enter(types.chunkContent, {\n      contentType: constants.contentTypeContent\n    })\n    return chunkInside(code)\n  }\n\n  /**\n   * In a content chunk.\n   *\n   * ```markdown\n   * > | abc\n   *     ^^^\n   * ```\n   *\n   * @type {State}\n   */\n  function chunkInside(code) {\n    if (code === codes.eof) {\n      return contentEnd(code)\n    }\n\n    // To do: in `markdown-rs`, each line is parsed on its own, and everything\n    // is stitched together resolving.\n    if (markdownLineEnding(code)) {\n      return effects.check(\n        continuationConstruct,\n        contentContinue,\n        contentEnd\n      )(code)\n    }\n\n    // Data.\n    effects.consume(code)\n    return chunkInside\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function contentEnd(code) {\n    effects.exit(types.chunkContent)\n    effects.exit(types.content)\n    return ok(code)\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function contentContinue(code) {\n    assert(markdownLineEnding(code), 'expected eol')\n    effects.consume(code)\n    effects.exit(types.chunkContent)\n    assert(previous, 'expected previous token')\n    previous.next = effects.enter(types.chunkContent, {\n      contentType: constants.contentTypeContent,\n      previous\n    })\n    previous = previous.next\n    return chunkInside\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n */\nfunction tokenizeContinuation(effects, ok, nok) {\n  const self = this\n\n  return startLookahead\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function startLookahead(code) {\n    assert(markdownLineEnding(code), 'expected a line ending')\n    effects.exit(types.chunkContent)\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    return factorySpace(effects, prefixed, types.linePrefix)\n  }\n\n  /**\n   *\n   *\n   * @type {State}\n   */\n  function prefixed(code) {\n    if (code === codes.eof || markdownLineEnding(code)) {\n      return nok(code)\n    }\n\n    // Always populated by defaults.\n    assert(\n      self.parser.constructs.disable.null,\n      'expected `disable.null` to be populated'\n    )\n\n    const tail = self.events[self.events.length - 1]\n\n    if (\n      !self.parser.constructs.disable.null.includes('codeIndented') &&\n      tail &&\n      tail[1].type === types.linePrefix &&\n      tail[2].sliceSerialize(tail[1], true).length >= constants.tabSize\n    ) {\n      return ok(code)\n    }\n\n    return effects.interrupt(self.parser.constructs.flow, nok, ok)(code)\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;CASC;;;AAKD;AAHA;AAIA;AAFA;AAEA;AAAA;AAHA;;;;;;AASO,MAAM,UAAU;IAAC,SAAS;IAAgB,UAAU;AAAe;AAE1E,sBAAsB,GACtB,MAAM,wBAAwB;IAAC,SAAS;IAAM,UAAU;AAAoB;AAE5E;;;;;CAKC,GACD,SAAS,eAAe,MAAM;IAC5B,CAAA,GAAA,gLAAA,CAAA,cAAW,AAAD,EAAE;IACZ,OAAO;AACT;AAEA;;;;CAIC,GACD,SAAS,gBAAgB,OAAO,EAAE,EAAE;IAClC,8BAA8B,GAC9B,IAAI;IAEJ,OAAO;;IAEP;;;;;;;;;GASC,GACD,SAAS,WAAW,IAAI;QACtB,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EACH,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,IAAI,CAAC,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAC1C;QAGF,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,OAAO;QAC3B,WAAW,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,YAAY,EAAE;YAC3C,aAAa,+JAAA,CAAA,YAAS,CAAC,kBAAkB;QAC3C;QACA,OAAO,YAAY;IACrB;IAEA;;;;;;;;;GASC,GACD,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,EAAE;YACtB,OAAO,WAAW;QACpB;QAEA,0EAA0E;QAC1E,kCAAkC;QAClC,IAAI,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAC5B,OAAO,QAAQ,KAAK,CAClB,uBACA,iBACA,YACA;QACJ;QAEA,QAAQ;QACR,QAAQ,OAAO,CAAC;QAChB,OAAO;IACT;IAEA;;;;GAIC,GACD,SAAS,WAAW,IAAI;QACtB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,YAAY;QAC/B,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,OAAO;QAC1B,OAAO,GAAG;IACZ;IAEA;;;;GAIC,GACD,SAAS,gBAAgB,IAAI;QAC3B,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;QACjC,QAAQ,OAAO,CAAC;QAChB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,YAAY;QAC/B,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,UAAU;QACjB,SAAS,IAAI,GAAG,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,YAAY,EAAE;YAChD,aAAa,+JAAA,CAAA,YAAS,CAAC,kBAAkB;YACzC;QACF;QACA,WAAW,SAAS,IAAI;QACxB,OAAO;IACT;AACF;AAEA;;;;CAIC,GACD,SAAS,qBAAqB,OAAO,EAAE,EAAE,EAAE,GAAG;IAC5C,MAAM,OAAO,IAAI;IAEjB,OAAO;;IAEP;;;;GAIC,GACD,SAAS,eAAe,IAAI;QAC1B,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EAAE,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;QACjC,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,YAAY;QAC/B,QAAQ,KAAK,CAAC,2JAAA,CAAA,QAAK,CAAC,UAAU;QAC9B,QAAQ,OAAO,CAAC;QAChB,QAAQ,IAAI,CAAC,2JAAA,CAAA,QAAK,CAAC,UAAU;QAC7B,OAAO,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EAAE,SAAS,UAAU,2JAAA,CAAA,QAAK,CAAC,UAAU;IACzD;IAEA;;;;GAIC,GACD,SAAS,SAAS,IAAI;QACpB,IAAI,SAAS,2JAAA,CAAA,QAAK,CAAC,GAAG,IAAI,CAAA,GAAA,8JAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO;YAClD,OAAO,IAAI;QACb;QAEA,gCAAgC;QAChC,CAAA,GAAA,4IAAA,CAAA,KAAM,AAAD,EACH,KAAK,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,EACnC;QAGF,MAAM,OAAO,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC,MAAM,GAAG,EAAE;QAEhD,IACE,CAAC,KAAK,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,mBAC9C,QACA,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,2JAAA,CAAA,QAAK,CAAC,UAAU,IACjC,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,MAAM,IAAI,+JAAA,CAAA,YAAS,CAAC,OAAO,EACjE;YACA,OAAO,GAAG;QACZ;QAEA,OAAO,QAAQ,SAAS,CAAC,KAAK,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE,KAAK,IAAI;IACjE;AACF", "ignoreList": [0]}}, {"offset": {"line": 5157, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}