(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/node_modules_@shikijs_langs_dist_tasl_mjs_4010ae._.js", {

"[project]/node_modules/@shikijs/langs/dist/tasl.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
const lang = Object.freeze(JSON.parse("{\"displayName\":\"Tasl\",\"fileTypes\":[\"tasl\"],\"name\":\"tasl\",\"patterns\":[{\"include\":\"#comment\"},{\"include\":\"#namespace\"},{\"include\":\"#type\"},{\"include\":\"#class\"},{\"include\":\"#edge\"}],\"repository\":{\"class\":{\"begin\":\"(?:^\\\\s*)(class)\\\\b\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.control.tasl.class\"}},\"end\":\"$\",\"patterns\":[{\"include\":\"#key\"},{\"include\":\"#export\"},{\"include\":\"#expression\"}]},\"comment\":{\"captures\":{\"1\":{\"name\":\"punctuation.definition.comment.tasl\"}},\"match\":\"(#).*$\",\"name\":\"comment.line.number-sign.tasl\"},\"component\":{\"begin\":\"->\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.separator.tasl.component\"}},\"end\":\"$\",\"patterns\":[{\"include\":\"#expression\"}]},\"coproduct\":{\"begin\":\"\\\\[\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.block.tasl.coproduct\"}},\"end\":\"\\\\]\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.block.tasl.coproduct\"}},\"patterns\":[{\"include\":\"#comment\"},{\"include\":\"#term\"},{\"include\":\"#option\"}]},\"datatype\":{\"match\":\"[a-zA-Z][a-zA-Z0-9]*:(?:[A-Za-z0-9\\\\-._~!$&'()*+,;=:@/?]|%[0-9A-Fa-f]{2})+\",\"name\":\"string.regexp\"},\"edge\":{\"begin\":\"(?:^\\\\s*)(edge)\\\\b\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.control.tasl.edge\"}},\"end\":\"$\",\"patterns\":[{\"include\":\"#key\"},{\"include\":\"#export\"},{\"match\":\"=/\",\"name\":\"punctuation.separator.tasl.edge.source\"},{\"match\":\"/=>\",\"name\":\"punctuation.separator.tasl.edge.target\"},{\"match\":\"=>\",\"name\":\"punctuation.separator.tasl.edge\"},{\"include\":\"#expression\"}]},\"export\":{\"match\":\"::\",\"name\":\"keyword.operator.tasl.export\"},\"expression\":{\"patterns\":[{\"include\":\"#literal\"},{\"include\":\"#uri\"},{\"include\":\"#product\"},{\"include\":\"#coproduct\"},{\"include\":\"#reference\"},{\"include\":\"#optional\"},{\"include\":\"#identifier\"}]},\"identifier\":{\"captures\":{\"1\":{\"name\":\"variable\"}},\"match\":\"([a-zA-Z][a-zA-Z0-9]*)\\\\b\"},\"key\":{\"match\":\"[a-zA-Z][a-zA-Z0-9]*:(?:[A-Za-z0-9\\\\-._~!$&'()*+,;=:@/?]|%[0-9A-Fa-f]{2})+\",\"name\":\"markup.bold entity.name.class\"},\"literal\":{\"patterns\":[{\"include\":\"#datatype\"}]},\"namespace\":{\"captures\":{\"1\":{\"name\":\"keyword.control.tasl.namespace\"},\"2\":{\"patterns\":[{\"include\":\"#namespaceURI\"},{\"match\":\"[a-zA-Z][a-zA-Z0-9]*\\\\b\",\"name\":\"entity.name\"}]}},\"match\":\"(?:^\\\\s*)(namespace)\\\\b(.*)\"},\"namespaceURI\":{\"match\":\"[a-z]+:[a-zA-Z0-9-._~:\\\\/?#\\\\[\\\\]@!$&'()*+,;%=]+\",\"name\":\"markup.underline.link\"},\"option\":{\"begin\":\"<-\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.separator.tasl.option\"}},\"end\":\"$\",\"patterns\":[{\"include\":\"#expression\"}]},\"optional\":{\"begin\":\"\\\\?\",\"beginCaptures\":{\"0\":{\"name\":\"keyword.operator\"}},\"end\":\"$\",\"patterns\":[{\"include\":\"#expression\"}]},\"product\":{\"begin\":\"{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.block.tasl.product\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.block.tasl.product\"}},\"patterns\":[{\"include\":\"#comment\"},{\"include\":\"#term\"},{\"include\":\"#component\"}]},\"reference\":{\"captures\":{\"1\":{\"name\":\"markup.bold keyword.operator\"},\"2\":{\"patterns\":[{\"include\":\"#key\"}]}},\"match\":\"(\\\\*)\\\\s*(.*)\"},\"term\":{\"match\":\"[a-zA-Z][a-zA-Z0-9]*:(?:[A-Za-z0-9\\\\-._~!$&'()*+,;=:@/?]|%[0-9A-Fa-f]{2})+\",\"name\":\"entity.other.tasl.key\"},\"type\":{\"begin\":\"(?:^\\\\s*)(type)\\\\b\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.control.tasl.type\"}},\"end\":\"$\",\"patterns\":[{\"include\":\"#expression\"}]},\"uri\":{\"match\":\"<>\",\"name\":\"variable.other.constant\"}},\"scopeName\":\"source.tasl\"}"));
const __TURBOPACK__default__export__ = [
    lang
];
}}),
}]);

//# sourceMappingURL=node_modules_%40shikijs_langs_dist_tasl_mjs_4010ae._.js.map