<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tamil Translation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .tamil-text {
            font-size: 18px;
            color: #2563eb;
            font-weight: bold;
            margin: 10px 0;
        }
        .english-text {
            font-size: 16px;
            color: #059669;
            margin: 10px 0;
        }
        .test-button {
            background-color: #3b82f6;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .test-button:hover {
            background-color: #2563eb;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            background-color: #f0f9ff;
            border-left: 4px solid #3b82f6;
        }
        .error {
            background-color: #fef2f2;
            border-left-color: #ef4444;
            color: #dc2626;
        }
        .success {
            background-color: #f0fdf4;
            border-left-color: #22c55e;
            color: #16a34a;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Tamil Translation Test for Financial Query System</h1>
        <p>This page tests the Tamil language translation functionality that should work with the financial query system.</p>

        <div class="test-section">
            <h3>Test 1: Tamil Financial Query</h3>
            <div class="tamil-text">பணம் பற்றி சொல்லுங்கள்</div>
            <div class="english-text">Expected Translation: "tell me about money"</div>
            <button class="test-button" onclick="testTamilToEnglish('பணம் பற்றி சொல்லுங்கள்', 'test1')">Test Translation</button>
            <div id="test1-result"></div>
        </div>

        <div class="test-section">
            <h3>Test 2: Tamil Investment Query</h3>
            <div class="tamil-text">முதலீடு எப்படி செய்வது</div>
            <div class="english-text">Expected Translation: "how to do investment"</div>
            <button class="test-button" onclick="testTamilToEnglish('முதலீடு எப்படி செய்வது', 'test2')">Test Translation</button>
            <div id="test2-result"></div>
        </div>

        <div class="test-section">
            <h3>Test 3: English to Tamil Response</h3>
            <div class="english-text">Investment is a way to grow your money over time</div>
            <div class="tamil-text">Expected Translation: "முதலீடு உங்கள் பணம் வளர்ச்சி நேரம்"</div>
            <button class="test-button" onclick="testEnglishToTamil('Investment is a way to grow your money over time', 'test3')">Test Translation</button>
            <div id="test3-result"></div>
        </div>

        <div class="test-section">
            <h3>Test 4: Language Detection</h3>
            <button class="test-button" onclick="testLanguageDetection('பணம் வங்கி முதலீடு', 'test4')">Test Tamil Detection</button>
            <button class="test-button" onclick="testLanguageDetection('money bank investment', 'test4')">Test English Detection</button>
            <div id="test4-result"></div>
        </div>

        <div class="test-section">
            <h3>Test 5: Full Query Simulation</h3>
            <p>This simulates the complete flow: Tamil Query → English Translation → API Call → Tamil Response</p>
            <button class="test-button" onclick="simulateFullQuery('வங்கி கடன் என்ன', 'test5')">Simulate Tamil Query</button>
            <div id="test5-result"></div>
        </div>
    </div>

    <script>
        // Import the translation service (simulated for testing)
        class TestTranslationService {
            static detectLanguage(text) {
                // Tamil detection using Unicode ranges
                if (/[\u0B80-\u0BFF]/.test(text)) return 'ta';
                return 'en';
            }

            static async dictionaryBasedTranslation(text, sourceLang, targetLang) {
                const tamilToEnglish = {
                    'பணம்': 'money', 'வங்கி': 'bank', 'கடன்': 'loan', 'முதலீடு': 'investment',
                    'பங்கு': 'share', 'சந்தை': 'market', 'விலை': 'price', 'வட்டி': 'interest',
                    'பற்றி சொல்லுங்கள்': 'tell me about', 'எப்படி செய்வது': 'how to do',
                    'என்ன': 'what', 'எப்படி': 'how', 'எங்கே': 'where'
                };

                const englishToTamil = {
                    'money': 'பணம்', 'bank': 'வங்கி', 'loan': 'கடன்', 'investment': 'முதலீடு',
                    'share': 'பங்கு', 'market': 'சந்தை', 'price': 'விலை', 'interest': 'வட்டி',
                    'is': 'உள்ளது', 'a': 'ஒரு', 'way': 'வழி', 'to': 'க்கு', 'grow': 'வளர்ச்சி',
                    'your': 'உங்கள்', 'over': 'மீது', 'time': 'நேரம்'
                };

                let translatedText = text;
                let dictionary = {};

                if (sourceLang === 'ta' && targetLang === 'en') {
                    dictionary = tamilToEnglish;
                } else if (sourceLang === 'en' && targetLang === 'ta') {
                    dictionary = englishToTamil;
                }

                for (const [sourceWord, targetWord] of Object.entries(dictionary)) {
                    const regex = new RegExp(`\\b${sourceWord.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'gi');
                    translatedText = translatedText.replace(regex, targetWord);
                }

                return `[Dictionary] ${translatedText}`;
            }
        }

        async function testTamilToEnglish(tamilText, testId) {
            const resultDiv = document.getElementById(testId + '-result');
            resultDiv.innerHTML = '<div class="result">Testing translation...</div>';

            try {
                const translation = await TestTranslationService.dictionaryBasedTranslation(tamilText, 'ta', 'en');
                resultDiv.innerHTML = `<div class="result success">
                    <strong>Original:</strong> ${tamilText}<br>
                    <strong>Translated:</strong> ${translation}
                </div>`;
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">Error: ${error.message}</div>`;
            }
        }

        async function testEnglishToTamil(englishText, testId) {
            const resultDiv = document.getElementById(testId + '-result');
            resultDiv.innerHTML = '<div class="result">Testing translation...</div>';

            try {
                const translation = await TestTranslationService.dictionaryBasedTranslation(englishText, 'en', 'ta');
                resultDiv.innerHTML = `<div class="result success">
                    <strong>Original:</strong> ${englishText}<br>
                    <strong>Translated:</strong> ${translation}
                </div>`;
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">Error: ${error.message}</div>`;
            }
        }

        function testLanguageDetection(text, testId) {
            const resultDiv = document.getElementById(testId + '-result');
            const detectedLang = TestTranslationService.detectLanguage(text);
            const langName = detectedLang === 'ta' ? 'Tamil' : 'English';
            
            resultDiv.innerHTML = `<div class="result success">
                <strong>Text:</strong> ${text}<br>
                <strong>Detected Language:</strong> ${langName} (${detectedLang})
            </div>`;
        }

        async function simulateFullQuery(tamilQuery, testId) {
            const resultDiv = document.getElementById(testId + '-result');
            resultDiv.innerHTML = '<div class="result">Simulating full query flow...</div>';

            try {
                // Step 1: Detect language
                const detectedLang = TestTranslationService.detectLanguage(tamilQuery);
                
                // Step 2: Translate to English
                const englishQuery = await TestTranslationService.dictionaryBasedTranslation(tamilQuery, 'ta', 'en');
                
                // Step 3: Simulate API response
                const mockApiResponse = "A bank loan is money borrowed from a financial institution that must be repaid with interest.";
                
                // Step 4: Translate response back to Tamil
                const tamilResponse = await TestTranslationService.dictionaryBasedTranslation(mockApiResponse, 'en', 'ta');

                resultDiv.innerHTML = `<div class="result success">
                    <strong>1. Original Tamil Query:</strong> ${tamilQuery}<br>
                    <strong>2. Detected Language:</strong> ${detectedLang}<br>
                    <strong>3. Translated to English:</strong> ${englishQuery}<br>
                    <strong>4. Mock API Response:</strong> ${mockApiResponse}<br>
                    <strong>5. Final Tamil Response:</strong> ${tamilResponse}
                </div>`;
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">Error: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
