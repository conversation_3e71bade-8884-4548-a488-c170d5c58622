module.exports = {

"[project]/public/images/favicon.ico [app-ssr] (static)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__("/_next/static/media/favicon.7ab433ae.ico");}}),
"[project]/public/images/favicon.ico.mjs { IMAGE => \"[project]/public/images/favicon.ico [app-ssr] (static)\" } [app-ssr] (structured image object, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$favicon$2e$ico__$5b$app$2d$ssr$5d$__$28$static$29$__ = __turbopack_import__("[project]/public/images/favicon.ico [app-ssr] (static)");
;
const __TURBOPACK__default__export__ = {
    src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$favicon$2e$ico__$5b$app$2d$ssr$5d$__$28$static$29$__["default"],
    width: 58,
    height: 54,
    blurDataURL: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAHCAYAAAA1WQxeAAAA8klEQVR42gHnABj/AAEBAgEUJTAqN2uRkkWOxM8/hre8IUpiWwUKDQkAAAAAABIpMy04kL3HPqnj+j2l2+48qeD1Oqnd7yJfdm8DBgcEACqAnJg0uOf8MI2vqhQ0QTceVGteMLbh6i2v1NwPKzMpACyryMMqx+36IGBtWwABAQAKGx4TKLLPyyfK6vMVSVNJACGVpJke1/L9Ia6+tRdUWkklcoBzMLrh7iPF3+QONjovAA44OiwZws3HE+f2/BXm9fUc3fL6Kcjt/h/Q6e8RXGFRAAECAgEMNjcrE6CklhHQ1McSwcW2GZCbjxnE0ssPZmpRi6piXKCTQHwAAAAASUVORK5CYII=",
    blurWidth: 8,
    blurHeight: 7
};
}}),
"[project]/components/chatComponents/UploadDropdown.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/react-icons/pi/index.mjs [app-ssr] (ecmascript)");
;
;
const uploadOptions = [
    {
        type: 'pdf',
        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiFilePdf"], {
            className: "w-5 h-5"
        }, void 0, false, {
            fileName: "[project]/components/chatComponents/UploadDropdown.tsx",
            lineNumber: 30,
            columnNumber: 11
        }, this),
        label: {
            English: 'PDF/Document',
            Tamil: 'PDF/ஆவணம்',
            Telugu: 'PDF/పత్రం',
            Kannada: 'PDF/ದಾಖಲೆ'
        },
        description: {
            English: 'Upload PDF or document files',
            Tamil: 'PDF அல்லது ஆவண கோப்புகளை பதிவேற்றவும்',
            Telugu: 'PDF లేదా పత్రం ఫైల్‌లను అప్‌లోడ్ చేయండి',
            Kannada: 'PDF ಅಥವಾ ದಾಖಲೆ ಫೈಲ್‌ಗಳನ್ನು ಅಪ್‌ಲೋಡ್ ಮಾಡಿ'
        }
    },
    {
        type: 'youtube',
        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiYoutubeLogo"], {
            className: "w-5 h-5"
        }, void 0, false, {
            fileName: "[project]/components/chatComponents/UploadDropdown.tsx",
            lineNumber: 46,
            columnNumber: 11
        }, this),
        label: {
            English: 'YouTube URL',
            Tamil: 'YouTube URL',
            Telugu: 'YouTube URL',
            Kannada: 'YouTube URL'
        },
        description: {
            English: 'Add YouTube video link',
            Tamil: 'YouTube வீடியோ இணைப்பைச் சேர்க்கவும்',
            Telugu: 'YouTube వీడియో లింక్ జోడించండి',
            Kannada: 'YouTube ವೀಡಿಯೊ ಲಿಂಕ್ ಸೇರಿಸಿ'
        }
    },
    {
        type: 'article',
        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiGlobe"], {
            className: "w-5 h-5"
        }, void 0, false, {
            fileName: "[project]/components/chatComponents/UploadDropdown.tsx",
            lineNumber: 62,
            columnNumber: 11
        }, this),
        label: {
            English: 'Article URL',
            Tamil: 'கட்டுரை URL',
            Telugu: 'వ్యాసం URL',
            Kannada: 'ಲೇಖನ URL'
        },
        description: {
            English: 'Add article or webpage link',
            Tamil: 'கட்டுரை அல்லது வலைப்பக்க இணைப்பைச் சேர்க்கவும்',
            Telugu: 'వ్యాసం లేదా వెబ్‌పేజీ లింక్ జోడించండి',
            Kannada: 'ಲೇಖನ ಅಥವಾ ವೆಬ್‌ಪುಟದ ಲಿಂಕ್ ಸೇರಿಸಿ'
        }
    },
    {
        type: 'mp3',
        icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiMusicNote"], {
            className: "w-5 h-5"
        }, void 0, false, {
            fileName: "[project]/components/chatComponents/UploadDropdown.tsx",
            lineNumber: 78,
            columnNumber: 11
        }, this),
        label: {
            English: 'MP3 Audio',
            Tamil: 'MP3 ஆடியோ',
            Telugu: 'MP3 ఆడియో',
            Kannada: 'MP3 ಆಡಿಯೊ'
        },
        description: {
            English: 'Upload audio files',
            Tamil: 'ஆடியோ கோப்புகளை பதிவேற்றவும்',
            Telugu: 'ఆడియో ఫైల్‌లను అప్‌లోడ్ చేయండి',
            Kannada: 'ಆಡಿಯೊ ಫೈಲ್‌ಗಳನ್ನು ಅಪ್‌ಲೋಡ್ ಮಾಡಿ'
        }
    }
];
const UploadDropdown = ({ onSelect, selectedLanguage })=>{
    const getLanguageKey = ()=>{
        switch(selectedLanguage){
            case 'Tamil':
                return 'Tamil';
            case 'Telugu':
                return 'Telugu';
            case 'Kannada':
                return 'Kannada';
            default:
                return 'English';
        }
    };
    const getHoverColor = ()=>{
        switch(selectedLanguage){
            case 'Tamil':
                return 'hover:bg-purple-50 hover:border-purple-200';
            case 'Telugu':
                return 'hover:bg-green-50 hover:border-green-200';
            case 'Kannada':
                return 'hover:bg-orange-50 hover:border-orange-200';
            default:
                return 'hover:bg-blue-50 hover:border-blue-200';
        }
    };
    const getIconColor = (index)=>{
        const colors = {
            Tamil: [
                'text-purple-600',
                'text-purple-500',
                'text-purple-700',
                'text-purple-400'
            ],
            Telugu: [
                'text-green-600',
                'text-green-500',
                'text-green-700',
                'text-green-400'
            ],
            Kannada: [
                'text-orange-600',
                'text-orange-500',
                'text-orange-700',
                'text-orange-400'
            ],
            English: [
                'text-blue-600',
                'text-blue-500',
                'text-blue-700',
                'text-blue-400'
            ]
        };
        const languageColors = colors[selectedLanguage] || colors.English;
        return languageColors[index % languageColors.length];
    };
    const languageKey = getLanguageKey();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "absolute bottom-full right-0 mb-2 w-64 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600 shadow-lg z-50 overflow-hidden animate-fadeIn",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "p-2",
            children: uploadOptions.map((option, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    onClick: ()=>onSelect(option.type),
                    className: `w-full flex items-start gap-3 p-3 rounded-lg border border-transparent transition-all ${getHoverColor()}`,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: `flex-shrink-0 ${getIconColor(index)}`,
                            children: option.icon
                        }, void 0, false, {
                            fileName: "[project]/components/chatComponents/UploadDropdown.tsx",
                            lineNumber: 144,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex-grow text-left",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "font-medium text-sm text-gray-900 dark:text-gray-100",
                                    children: option.label[languageKey]
                                }, void 0, false, {
                                    fileName: "[project]/components/chatComponents/UploadDropdown.tsx",
                                    lineNumber: 148,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-xs text-gray-500 dark:text-gray-400 mt-0.5",
                                    children: option.description[languageKey]
                                }, void 0, false, {
                                    fileName: "[project]/components/chatComponents/UploadDropdown.tsx",
                                    lineNumber: 151,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/chatComponents/UploadDropdown.tsx",
                            lineNumber: 147,
                            columnNumber: 13
                        }, this)
                    ]
                }, option.type, true, {
                    fileName: "[project]/components/chatComponents/UploadDropdown.tsx",
                    lineNumber: 139,
                    columnNumber: 11
                }, this))
        }, void 0, false, {
            fileName: "[project]/components/chatComponents/UploadDropdown.tsx",
            lineNumber: 137,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/chatComponents/UploadDropdown.tsx",
        lineNumber: 136,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = UploadDropdown;
}}),
"[project]/components/chatComponents/FileDropZone.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/react-icons/pi/index.mjs [app-ssr] (ecmascript)");
;
;
;
const FileDropZone = ({ acceptedTypes, onFilesSelected, maxFiles = 1, maxFileSize = 50, selectedLanguage })=>{
    const [isDragging, setIsDragging] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [files, setFiles] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const fileInputRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const getLanguageText = ()=>{
        switch(selectedLanguage){
            case 'Tamil':
                return {
                    dragText: 'கோப்புகளை இங்கே இழுத்து விடவும் அல்லது உலாவ கிளிக் செய்யவும்',
                    browseText: 'உலாவு',
                    maxSizeText: `அதிகபட்சம் ${maxFileSize}MB`,
                    removeText: 'அகற்று',
                    selectedText: 'தேர்ந்தெடுக்கப்பட்டது'
                };
            case 'Telugu':
                return {
                    dragText: 'ఫైల్‌లను ఇక్కడ లాగండి లేదా బ్రౌజ్ చేయడానికి క్లిక్ చేయండి',
                    browseText: 'బ్రౌజ్',
                    maxSizeText: `గరిష్టంగా ${maxFileSize}MB`,
                    removeText: 'తొలగించు',
                    selectedText: 'ఎంచుకోబడింది'
                };
            case 'Kannada':
                return {
                    dragText: 'ಫೈಲ್‌ಗಳನ್ನು ಇಲ್ಲಿ ಎಳೆಯಿರಿ ಅಥವಾ ಬ್ರೌಸ್ ಮಾಡಲು ಕ್ಲಿಕ್ ಮಾಡಿ',
                    browseText: 'ಬ್ರೌಸ್',
                    maxSizeText: `ಗರಿಷ್ಠ ${maxFileSize}MB`,
                    removeText: 'ತೆಗೆದುಹಾಕಿ',
                    selectedText: 'ಆಯ್ಕೆಮಾಡಲಾಗಿದೆ'
                };
            default:
                return {
                    dragText: 'Drag files here or click to browse',
                    browseText: 'Browse',
                    maxSizeText: `Max ${maxFileSize}MB`,
                    removeText: 'Remove',
                    selectedText: 'Selected'
                };
        }
    };
    const getBorderColor = ()=>{
        switch(selectedLanguage){
            case 'Tamil':
                return isDragging ? 'border-purple-400 bg-purple-50' : 'border-purple-300';
            case 'Telugu':
                return isDragging ? 'border-green-400 bg-green-50' : 'border-green-300';
            case 'Kannada':
                return isDragging ? 'border-orange-400 bg-orange-50' : 'border-orange-300';
            default:
                return isDragging ? 'border-blue-400 bg-blue-50' : 'border-blue-300';
        }
    };
    const getIconColor = ()=>{
        switch(selectedLanguage){
            case 'Tamil':
                return 'text-purple-500';
            case 'Telugu':
                return 'text-green-500';
            case 'Kannada':
                return 'text-orange-500';
            default:
                return 'text-blue-500';
        }
    };
    const validateFile = (file)=>{
        // Check file size
        if (file.size > maxFileSize * 1024 * 1024) {
            return `File size exceeds ${maxFileSize}MB limit`;
        }
        // Check file type
        const fileName = file.name.toLowerCase();
        const acceptedExtensions = acceptedTypes.split(',').map((type)=>type.trim().toLowerCase());
        // More comprehensive file type validation
        const isValidType = acceptedExtensions.some((acceptedType)=>{
            if (acceptedType.startsWith('.')) {
                return fileName.endsWith(acceptedType);
            }
            return file.type === acceptedType;
        });
        if (!isValidType) {
            // Provide more specific error messages based on accepted types
            let typeDescription = '';
            if (acceptedTypes.includes('.pdf')) {
                typeDescription = 'PDF, DOC, DOCX, TXT, or RTF files';
            } else if (acceptedTypes.includes('.mp3')) {
                typeDescription = 'MP3, WAV, M4A, or FLAC audio files';
            } else {
                typeDescription = acceptedTypes;
            }
            return `File type not supported. Please upload ${typeDescription}`;
        }
        return null;
    };
    const handleFiles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((fileList)=>{
        const newFiles = [];
        const validFiles = [];
        Array.from(fileList).slice(0, maxFiles).forEach((file)=>{
            const error = validateFile(file);
            const fileWithStatus = {
                file,
                status: error ? 'error' : 'selected',
                error
            };
            newFiles.push(fileWithStatus);
            if (!error) {
                validFiles.push(file);
            }
        });
        setFiles(newFiles);
        if (validFiles.length > 0) {
            onFilesSelected(validFiles);
        }
    }, [
        maxFiles,
        onFilesSelected
    ]);
    const handleDragOver = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((e)=>{
        e.preventDefault();
        setIsDragging(true);
    }, []);
    const handleDragLeave = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((e)=>{
        e.preventDefault();
        setIsDragging(false);
    }, []);
    const handleDrop = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((e)=>{
        e.preventDefault();
        setIsDragging(false);
        if (e.dataTransfer.files) {
            handleFiles(e.dataTransfer.files);
        }
    }, [
        handleFiles
    ]);
    const handleFileInputChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((e)=>{
        if (e.target.files) {
            handleFiles(e.target.files);
        }
    }, [
        handleFiles
    ]);
    const handleRemoveFile = (index)=>{
        const newFiles = files.filter((_, i)=>i !== index);
        setFiles(newFiles);
        const validFiles = newFiles.filter((f)=>f.status !== 'error').map((f)=>f.file);
        onFilesSelected(validFiles);
    };
    const handleClick = ()=>{
        fileInputRef.current?.click();
    };
    const text = getLanguageText();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-4",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                onClick: handleClick,
                onDragOver: handleDragOver,
                onDragLeave: handleDragLeave,
                onDrop: handleDrop,
                className: `
          border-2 border-dashed rounded-xl p-8 text-center cursor-pointer transition-all duration-200
          ${getBorderColor()}
          hover:bg-gray-50 dark:hover:bg-gray-700/50
          min-h-[160px] flex items-center justify-center
        `,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                        ref: fileInputRef,
                        type: "file",
                        accept: acceptedTypes,
                        multiple: maxFiles > 1,
                        onChange: handleFileInputChange,
                        className: "hidden"
                    }, void 0, false, {
                        fileName: "[project]/components/chatComponents/FileDropZone.tsx",
                        lineNumber: 210,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex flex-col items-center justify-center gap-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "p-3 rounded-full bg-gray-100 dark:bg-gray-800",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiCloudArrowUp"], {
                                    className: `w-10 h-10 ${getIconColor()}`
                                }, void 0, false, {
                                    fileName: "[project]/components/chatComponents/FileDropZone.tsx",
                                    lineNumber: 221,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/components/chatComponents/FileDropZone.tsx",
                                lineNumber: 220,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "space-y-1",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-sm font-medium text-gray-700 dark:text-gray-300 leading-relaxed",
                                        children: text.dragText
                                    }, void 0, false, {
                                        fileName: "[project]/components/chatComponents/FileDropZone.tsx",
                                        lineNumber: 224,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-xs text-gray-500 dark:text-gray-400",
                                        children: text.maxSizeText
                                    }, void 0, false, {
                                        fileName: "[project]/components/chatComponents/FileDropZone.tsx",
                                        lineNumber: 227,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/chatComponents/FileDropZone.tsx",
                                lineNumber: 223,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/chatComponents/FileDropZone.tsx",
                        lineNumber: 219,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/chatComponents/FileDropZone.tsx",
                lineNumber: 198,
                columnNumber: 7
            }, this),
            files.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-3",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "border-t border-gray-200 dark:border-gray-700 pt-4",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                            className: "text-sm font-medium text-gray-700 dark:text-gray-300 mb-3",
                            children: [
                                text.selectedText,
                                " (",
                                files.length,
                                ")"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/chatComponents/FileDropZone.tsx",
                            lineNumber: 238,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "space-y-2",
                            children: files.map((fileWithStatus, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: `
                    flex items-center gap-4 p-4 rounded-lg border transition-all duration-200
                    ${fileWithStatus.status === 'error' ? 'border-red-200 bg-red-50 dark:bg-red-900/20 dark:border-red-800' : 'border-gray-200 bg-gray-50 dark:bg-gray-700/50 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700'}
                  `,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex-shrink-0 flex items-center justify-center w-10 h-10 rounded-lg bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600",
                                            children: fileWithStatus.status === 'error' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiX"], {
                                                className: "w-5 h-5 text-red-500"
                                            }, void 0, false, {
                                                fileName: "[project]/components/chatComponents/FileDropZone.tsx",
                                                lineNumber: 255,
                                                columnNumber: 23
                                            }, this) : fileWithStatus.status === 'success' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiCheck"], {
                                                className: "w-5 h-5 text-green-500"
                                            }, void 0, false, {
                                                fileName: "[project]/components/chatComponents/FileDropZone.tsx",
                                                lineNumber: 257,
                                                columnNumber: 23
                                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiFile"], {
                                                className: `w-5 h-5 ${getIconColor()}`
                                            }, void 0, false, {
                                                fileName: "[project]/components/chatComponents/FileDropZone.tsx",
                                                lineNumber: 259,
                                                columnNumber: 23
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/components/chatComponents/FileDropZone.tsx",
                                            lineNumber: 253,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex-grow min-w-0 space-y-1",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-sm font-medium text-gray-900 dark:text-gray-100 truncate",
                                                    children: fileWithStatus.file.name
                                                }, void 0, false, {
                                                    fileName: "[project]/components/chatComponents/FileDropZone.tsx",
                                                    lineNumber: 264,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex items-center gap-2",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-xs text-gray-500 dark:text-gray-400",
                                                            children: [
                                                                (fileWithStatus.file.size / 1024 / 1024).toFixed(2),
                                                                " MB"
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/components/chatComponents/FileDropZone.tsx",
                                                            lineNumber: 268,
                                                            columnNumber: 23
                                                        }, this),
                                                        fileWithStatus.status === 'selected' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400",
                                                            children: "Ready"
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/chatComponents/FileDropZone.tsx",
                                                            lineNumber: 272,
                                                            columnNumber: 25
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/components/chatComponents/FileDropZone.tsx",
                                                    lineNumber: 267,
                                                    columnNumber: 21
                                                }, this),
                                                fileWithStatus.error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-xs text-red-600 dark:text-red-400 mt-1 leading-relaxed",
                                                    children: fileWithStatus.error
                                                }, void 0, false, {
                                                    fileName: "[project]/components/chatComponents/FileDropZone.tsx",
                                                    lineNumber: 278,
                                                    columnNumber: 23
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/components/chatComponents/FileDropZone.tsx",
                                            lineNumber: 263,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: ()=>handleRemoveFile(index),
                                            className: "flex-shrink-0 p-2 rounded-lg text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-200",
                                            title: text.removeText,
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiX"], {
                                                className: "w-4 h-4"
                                            }, void 0, false, {
                                                fileName: "[project]/components/chatComponents/FileDropZone.tsx",
                                                lineNumber: 289,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/components/chatComponents/FileDropZone.tsx",
                                            lineNumber: 284,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, index, true, {
                                    fileName: "[project]/components/chatComponents/FileDropZone.tsx",
                                    lineNumber: 243,
                                    columnNumber: 17
                                }, this))
                        }, void 0, false, {
                            fileName: "[project]/components/chatComponents/FileDropZone.tsx",
                            lineNumber: 241,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/chatComponents/FileDropZone.tsx",
                    lineNumber: 237,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/chatComponents/FileDropZone.tsx",
                lineNumber: 236,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/chatComponents/FileDropZone.tsx",
        lineNumber: 196,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = FileDropZone;
}}),
"[project]/components/chatComponents/URLInput.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/react-icons/pi/index.mjs [app-ssr] (ecmascript)");
;
;
;
const URLInput = ({ type, value, onChange, onSubmit, selectedLanguage })=>{
    const [isValid, setIsValid] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    const getLanguageText = ()=>{
        switch(selectedLanguage){
            case 'Tamil':
                return {
                    youtubePlaceholder: 'YouTube வீடியோ URL ஐ உள்ளிடவும்...',
                    articlePlaceholder: 'கட்டுரை அல்லது வலைப்பக்க URL ஐ உள்ளிடவும்...',
                    addButton: 'சேர்',
                    invalidUrl: 'தவறான URL வடிவம்',
                    invalidYoutube: 'தவறான YouTube URL',
                    validUrl: 'சரியான URL'
                };
            case 'Telugu':
                return {
                    youtubePlaceholder: 'YouTube వీడియో URL ని ఎంటర్ చేయండి...',
                    articlePlaceholder: 'వ్యాసం లేదా వెబ్‌పేజీ URL ని ఎంటర్ చేయండి...',
                    addButton: 'జోడించు',
                    invalidUrl: 'చెల్లని URL ఫార్మాట్',
                    invalidYoutube: 'చెల్లని YouTube URL',
                    validUrl: 'చెల్లుబాటు అయ్యే URL'
                };
            case 'Kannada':
                return {
                    youtubePlaceholder: 'YouTube ವೀಡಿಯೊ URL ಅನ್ನು ನಮೂದಿಸಿ...',
                    articlePlaceholder: 'ಲೇಖನ ಅಥವಾ ವೆಬ್‌ಪುಟದ URL ಅನ್ನು ನಮೂದಿಸಿ...',
                    addButton: 'ಸೇರಿಸಿ',
                    invalidUrl: 'ಅಮಾನ್ಯ URL ಸ್ವರೂಪ',
                    invalidYoutube: 'ಅಮಾನ್ಯ YouTube URL',
                    validUrl: 'ಮಾನ್ಯ URL'
                };
            default:
                return {
                    youtubePlaceholder: 'Enter YouTube video URL...',
                    articlePlaceholder: 'Enter article or webpage URL...',
                    addButton: 'Add',
                    invalidUrl: 'Invalid URL format',
                    invalidYoutube: 'Invalid YouTube URL',
                    validUrl: 'Valid URL'
                };
        }
    };
    const validateURL = (url)=>{
        if (!url.trim()) {
            return {
                isValid: false,
                error: ''
            };
        }
        try {
            const urlObj = new URL(url);
            if (type === 'youtube') {
                const isYouTube = urlObj.hostname === 'www.youtube.com' || urlObj.hostname === 'youtube.com' || urlObj.hostname === 'youtu.be' || urlObj.hostname === 'm.youtube.com';
                if (!isYouTube) {
                    return {
                        isValid: false,
                        error: getLanguageText().invalidYoutube
                    };
                }
            }
            return {
                isValid: true,
                error: ''
            };
        } catch  {
            return {
                isValid: false,
                error: getLanguageText().invalidUrl
            };
        }
    };
    const handleInputChange = (e)=>{
        const newValue = e.target.value;
        onChange(newValue);
        const validation = validateURL(newValue);
        setIsValid(validation.isValid);
        setError(validation.error);
    };
    const handleSubmit = ()=>{
        if (isValid && value.trim()) {
            onSubmit(value.trim());
        }
    };
    const handleKeyPress = (e)=>{
        if (e.key === 'Enter' && isValid && value.trim()) {
            e.preventDefault();
            handleSubmit();
        }
    };
    const getBorderColor = ()=>{
        if (isValid === null) {
            switch(selectedLanguage){
                case 'Tamil':
                    return 'border-purple-300 focus:border-purple-500';
                case 'Telugu':
                    return 'border-green-300 focus:border-green-500';
                case 'Kannada':
                    return 'border-orange-300 focus:border-orange-500';
                default:
                    return 'border-blue-300 focus:border-blue-500';
            }
        }
        return isValid ? 'border-green-300 focus:border-green-500' : 'border-red-300 focus:border-red-500';
    };
    const getButtonColor = ()=>{
        switch(selectedLanguage){
            case 'Tamil':
                return 'bg-purple-500 hover:bg-purple-600 focus:ring-purple-500';
            case 'Telugu':
                return 'bg-green-500 hover:bg-green-600 focus:ring-green-500';
            case 'Kannada':
                return 'bg-orange-500 hover:bg-orange-600 focus:ring-orange-500';
            default:
                return 'bg-blue-500 hover:bg-blue-600 focus:ring-blue-500';
        }
    };
    const text = getLanguageText();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-2",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "relative",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                        type: "url",
                        value: value,
                        onChange: handleInputChange,
                        onKeyPress: handleKeyPress,
                        placeholder: type === 'youtube' ? text.youtubePlaceholder : text.articlePlaceholder,
                        className: `
            w-full px-3 py-2 pr-10 border rounded-lg
            focus:outline-none focus:ring-2 focus:ring-opacity-50
            ${getBorderColor()}
            dark:bg-gray-700 dark:text-white dark:placeholder-gray-400
          `
                    }, void 0, false, {
                        fileName: "[project]/components/chatComponents/URLInput.tsx",
                        lineNumber: 148,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute right-3 top-1/2 transform -translate-y-1/2",
                        children: [
                            isValid === true && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiCheck"], {
                                className: "w-5 h-5 text-green-500"
                            }, void 0, false, {
                                fileName: "[project]/components/chatComponents/URLInput.tsx",
                                lineNumber: 165,
                                columnNumber: 13
                            }, this),
                            isValid === false && value.trim() && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiX"], {
                                className: "w-5 h-5 text-red-500"
                            }, void 0, false, {
                                fileName: "[project]/components/chatComponents/URLInput.tsx",
                                lineNumber: 168,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/chatComponents/URLInput.tsx",
                        lineNumber: 163,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/chatComponents/URLInput.tsx",
                lineNumber: 147,
                columnNumber: 7
            }, this),
            error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "text-xs text-red-500 dark:text-red-400",
                children: error
            }, void 0, false, {
                fileName: "[project]/components/chatComponents/URLInput.tsx",
                lineNumber: 175,
                columnNumber: 9
            }, this),
            isValid && value.trim() && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "text-xs text-green-500 dark:text-green-400",
                children: text.validUrl
            }, void 0, false, {
                fileName: "[project]/components/chatComponents/URLInput.tsx",
                lineNumber: 182,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                type: "button",
                onClick: handleSubmit,
                disabled: !isValid || !value.trim(),
                className: `
          w-full flex items-center justify-center gap-2 px-4 py-2 rounded-lg
          text-white font-medium text-sm transition-all
          ${isValid && value.trim() ? getButtonColor() : 'bg-gray-400 cursor-not-allowed'}
          disabled:opacity-50
        `,
                children: [
                    text.addButton,
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiArrowRight"], {
                        className: "w-4 h-4"
                    }, void 0, false, {
                        fileName: "[project]/components/chatComponents/URLInput.tsx",
                        lineNumber: 203,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/chatComponents/URLInput.tsx",
                lineNumber: 188,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/chatComponents/URLInput.tsx",
        lineNumber: 146,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = URLInput;
}}),
"[project]/services/faissService.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * FAISS Service - Handles all FAISS-related API calls
 * Replaces Pinecone-specific functionality with FAISS backend integration
 */ __turbopack_esm__({
    "cancelUpload": (()=>cancelUpload),
    "checkFaissIndexExists": (()=>checkFaissIndexExists),
    "getCSVData": (()=>getCSVData),
    "getEmbeddingModels": (()=>getEmbeddingModels),
    "getFaissCategories": (()=>getFaissCategories),
    "getFaissConfig": (()=>getFaissConfig),
    "getUploadStatus": (()=>getUploadStatus),
    "listCSVFiles": (()=>listCSVFiles),
    "listExcelFiles": (()=>listExcelFiles),
    "queryFaiss": (()=>queryFaiss),
    "setFaissConfig": (()=>setFaissConfig),
    "uploadCSVToFaiss": (()=>uploadCSVToFaiss),
    "uploadExcelToFaiss": (()=>uploadExcelToFaiss),
    "uploadFileToFaiss": (()=>uploadFileToFaiss)
});
// Base URL for FAISS backend
const FAISS_BASE_URL = ("TURBOPACK compile-time truthy", 1) ? 'http://localhost:5010' : ("TURBOPACK unreachable", undefined);
const uploadCSVToFaiss = async (file, indexName, clientEmail, updateMode = 'update', embedModel = 'all-MiniLM-L6-v2', signal, onProgress)=>{
    return new Promise((resolve, reject)=>{
        try {
            // Validate file type
            if (file.type !== 'text/csv' && !file.name.toLowerCase().endsWith('.csv')) {
                reject(new Error('Only CSV files are supported for CSV upload'));
                return;
            }
            // Create FormData
            const formData = new FormData();
            formData.append('file', file);
            formData.append('index_name', indexName);
            formData.append('update_mode', updateMode);
            formData.append('embed_model', embedModel);
            if (clientEmail) {
                formData.append('client', clientEmail);
            }
            // Create XMLHttpRequest for progress tracking
            const xhr = new XMLHttpRequest();
            // Handle response
            xhr.onload = ()=>{
                if (xhr.status >= 200 && xhr.status < 300) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        resolve(response);
                    } catch (e) {
                        reject(new Error('Invalid JSON response from server'));
                    }
                } else {
                    try {
                        const errorResponse = JSON.parse(xhr.responseText);
                        reject(new Error(errorResponse.error || `HTTP ${xhr.status}: ${xhr.statusText}`));
                    } catch (e) {
                        reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
                    }
                }
            };
            xhr.onerror = ()=>{
                reject(new Error('Network error occurred during upload'));
            };
            xhr.onabort = ()=>{
                reject(new Error('Upload was cancelled'));
            };
            // Track upload progress
            if (onProgress) {
                xhr.upload.onprogress = (event)=>{
                    if (event.lengthComputable) {
                        const progress = Math.round(event.loaded / event.total * 100);
                        onProgress(progress);
                    }
                };
            }
            // Handle cancellation
            if (signal) {
                signal.addEventListener('abort', ()=>{
                    xhr.abort();
                });
            }
            // Send request
            xhr.open('POST', `${FAISS_BASE_URL}/api/upload-csv`, true);
            xhr.send(formData);
        } catch (error) {
            reject(error);
        }
    });
};
const uploadExcelToFaiss = async (file, indexName, clientId, updateMode = 'update', embedModel = 'all-MiniLM-L6-v2', signal, onProgress)=>{
    return new Promise((resolve, reject)=>{
        try {
            // Validate file type
            const fileName = file.name.toLowerCase();
            if (!fileName.endsWith('.xlsx') && !fileName.endsWith('.xls')) {
                reject(new Error('Only Excel files (.xlsx, .xls) are supported for Excel upload'));
                return;
            }
            // Create FormData
            const formData = new FormData();
            formData.append('file', file);
            formData.append('index_name', indexName);
            formData.append('client_id', clientId);
            formData.append('update_mode', updateMode);
            formData.append('embed_model', embedModel);
            // Create XMLHttpRequest for progress tracking
            const xhr = new XMLHttpRequest();
            // Handle response
            xhr.onload = ()=>{
                if (xhr.status >= 200 && xhr.status < 300) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        resolve(response);
                    } catch (e) {
                        reject(new Error('Invalid JSON response from server'));
                    }
                } else {
                    try {
                        const errorResponse = JSON.parse(xhr.responseText);
                        reject(new Error(errorResponse.error?.message || errorResponse.message || `HTTP ${xhr.status}: ${xhr.statusText}`));
                    } catch (e) {
                        reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
                    }
                }
            };
            xhr.onerror = ()=>{
                reject(new Error('Network error occurred during upload'));
            };
            xhr.onabort = ()=>{
                reject(new Error('Upload was cancelled'));
            };
            // Track upload progress
            if (onProgress) {
                xhr.upload.onprogress = (event)=>{
                    if (event.lengthComputable) {
                        const progress = Math.round(event.loaded / event.total * 100);
                        onProgress(progress);
                    }
                };
            }
            // Handle cancellation
            if (signal) {
                signal.addEventListener('abort', ()=>{
                    xhr.abort();
                });
            }
            // Send request
            xhr.open('POST', `${FAISS_BASE_URL}/api/upload-excel`, true);
            xhr.send(formData);
        } catch (error) {
            reject(error);
        }
    });
};
const uploadFileToFaiss = async (file, indexName, clientEmail, updateMode = 'update', embedModel = 'all-MiniLM-L6-v2', signal, onProgress)=>{
    const fileName = file.name.toLowerCase();
    const isExcel = fileName.endsWith('.xlsx') || fileName.endsWith('.xls');
    if (isExcel) {
        // For Excel files, client_id is required
        if (!clientEmail) {
            throw new Error('Client email is required for Excel file uploads');
        }
        return uploadExcelToFaiss(file, indexName, clientEmail, updateMode, embedModel, signal, onProgress);
    } else {
        // For CSV files, use the existing CSV upload function
        return uploadCSVToFaiss(file, indexName, clientEmail, updateMode, embedModel, signal, onProgress);
    }
};
const getEmbeddingModels = async ()=>{
    const response = await fetch(`${FAISS_BASE_URL}/api/list-embedding-models`);
    if (!response.ok) {
        throw new Error(`Failed to fetch embedding models: ${response.statusText}`);
    }
    return response.json();
};
const getFaissCategories = async (clientEmail)=>{
    const response = await fetch(`${FAISS_BASE_URL}/api/list-categories`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(clientEmail ? {
            client_email: clientEmail
        } : {})
    });
    if (!response.ok) {
        throw new Error(`Failed to fetch FAISS categories: ${response.statusText}`);
    }
    const data = await response.json();
    return data.categories || [];
};
const checkFaissIndexExists = async (indexName, embedModel)=>{
    const response = await fetch(`${FAISS_BASE_URL}/api/check-index`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            index_name: indexName,
            embed_model: embedModel
        })
    });
    if (!response.ok) {
        throw new Error(`Failed to check FAISS index: ${response.statusText}`);
    }
    const data = await response.json();
    return data.exists || false;
};
/**
 * Get current user's email from session storage
 */ const getCurrentUserEmail = ()=>{
    try {
        if ("TURBOPACK compile-time truthy", 1) return null;
        "TURBOPACK unreachable";
        // Try multiple sources for user email
        const directEmail = undefined;
        // Try from user session data
        const userSession = undefined;
    } catch (error) {
        console.error('Error getting current user email:', error);
        return null;
    }
};
const queryFaiss = async (query, indexName, k = 5, userEmail)=>{
    // Get user email if not provided
    const emailToUse = userEmail || getCurrentUserEmail();
    const requestBody = {
        query,
        index_name: indexName,
        k
    };
    // Add user email for access validation if available
    if (emailToUse) {
        requestBody.user_email = emailToUse;
    }
    const response = await fetch(`${FAISS_BASE_URL}/api/query-faiss`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
    });
    if (!response.ok) {
        throw new Error(`Failed to query FAISS index: ${response.statusText}`);
    }
    const data = await response.json();
    return data.results || [];
};
const getUploadStatus = async (uploadId)=>{
    const response = await fetch(`${FAISS_BASE_URL}/api/upload-status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            upload_id: uploadId
        })
    });
    if (!response.ok) {
        throw new Error(`Failed to get upload status: ${response.statusText}`);
    }
    return response.json();
};
const cancelUpload = async (uploadId)=>{
    const response = await fetch(`${FAISS_BASE_URL}/api/cancel-upload`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            upload_id: uploadId
        })
    });
    if (!response.ok) {
        throw new Error(`Failed to cancel upload: ${response.statusText}`);
    }
};
const getCSVData = async (indexName, limit = 100, offset = 0)=>{
    const response = await fetch(`${FAISS_BASE_URL}/api/get-csv-data`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            index_name: indexName,
            limit,
            offset
        })
    });
    if (!response.ok) {
        throw new Error(`Failed to get CSV data: ${response.statusText}`);
    }
    return response.json();
};
const listCSVFiles = async (clientEmail)=>{
    const response = await fetch(`${FAISS_BASE_URL}/api/list-csv-files`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(clientEmail ? {
            client_email: clientEmail
        } : {})
    });
    if (!response.ok) {
        throw new Error(`Failed to list CSV files: ${response.statusText}`);
    }
    const data = await response.json();
    return data.files || [];
};
const listExcelFiles = async (clientId)=>{
    const response = await fetch(`${FAISS_BASE_URL}/api/list-excel-files`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(clientId ? {
            client_id: clientId
        } : {})
    });
    if (!response.ok) {
        throw new Error(`Failed to list Excel files: ${response.statusText}`);
    }
    const data = await response.json();
    return data.excel_files || [];
};
const getFaissConfig = ()=>{
    if ("TURBOPACK compile-time truthy", 1) return null;
    "TURBOPACK unreachable";
};
const setFaissConfig = (config)=>{
    if ("TURBOPACK compile-time truthy", 1) return;
    "TURBOPACK unreachable";
};
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("events", () => require("events"));

module.exports = mod;
}}),
"[project]/services/uploadService.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/**
 * Upload Service for ChatInputUpload component
 * Handles file and URL uploads to the Python FAISS backend
 * Enhanced with cache memory functionality for faster responses
 */ __turbopack_esm__({
    "CacheUtils": (()=>CacheUtils),
    "checkBackendHealth": (()=>checkBackendHealth),
    "handleUpload": (()=>handleUpload),
    "processArticleURL": (()=>processArticleURL),
    "processAudio": (()=>processAudio),
    "processDocument": (()=>processDocument),
    "processPDF": (()=>processPDF),
    "processPDFDocument": (()=>processPDFDocument),
    "processYouTubeURL": (()=>processYouTubeURL),
    "searchContent": (()=>searchContent),
    "testConnection": (()=>testConnection)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$faissService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/services/faissService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/axios/lib/axios.js [app-ssr] (ecmascript)");
;
;
// Cache configuration
const CACHE_CONFIG = {
    UPLOAD_CACHE_KEY: 'upload_cache',
    QUERY_CACHE_KEY: 'query_cache',
    CACHE_EXPIRY_MS: 24 * 60 * 60 * 1000,
    MAX_CACHE_SIZE: 100,
    ENABLE_CACHE: true
};
// Cache utility class
class CacheManager {
    static instance;
    constructor(){}
    static getInstance() {
        if (!CacheManager.instance) {
            CacheManager.instance = new CacheManager();
        }
        return CacheManager.instance;
    }
    // Generate cache key for uploads
    generateUploadCacheKey(type, data, options = {}) {
        if (data instanceof File) {
            return `upload_${type}_${data.name}_${data.size}_${data.lastModified}_${options.index_name || 'default'}`;
        } else {
            return `upload_${type}_${this.hashString(data)}_${options.index_name || 'default'}`;
        }
    }
    // Generate cache key for queries
    generateQueryCacheKey(query, indexName, options = {}) {
        return `query_${this.hashString(query)}_${indexName}_${JSON.stringify(options)}`;
    }
    // Simple string hash function
    hashString(str) {
        let hash = 0;
        for(let i = 0; i < str.length; i++){
            const char = str.charCodeAt(i);
            hash = (hash << 5) - hash + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return Math.abs(hash).toString(36);
    }
    // Get cached item
    get(cacheKey, key) {
        if ("TURBOPACK compile-time truthy", 1) return null;
        "TURBOPACK unreachable";
    }
    // Set cached item
    set(cacheKey, key, data, customExpiryMs) {
        if ("TURBOPACK compile-time truthy", 1) return;
        "TURBOPACK unreachable";
    }
    // Remove cached item
    remove(cacheKey, key) {
        if ("TURBOPACK compile-time truthy", 1) return;
        "TURBOPACK unreachable";
    }
    // Clean expired items and enforce size limit
    cleanCache(cache) {
        const now = Date.now();
        const keys = Object.keys(cache);
        // Remove expired items
        keys.forEach((key)=>{
            if (cache[key].expiresAt < now) {
                delete cache[key];
            }
        });
        // Enforce size limit by removing oldest items
        const remainingKeys = Object.keys(cache);
        if (remainingKeys.length > CACHE_CONFIG.MAX_CACHE_SIZE) {
            const sortedKeys = remainingKeys.sort((a, b)=>cache[a].timestamp - cache[b].timestamp);
            const keysToRemove = sortedKeys.slice(0, remainingKeys.length - CACHE_CONFIG.MAX_CACHE_SIZE);
            keysToRemove.forEach((key)=>delete cache[key]);
        }
    }
    // Clear all cache
    clearCache(cacheKey) {
        if ("TURBOPACK compile-time truthy", 1) return;
        "TURBOPACK unreachable";
    }
    // Get cache statistics
    getCacheStats(cacheKey) {
        if ("TURBOPACK compile-time truthy", 1) return {
            size: 0,
            oldestItem: 0,
            newestItem: 0
        };
        "TURBOPACK unreachable";
    }
}
// Configure axios defaults for longer timeouts
const api = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].create({
    timeout: 120000,
    headers: {
        'Content-Type': 'application/json'
    },
    withCredentials: false
});
// Initialize cache manager
const cacheManager = CacheManager.getInstance();
// Add request interceptor for debugging
api.interceptors.request.use((config)=>{
    console.log('🚀 Making request to:', config.url);
    console.log('📤 Request config:', {
        method: config.method,
        url: config.url,
        headers: config.headers,
        timeout: config.timeout
    });
    return config;
}, (error)=>{
    console.error('❌ Request interceptor error:', error);
    return Promise.reject(error);
});
// Add response interceptor for debugging
api.interceptors.response.use((response)=>{
    console.log('✅ Response received:', {
        status: response.status,
        statusText: response.statusText,
        url: response.config.url
    });
    return response;
}, (error)=>{
    console.error('❌ Response interceptor error:', {
        message: error.message,
        code: error.code,
        status: error.response?.status,
        statusText: error.response?.statusText,
        url: error.config?.url,
        data: error.response?.data
    });
    return Promise.reject(error);
});
// Backend configuration - Use the same port as FAISS service for consistency
const BACKEND_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:5010';
// API endpoints
const ENDPOINTS = {
    PROCESS_YOUTUBE: '/api/process_youtube',
    PROCESS_ARTICLE: '/api/process_article',
    PROCESS_DOCUMENT: '/api/process_document',
    PROCESS_PDF: '/api/process_pdf',
    PROCESS_AUDIO: '/api/process_audio',
    SEARCH: '/api/search',
    HEALTH: '/api/health'
};
async function processYouTubeURL(url, options = {}) {
    try {
        console.log('🎥 Starting YouTube URL processing:', url);
        // Get selected index from localStorage if not provided in options
        const selectedIndex = options.index_name || (("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : null) || 'default';
        console.log(`📌 Using index for YouTube processing: ${selectedIndex}`);
        // Check cache first
        const cacheKey = cacheManager.generateUploadCacheKey('youtube', url, {
            index_name: selectedIndex
        });
        const cachedResult = cacheManager.get(CACHE_CONFIG.UPLOAD_CACHE_KEY, cacheKey);
        if (cachedResult) {
            console.log('💾 Using cached YouTube processing result');
            return {
                success: cachedResult.success,
                message: cachedResult.message + ' (from cache)',
                error: cachedResult.error,
                upload_id: cachedResult.upload_id,
                index_name: cachedResult.index_name,
                filename: cachedResult.filename
            };
        }
        const requestData = {
            url,
            index_name: selectedIndex,
            client_email: options.client_email || ''
        };
        console.log('📤 Sending request to:', `${BACKEND_BASE_URL}${ENDPOINTS.PROCESS_YOUTUBE}`);
        console.log('📤 Request data:', requestData);
        const startTime = Date.now();
        const response = await api.post(`${BACKEND_BASE_URL}${ENDPOINTS.PROCESS_YOUTUBE}`, requestData);
        const processingTime = Date.now() - startTime;
        console.log('✅ YouTube processing response:', response.data);
        // Cache successful results
        if (response.data.success) {
            const cacheData = {
                success: response.data.success,
                message: response.data.message,
                upload_id: response.data.upload_id,
                index_name: response.data.index_name,
                filename: response.data.filename,
                processingTime
            };
            // Cache for 24 hours for successful URL processing
            cacheManager.set(CACHE_CONFIG.UPLOAD_CACHE_KEY, cacheKey, cacheData);
            console.log('💾 Cached YouTube processing result');
        }
        return response.data;
    } catch (error) {
        console.error('❌ Error processing YouTube URL:', error);
        // Log detailed error information first
        const errorDetails = {
            message: error.message,
            code: error.code,
            status: error.response?.status,
            statusText: error.response?.statusText,
            data: error.response?.data,
            url: error.config?.url,
            method: error.config?.method
        };
        console.error('🔍 Detailed error information:', errorDetails);
        // Handle network errors
        if (error.code === 'ERR_NETWORK') {
            console.error('🌐 Network error detected - backend may be unreachable');
            return {
                success: false,
                error: `Network error - Cannot reach backend at ${BACKEND_BASE_URL}. Please check if the server is running on port 5010.`
            };
        }
        // Handle timeout specifically
        if (error.code === 'ECONNABORTED') {
            console.error('⏰ Request timeout detected');
            return {
                success: false,
                error: 'Processing timeout - the video is still being processed in the background'
            };
        }
        // Handle CORS errors
        if (error.message?.includes('CORS') || error.code === 'ERR_BLOCKED_BY_CLIENT') {
            console.error('🚫 CORS error detected');
            return {
                success: false,
                error: 'CORS error - please check backend CORS configuration'
            };
        }
        // Handle connection refused
        if (error.code === 'ECONNREFUSED') {
            console.error('🔌 Connection refused - backend server not running');
            return {
                success: false,
                error: `Connection refused - Backend server is not running on ${BACKEND_BASE_URL}`
            };
        }
        // Handle other specific error codes
        if (error.response?.status === 404) {
            console.error('🔍 Endpoint not found');
            return {
                success: false,
                error: `Endpoint not found: ${ENDPOINTS.PROCESS_YOUTUBE}`
            };
        }
        if (error.response?.status === 500) {
            console.error('💥 Server error');
            return {
                success: false,
                error: `Server error: ${error.response?.data?.error || 'Internal server error'}`
            };
        }
        return {
            success: false,
            error: error.response?.data?.error || error.message || 'Failed to process YouTube URL'
        };
    }
}
async function processArticleURL(url, options = {}) {
    try {
        console.log('📰 Starting Article URL processing:', url);
        // Get selected index from localStorage if not provided in options
        const selectedIndex = options.index_name || (("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : null) || 'default';
        console.log(`📌 Using index for Article processing: ${selectedIndex}`);
        // Check cache first
        const cacheKey = cacheManager.generateUploadCacheKey('article', url, {
            index_name: selectedIndex
        });
        const cachedResult = cacheManager.get(CACHE_CONFIG.UPLOAD_CACHE_KEY, cacheKey);
        if (cachedResult) {
            console.log('💾 Using cached Article processing result');
            return {
                success: cachedResult.success,
                message: cachedResult.message + ' (from cache)',
                error: cachedResult.error,
                upload_id: cachedResult.upload_id,
                index_name: cachedResult.index_name,
                filename: cachedResult.filename
            };
        }
        const startTime = Date.now();
        const response = await api.post(`${BACKEND_BASE_URL}${ENDPOINTS.PROCESS_ARTICLE}`, {
            url,
            index_name: selectedIndex,
            client_email: options.client_email || ''
        });
        const processingTime = Date.now() - startTime;
        console.log('✅ Article processing response:', response.data);
        // Cache successful results
        if (response.data.success) {
            const cacheData = {
                success: response.data.success,
                message: response.data.message,
                upload_id: response.data.upload_id,
                index_name: response.data.index_name,
                filename: response.data.filename,
                processingTime
            };
            // Cache for 24 hours for successful URL processing
            cacheManager.set(CACHE_CONFIG.UPLOAD_CACHE_KEY, cacheKey, cacheData);
            console.log('💾 Cached Article processing result');
        }
        return response.data;
    } catch (error) {
        console.error('❌ Error processing Article URL:', error);
        // Handle timeout specifically
        if (error.code === 'ECONNABORTED') {
            return {
                success: false,
                error: 'Processing timeout - the article is still being processed in the background'
            };
        }
        return {
            success: false,
            error: error.response?.data?.error || error.message || 'Failed to process Article URL'
        };
    }
}
async function processDocument(file, options = {}) {
    try {
        console.log('📄 Starting Document processing:', file.name);
        // Get selected index from localStorage if not provided in options
        const selectedIndex = options.index_name || (("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : null) || 'default';
        console.log(`📌 Using index for Document processing: ${selectedIndex}`);
        const formData = new FormData();
        formData.append('file', file);
        formData.append('index_name', selectedIndex);
        formData.append('client_email', options.client_email || '');
        const response = await api.post(`${BACKEND_BASE_URL}${ENDPOINTS.PROCESS_DOCUMENT}`, formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            },
            timeout: 180000
        });
        console.log('✅ Document processing response:', response.data);
        return response.data;
    } catch (error) {
        console.error('❌ Error processing Document:', error);
        // Handle timeout specifically
        if (error.code === 'ECONNABORTED') {
            return {
                success: false,
                error: 'Processing timeout - the document is still being processed in the background'
            };
        }
        return {
            success: false,
            error: error.response?.data?.error || error.message || 'Failed to process Document'
        };
    }
}
async function processPDF(file, options = {}) {
    try {
        console.log('📄 Starting PDF processing:', file.name);
        // Get selected index from localStorage if not provided in options
        const selectedIndex = options.index_name || (("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : null) || 'default';
        console.log(`📌 Using index for PDF processing: ${selectedIndex}`);
        const formData = new FormData();
        formData.append('file', file);
        formData.append('index_name', selectedIndex);
        formData.append('client_email', options.client_email || '');
        const response = await api.post(`${BACKEND_BASE_URL}${ENDPOINTS.PROCESS_PDF}`, formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            },
            timeout: 180000
        });
        console.log('✅ PDF processing response:', response.data);
        return response.data;
    } catch (error) {
        console.error('❌ Error processing PDF:', error);
        // Handle timeout specifically
        if (error.code === 'ECONNABORTED') {
            return {
                success: false,
                error: 'Processing timeout - the PDF is still being processed in the background'
            };
        }
        return {
            success: false,
            error: error.response?.data?.error || error.message || 'Failed to process PDF'
        };
    }
}
async function processAudio(file, options = {}) {
    try {
        console.log('🎵 Starting Audio processing:', file.name);
        // Get selected index from localStorage if not provided in options
        const selectedIndex = options.index_name || (("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : null) || 'default';
        console.log(`📌 Using index for Audio processing: ${selectedIndex}`);
        const formData = new FormData();
        formData.append('file', file);
        formData.append('index_name', selectedIndex);
        formData.append('client_email', options.client_email || '');
        const response = await api.post(`${BACKEND_BASE_URL}${ENDPOINTS.PROCESS_AUDIO}`, formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            },
            timeout: 300000
        });
        console.log('✅ Audio processing response:', response.data);
        return response.data;
    } catch (error) {
        console.error('❌ Error processing Audio:', error);
        // Handle timeout specifically
        if (error.code === 'ECONNABORTED') {
            return {
                success: false,
                error: 'Processing timeout - the audio is still being processed in the background'
            };
        }
        return {
            success: false,
            error: error.response?.data?.error || error.message || 'Failed to process Audio'
        };
    }
}
async function searchContent(query, options = {}) {
    try {
        console.log('🔍 Starting content search:', query);
        // Check cache first for search results
        const cacheKey = cacheManager.generateQueryCacheKey(query, options.index_name || 'default', options);
        const cachedResult = cacheManager.get(CACHE_CONFIG.QUERY_CACHE_KEY, cacheKey);
        // Track cache statistics
        cacheHitStats.totalRequests++;
        if (cachedResult) {
            console.log('💾 Using cached search result');
            cacheHitStats.cacheHits++;
            return {
                ...cachedResult,
                cached: true
            };
        }
        cacheHitStats.cacheMisses++;
        const startTime = Date.now();
        const response = await api.post(`${BACKEND_BASE_URL}${ENDPOINTS.SEARCH}`, {
            query,
            k: options.k || 5,
            index_name: options.index_name,
            use_deepseek: options.use_deepseek || false
        });
        const searchTime = Date.now() - startTime;
        console.log('✅ Search response:', response.data);
        const searchResult = {
            success: true,
            searchTime,
            ...response.data
        };
        // Cache successful search results for 1 hour
        if (searchResult.success && searchResult.results && searchResult.results.length > 0) {
            cacheManager.set(CACHE_CONFIG.QUERY_CACHE_KEY, cacheKey, searchResult, 60 * 60 * 1000); // 1 hour
            console.log('💾 Cached search result');
        }
        return searchResult;
    } catch (error) {
        console.error('❌ Error searching content:', error);
        return {
            success: false,
            results: [],
            query,
            error: error.response?.data?.error || error.message || 'Failed to search content'
        };
    }
}
async function testConnection() {
    try {
        console.log('🔌 Testing connection to backend...');
        console.log('🎯 Backend URL:', BACKEND_BASE_URL);
        // Try a simple GET request first
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(`${BACKEND_BASE_URL}/api/list-faiss-indexes`, {
            timeout: 10000,
            headers: {
                'Content-Type': 'application/json'
            },
            withCredentials: false
        });
        console.log('✅ Connection test successful:', response.data);
        return {
            success: true,
            message: 'Backend connection successful'
        };
    } catch (error) {
        console.error('❌ Connection test failed:', error);
        // Provide detailed error information
        const errorDetails = {
            message: error.message,
            code: error.code,
            status: error.response?.status,
            statusText: error.response?.statusText,
            url: error.config?.url
        };
        console.error('🔍 Error details:', errorDetails);
        return {
            success: false,
            error: `Connection failed: ${error.message} (${error.code || 'Unknown error'})`
        };
    }
}
async function checkBackendHealth() {
    try {
        console.log('🏥 Checking backend health...');
        const response = await api.get(`${BACKEND_BASE_URL}${ENDPOINTS.HEALTH}`, {
            timeout: 10000 // 10 seconds for health check
        });
        console.log('✅ Backend health check successful:', response.data);
        return response.data;
    } catch (error) {
        console.error('❌ Backend health check failed:', error);
        return {
            success: false,
            error: error.message || 'Backend is not available'
        };
    }
}
/**
 * Get upload options with FAISS configuration
 */ function getUploadOptionsWithConfig(options = {}) {
    const faissConfig = (0, __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$faissService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getFaissConfig"])();
    return {
        index_name: options.index_name || faissConfig?.indexName || 'default',
        client_email: options.client_email || faissConfig?.clientEmail || ''
    };
}
async function processPDFDocument(file, options = {}) {
    try {
        console.log('📄 Starting PDF/Document processing:', file.name);
        // Validate file type
        const fileName = file.name.toLowerCase();
        const isPDF = fileName.endsWith('.pdf');
        const isDocument = fileName.endsWith('.doc') || fileName.endsWith('.docx') || fileName.endsWith('.txt') || fileName.endsWith('.rtf');
        if (!isPDF && !isDocument) {
            return {
                success: false,
                error: 'Unsupported file type. Please upload PDF, DOC, DOCX, TXT, or RTF files.'
            };
        }
        // Get selected index from localStorage if not provided in options
        const selectedIndex = options.index_name || (("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : null) || 'default';
        console.log(`📌 Using index for ${isPDF ? 'PDF' : 'Document'} processing: ${selectedIndex}`);
        const formData = new FormData();
        formData.append('file', file);
        formData.append('index_name', selectedIndex);
        formData.append('client_email', options.client_email || '');
        // Choose the appropriate endpoint based on file type
        const endpoint = isPDF ? ENDPOINTS.PROCESS_PDF : ENDPOINTS.PROCESS_DOCUMENT;
        console.log(`📤 Sending ${isPDF ? 'PDF' : 'Document'} to endpoint:`, `${BACKEND_BASE_URL}${endpoint}`);
        const response = await api.post(`${BACKEND_BASE_URL}${endpoint}`, formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            },
            timeout: 180000
        });
        console.log(`✅ ${isPDF ? 'PDF' : 'Document'} processing response:`, response.data);
        return response.data;
    } catch (error) {
        console.error('❌ Error processing PDF/Document:', error);
        // Handle timeout specifically
        if (error.code === 'ECONNABORTED') {
            return {
                success: false,
                error: 'Processing timeout - the file is still being processed in the background'
            };
        }
        // Handle network errors
        if (error.code === 'ERR_NETWORK') {
            return {
                success: false,
                error: `Network error - Cannot reach backend at ${BACKEND_BASE_URL}. Please check if the server is running.`
            };
        }
        // Handle file size errors
        if (error.response?.status === 413) {
            return {
                success: false,
                error: 'File too large. Please upload a file smaller than 50MB.'
            };
        }
        // Handle unsupported file type errors
        if (error.response?.status === 400 && error.response?.data?.error?.includes('Unsupported file type')) {
            return {
                success: false,
                error: 'Unsupported file type. Please upload PDF, DOC, DOCX, TXT, or RTF files.'
            };
        }
        return {
            success: false,
            error: error.response?.data?.error || error.message || 'Failed to process file'
        };
    }
}
async function handleUpload(type, data, options = {}) {
    // Merge with FAISS configuration
    const uploadOptions = getUploadOptionsWithConfig(options);
    switch(type){
        case 'youtube':
            return processYouTubeURL(data, uploadOptions);
        case 'article':
            return processArticleURL(data, uploadOptions);
        case 'pdf':
            // Use the unified PDF/Document processor
            return processPDFDocument(data, uploadOptions);
        case 'mp3':
            return processAudio(data, uploadOptions);
        default:
            return {
                success: false,
                error: `Unsupported upload type: ${type}`
            };
    }
}
// Cache hit rate tracking
let cacheHitStats = {
    totalRequests: 0,
    cacheHits: 0,
    cacheMisses: 0
};
const CacheUtils = {
    /**
   * Clear all upload cache
   */ clearUploadCache: ()=>{
        cacheManager.clearCache(CACHE_CONFIG.UPLOAD_CACHE_KEY);
    },
    /**
   * Clear all query cache
   */ clearQueryCache: ()=>{
        cacheManager.clearCache(CACHE_CONFIG.QUERY_CACHE_KEY);
    },
    /**
   * Clear all caches
   */ clearAllCache: ()=>{
        cacheManager.clearCache(CACHE_CONFIG.UPLOAD_CACHE_KEY);
        cacheManager.clearCache(CACHE_CONFIG.QUERY_CACHE_KEY);
    },
    /**
   * Get cache statistics
   */ getCacheStats: ()=>{
        return {
            upload: cacheManager.getCacheStats(CACHE_CONFIG.UPLOAD_CACHE_KEY),
            query: cacheManager.getCacheStats(CACHE_CONFIG.QUERY_CACHE_KEY)
        };
    },
    /**
   * Get cache hit rate statistics
   */ getCacheHitRate: ()=>{
        const hitRate = cacheHitStats.totalRequests > 0 ? (cacheHitStats.cacheHits / cacheHitStats.totalRequests * 100).toFixed(2) : '0.00';
        return {
            ...cacheHitStats,
            hitRate: `${hitRate}%`
        };
    },
    /**
   * Reset cache hit rate statistics
   */ resetCacheHitStats: ()=>{
        cacheHitStats = {
            totalRequests: 0,
            cacheHits: 0,
            cacheMisses: 0
        };
    },
    /**
   * Check if caching is enabled
   */ isCacheEnabled: ()=>CACHE_CONFIG.ENABLE_CACHE,
    /**
   * Toggle cache functionality
   */ toggleCache: (enabled)=>{
        CACHE_CONFIG.ENABLE_CACHE = enabled;
        console.log(`💾 Cache ${enabled ? 'enabled' : 'disabled'}`);
    }
};
}}),
"[project]/components/chatComponents/ChatInputUpload.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$chatComponents$2f$UploadDropdown$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/components/chatComponents/UploadDropdown.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$chatComponents$2f$FileDropZone$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/components/chatComponents/FileDropZone.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$chatComponents$2f$URLInput$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/components/chatComponents/URLInput.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$uploadService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/services/uploadService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/react-hot-toast/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/react-icons/pi/index.mjs [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
const ChatInputUpload = ({ selectedLanguage, onFileUpload, onURLSubmit, onUploadStateChange, onNetworkError, disabled = false })=>{
    const [showDropdown, setShowDropdown] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [uploadState, setUploadState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        type: null,
        files: [],
        url: '',
        isActive: false,
        isProcessing: false,
        uploadResult: null
    });
    const dropdownRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const iconRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const autoCloseTimeoutRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Close dropdown when clicking outside or pressing Escape
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        function handleClickOutside(event) {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target) && iconRef.current && !iconRef.current.contains(event.target)) {
                setShowDropdown(false);
            }
        }
        function handleKeyDown(event) {
            if (event.key === 'Escape' && showDropdown) {
                setShowDropdown(false);
                // Return focus to the pin button
                iconRef.current?.focus();
            }
        }
        document.addEventListener('mousedown', handleClickOutside);
        document.addEventListener('keydown', handleKeyDown);
        return ()=>{
            document.removeEventListener('mousedown', handleClickOutside);
            document.removeEventListener('keydown', handleKeyDown);
        };
    }, [
        showDropdown
    ]);
    // Cleanup timeout on component unmount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        return ()=>{
            if (autoCloseTimeoutRef.current) {
                clearTimeout(autoCloseTimeoutRef.current);
            }
        };
    }, []);
    // Notify parent about upload state changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (onUploadStateChange) {
            onUploadStateChange(uploadState.isActive, showDropdown);
        }
    }, [
        uploadState.isActive,
        showDropdown,
        onUploadStateChange
    ]);
    const handleUploadTypeSelect = (type)=>{
        // If switching to a different type, clear previous content
        const isSwitchingType = uploadState.isActive && uploadState.type !== type;
        setUploadState({
            type,
            files: isSwitchingType ? [] : uploadState.files,
            url: isSwitchingType ? '' : uploadState.url,
            isActive: true,
            isProcessing: false,
            uploadResult: null
        });
        setShowDropdown(false);
        // If switching types, clear previous uploads
        if (isSwitchingType) {
            if (onFileUpload) {
                onFileUpload([]);
            }
        }
    };
    const handleAddMoreContent = ()=>{
        // Allow adding more content of the same type or switching types
        setShowDropdown(!showDropdown);
    };
    const getUploadStatusText = ()=>{
        if (!uploadState.isActive) return '';
        // Show processing status
        if (uploadState.isProcessing) {
            return 'Processing...';
        }
        // Show upload result status
        if (uploadState.uploadResult) {
            if (uploadState.uploadResult.success) {
                return '✅ Processed successfully';
            } else {
                return '❌ Processing failed';
            }
        }
        const fileCount = uploadState.files.length;
        const hasUrl = uploadState.url.trim().length > 0;
        if (uploadState.type === 'pdf' || uploadState.type === 'mp3') {
            return fileCount > 0 ? `${fileCount} file${fileCount > 1 ? 's' : ''} selected` : 'No files selected';
        } else {
            return hasUrl ? 'URL added' : 'No URL added';
        }
    };
    const handleFileUpload = async (files)=>{
        if (!files.length || !uploadState.type || uploadState.type !== 'pdf' && uploadState.type !== 'mp3') {
            return;
        }
        const file = files[0];
        console.log('📁 Files selected for upload:', files);
        // Validate file type and size before processing
        const fileName = file.name.toLowerCase();
        const fileSize = file.size;
        const maxSize = 50 * 1024 * 1024; // 50MB
        // Check file size
        if (fileSize > maxSize) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].error('File too large. Please upload a file smaller than 50MB.');
            return;
        }
        // Validate file type based on upload type
        if (uploadState.type === 'pdf') {
            const isPDF = fileName.endsWith('.pdf');
            const isDocument = fileName.endsWith('.doc') || fileName.endsWith('.docx') || fileName.endsWith('.txt') || fileName.endsWith('.rtf');
            if (!isPDF && !isDocument) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].error('Please upload PDF, DOC, DOCX, TXT, or RTF files only.');
                return;
            }
        } else if (uploadState.type === 'mp3') {
            const isAudio = fileName.endsWith('.mp3') || fileName.endsWith('.wav') || fileName.endsWith('.m4a') || fileName.endsWith('.flac');
            if (!isAudio) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].error('Please upload MP3, WAV, M4A, or FLAC audio files only.');
                return;
            }
        }
        // Update state with files and processing status
        setUploadState((prev)=>({
                ...prev,
                files,
                isProcessing: true,
                uploadResult: null
            }));
        try {
            console.log(`🚀 Starting ${uploadState.type} upload for file:`, file.name);
            const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$uploadService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["handleUpload"])(uploadState.type, file);
            console.log('📥 Upload result:', result);
            // Update state with result
            setUploadState((prev)=>({
                    ...prev,
                    isProcessing: false,
                    uploadResult: result
                }));
            // Show success/error toast with more specific messages
            if (result.success) {
                const fileType = uploadState.type === 'pdf' ? 'PDF/Document' : 'Audio';
                const cacheMessage = result.message?.includes('cache') ? ' (from cache)' : '';
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].success(`${fileType} file processed successfully${cacheMessage}! Content has been indexed.`);
                // Call the original callback if provided
                if (onFileUpload) {
                    onFileUpload(files);
                }
                // Auto-close popup after successful upload with a slight delay for user feedback
                autoCloseTimeoutRef.current = setTimeout(()=>{
                    handleClearUpload();
                }, 2000); // 2 second delay to show success state
            } else {
                const errorMessage = result.error || 'Failed to process file';
                console.error('❌ Upload failed:', errorMessage);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].error(errorMessage);
                // Check if it's a network error and trigger connection test
                if (errorMessage.includes('Network error') || errorMessage.includes('Connection refused')) {
                    if (onNetworkError) {
                        onNetworkError();
                    }
                }
            }
        } catch (error) {
            console.error('❌ Upload error:', error);
            setUploadState((prev)=>({
                    ...prev,
                    isProcessing: false,
                    uploadResult: {
                        success: false,
                        error: 'Upload failed due to network error'
                    }
                }));
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].error('Upload failed due to network error. Please check your connection.');
            // Call network error callback if provided
            if (onNetworkError) {
                onNetworkError();
            }
        }
    };
    const handleURLChange = (url)=>{
        setUploadState((prev)=>({
                ...prev,
                url
            }));
    };
    const handleURLSubmit = async (url)=>{
        if (!uploadState.type || uploadState.type !== 'youtube' && uploadState.type !== 'article') {
            return;
        }
        // Set processing state
        setUploadState((prev)=>({
                ...prev,
                isProcessing: true,
                uploadResult: null
            }));
        try {
            // Process the URL using the upload service
            const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$uploadService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["handleUpload"])(uploadState.type, url);
            // Update state with result
            setUploadState((prev)=>({
                    ...prev,
                    isProcessing: false,
                    uploadResult: result
                }));
            // Show success/error toast
            if (result.success) {
                const cacheMessage = result.message?.includes('cache') ? ' (from cache)' : '';
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].success(`${uploadState.type === 'youtube' ? 'YouTube video' : 'Article'} processed successfully${cacheMessage}!`);
                // Call the original callback if provided
                if (onURLSubmit) {
                    onURLSubmit(url, uploadState.type);
                }
                // Auto-close popup after successful upload with a slight delay for user feedback
                autoCloseTimeoutRef.current = setTimeout(()=>{
                    handleClearUpload();
                }, 2000); // 2 second delay to show success state
            } else {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].error(result.error || 'Failed to process URL');
                // Check if it's a network error and trigger connection test
                if (result.error?.includes('Network error') || result.error?.includes('Connection refused')) {
                    if (onNetworkError) {
                        onNetworkError();
                    }
                }
            }
        } catch (error) {
            console.error('Upload error:', error);
            setUploadState((prev)=>({
                    ...prev,
                    isProcessing: false,
                    uploadResult: {
                        success: false,
                        error: 'Upload failed'
                    }
                }));
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].error('Upload failed');
            // Trigger network error callback for any catch block errors
            if (onNetworkError) {
                onNetworkError();
            }
        }
    };
    const handleClearUpload = ()=>{
        // Clear any pending auto-close timeout
        if (autoCloseTimeoutRef.current) {
            clearTimeout(autoCloseTimeoutRef.current);
            autoCloseTimeoutRef.current = null;
        }
        setUploadState({
            type: null,
            files: [],
            url: '',
            isActive: false,
            isProcessing: false,
            uploadResult: null
        });
    };
    const getIconColor = ()=>{
        if (disabled) return 'text-gray-400';
        switch(selectedLanguage){
            case 'Tamil':
                return uploadState.isActive ? 'text-purple-600' : 'text-purple-500 hover:text-purple-600';
            case 'Telugu':
                return uploadState.isActive ? 'text-green-600' : 'text-green-500 hover:text-green-600';
            case 'Kannada':
                return uploadState.isActive ? 'text-orange-600' : 'text-orange-500 hover:text-orange-600';
            default:
                return uploadState.isActive ? 'text-blue-600' : 'text-blue-500 hover:text-blue-600';
        }
    };
    const renderUploadContent = ()=>{
        if (!uploadState.isActive || !uploadState.type) return null;
        const isFileType = uploadState.type === 'pdf' || uploadState.type === 'mp3';
        const isURLType = uploadState.type === 'youtube' || uploadState.type === 'article';
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "mb-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center justify-between mb-2",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: `p-1 rounded text-xs font-medium text-white ${uploadState.type === 'pdf' ? 'bg-red-500' : uploadState.type === 'mp3' ? 'bg-purple-500' : uploadState.type === 'youtube' ? 'bg-red-600' : uploadState.type === 'article' ? 'bg-blue-500' : 'bg-gray-500'}`,
                                    children: uploadState.type === 'pdf' ? 'PDF' : uploadState.type === 'mp3' ? 'MP3' : uploadState.type === 'youtube' ? 'YT' : uploadState.type === 'article' ? 'WEB' : '?'
                                }, void 0, false, {
                                    fileName: "[project]/components/chatComponents/ChatInputUpload.tsx",
                                    lineNumber: 361,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex flex-col",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-sm font-medium text-gray-700 dark:text-gray-300",
                                            children: [
                                                uploadState.type === 'pdf' && 'PDF/Document Upload',
                                                uploadState.type === 'mp3' && 'MP3 Audio Upload',
                                                uploadState.type === 'youtube' && 'YouTube URL',
                                                uploadState.type === 'article' && 'Article URL'
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/components/chatComponents/ChatInputUpload.tsx",
                                            lineNumber: 373,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: `text-xs flex items-center gap-1 ${uploadState.uploadResult?.success ? 'text-green-600 dark:text-green-400' : uploadState.uploadResult && !uploadState.uploadResult.success ? 'text-red-600 dark:text-red-400' : uploadState.isProcessing ? 'text-blue-600 dark:text-blue-400' : 'text-gray-500 dark:text-gray-400'}`,
                                            children: [
                                                uploadState.isProcessing && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "w-3 h-3 border border-blue-600 border-t-transparent rounded-full animate-spin"
                                                }, void 0, false, {
                                                    fileName: "[project]/components/chatComponents/ChatInputUpload.tsx",
                                                    lineNumber: 386,
                                                    columnNumber: 19
                                                }, this),
                                                uploadState.uploadResult?.success && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiCheckCircle"], {
                                                            className: "w-3 h-3"
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/chatComponents/ChatInputUpload.tsx",
                                                            lineNumber: 390,
                                                            columnNumber: 21
                                                        }, this),
                                                        uploadState.uploadResult.message?.includes('cache') && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiDatabase"], {
                                                            className: "w-3 h-3 text-blue-500",
                                                            title: "Loaded from cache"
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/chatComponents/ChatInputUpload.tsx",
                                                            lineNumber: 392,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true),
                                                uploadState.uploadResult && !uploadState.uploadResult.success && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiWarning"], {
                                                    className: "w-3 h-3"
                                                }, void 0, false, {
                                                    fileName: "[project]/components/chatComponents/ChatInputUpload.tsx",
                                                    lineNumber: 397,
                                                    columnNumber: 19
                                                }, this),
                                                getUploadStatusText()
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/components/chatComponents/ChatInputUpload.tsx",
                                            lineNumber: 379,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/chatComponents/ChatInputUpload.tsx",
                                    lineNumber: 372,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/chatComponents/ChatInputUpload.tsx",
                            lineNumber: 360,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "relative",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            ref: iconRef,
                                            type: "button",
                                            onClick: handleAddMoreContent,
                                            onKeyDown: (e)=>{
                                                if (e.key === 'Enter' || e.key === ' ') {
                                                    e.preventDefault();
                                                    handleAddMoreContent();
                                                }
                                            },
                                            disabled: disabled,
                                            className: `p-1.5 rounded-full transition-all duration-200 transform hover:scale-105 border border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 ${getIconColor()} ${disabled ? 'cursor-not-allowed opacity-50 bg-gray-100' : 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600 bg-white dark:bg-gray-700 shadow-sm hover:shadow-md'} ${showDropdown ? 'ring-2 ring-offset-1 ' + (selectedLanguage === 'Tamil' ? 'ring-purple-300 focus:ring-purple-500' : selectedLanguage === 'Telugu' ? 'ring-green-300 focus:ring-green-500' : selectedLanguage === 'Kannada' ? 'ring-orange-300 focus:ring-orange-500' : 'ring-blue-300 focus:ring-blue-500') : selectedLanguage === 'Tamil' ? 'focus:ring-purple-500' : selectedLanguage === 'Telugu' ? 'focus:ring-green-500' : selectedLanguage === 'Kannada' ? 'focus:ring-orange-500' : 'focus:ring-blue-500'}`,
                                            title: disabled ? 'Upload disabled' : showDropdown ? 'Close upload options (Press Esc)' : 'Add more files or change upload type (Press Enter)',
                                            "aria-label": disabled ? 'Upload disabled' : showDropdown ? 'Close upload options' : 'Add more files or change upload type',
                                            "aria-expanded": showDropdown,
                                            "aria-haspopup": "menu",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiPaperclip"], {
                                                className: `w-4 h-4 transition-transform duration-200 ${showDropdown ? 'rotate-45' : ''}`
                                            }, void 0, false, {
                                                fileName: "[project]/components/chatComponents/ChatInputUpload.tsx",
                                                lineNumber: 433,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/components/chatComponents/ChatInputUpload.tsx",
                                            lineNumber: 406,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: `absolute -top-1 -right-1 w-3 h-3 rounded-full text-xs flex items-center justify-center text-white font-bold ${uploadState.type === 'pdf' ? 'bg-red-500' : uploadState.type === 'mp3' ? 'bg-purple-500' : uploadState.type === 'youtube' ? 'bg-red-600' : uploadState.type === 'article' ? 'bg-blue-500' : 'bg-gray-500'}`,
                                            children: uploadState.type === 'pdf' ? 'P' : uploadState.type === 'mp3' ? '♪' : uploadState.type === 'youtube' ? 'Y' : uploadState.type === 'article' ? 'A' : '?'
                                        }, void 0, false, {
                                            fileName: "[project]/components/chatComponents/ChatInputUpload.tsx",
                                            lineNumber: 437,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/chatComponents/ChatInputUpload.tsx",
                                    lineNumber: 405,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: handleClearUpload,
                                    className: "p-1.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors rounded-full hover:bg-gray-200 dark:hover:bg-gray-600",
                                    title: "Clear upload",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiX"], {
                                        className: "w-4 h-4"
                                    }, void 0, false, {
                                        fileName: "[project]/components/chatComponents/ChatInputUpload.tsx",
                                        lineNumber: 454,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/components/chatComponents/ChatInputUpload.tsx",
                                    lineNumber: 449,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/chatComponents/ChatInputUpload.tsx",
                            lineNumber: 403,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/chatComponents/ChatInputUpload.tsx",
                    lineNumber: 359,
                    columnNumber: 9
                }, this),
                isFileType && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: uploadState.isProcessing ? 'opacity-50 pointer-events-none' : '',
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$chatComponents$2f$FileDropZone$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        acceptedTypes: uploadState.type === 'pdf' ? '.pdf,.doc,.docx,.txt,.rtf' : '.mp3,.wav,.m4a,.flac',
                        onFilesSelected: handleFileUpload,
                        maxFiles: 1,
                        selectedLanguage: selectedLanguage
                    }, void 0, false, {
                        fileName: "[project]/components/chatComponents/ChatInputUpload.tsx",
                        lineNumber: 461,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/components/chatComponents/ChatInputUpload.tsx",
                    lineNumber: 460,
                    columnNumber: 11
                }, this),
                isURLType && uploadState.type && (uploadState.type === 'youtube' || uploadState.type === 'article') && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: uploadState.isProcessing ? 'opacity-50 pointer-events-none' : '',
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$chatComponents$2f$URLInput$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                        type: uploadState.type,
                        value: uploadState.url,
                        onChange: handleURLChange,
                        onSubmit: handleURLSubmit,
                        selectedLanguage: selectedLanguage
                    }, void 0, false, {
                        fileName: "[project]/components/chatComponents/ChatInputUpload.tsx",
                        lineNumber: 472,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/components/chatComponents/ChatInputUpload.tsx",
                    lineNumber: 471,
                    columnNumber: 11
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/components/chatComponents/ChatInputUpload.tsx",
            lineNumber: 358,
            columnNumber: 7
        }, this);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "relative",
        children: [
            renderUploadContent(),
            showDropdown && !disabled && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                ref: dropdownRef,
                className: uploadState.isActive ? "absolute top-full right-0 mt-2 z-50" : "",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$chatComponents$2f$UploadDropdown$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                    onSelect: handleUploadTypeSelect,
                    selectedLanguage: selectedLanguage
                }, void 0, false, {
                    fileName: "[project]/components/chatComponents/ChatInputUpload.tsx",
                    lineNumber: 512,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/chatComponents/ChatInputUpload.tsx",
                lineNumber: 508,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/chatComponents/ChatInputUpload.tsx",
        lineNumber: 486,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = ChatInputUpload;
}}),
"[project]/components/chatComponents/services/ApiService.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "API_CONFIG": (()=>API_CONFIG),
    "ApiService": (()=>ApiService)
});
const API_CONFIG = {
    // Production endpoint (previously used)
    PROD_ENDPOINT: "http://localhost:5010/financial_query",
    // Development endpoint for suggest.py (running locally)
    DEV_ENDPOINT: "http://localhost:5010/financial_query",
    // Use DEV_ENDPOINT for local development, PROD_ENDPOINT for production
    ACTIVE_ENDPOINT: "http://localhost:5010/financial_query",
    // FAISS collection endpoint (keeping PINE for backward compatibility)
    PINE_COLLECTION_ENDPOINT: "https://dev-commonmannit.mannit.co/mannit/retrievecollection?ColName=FAISS"
};
class ApiService {
    // Function to fetch PINE collection data for the current user
    // Falls back to default configuration (default index) when:
    // - No user email is found
    // - API requests fail
    // - No PINE data exists for user
    // - Any errors occur
    static async fetchPineCollection() {
        try {
            const userEmail = localStorage.getItem("user_email");
            console.log("local mail", userEmail);
            if (!userEmail) {
                console.warn("No user email found in localStorage - using default configuration");
                // Set default configuration
                const defaultConfig = {
                    api_key: "pcsk_3pBLRt_49J48GS74JYL6yrCZRKMaERk4vNsF6FZgr3L7b7fZ6nqw8c9XUfzQhTwrXyWaua",
                    index_name: "default",
                    embed_model: "all-MiniLM-L6-v2"
                };
                localStorage.setItem("faiss_index_name", defaultConfig.index_name);
                if (defaultConfig.embed_model) {
                    localStorage.setItem("faiss_embed_model", defaultConfig.embed_model);
                }
                return defaultConfig;
            }
            // Use filtered API endpoint to get ALL entries for this user (remove filtercount=1 to get all matches)
            const filterUrl = `https://dev-commonmannit.mannit.co/mannit/retrievecollection?ColName=FAISS&f1_field=client&f1_op=eq&f1_value=${encodeURIComponent(userEmail.trim())}`;
            const response = await fetch(filterUrl, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'xxxid': 'FAISS'
                }
            });
            if (!response.ok) {
                console.warn(`Failed to fetch PINE collection: ${response.status} - using default configuration`);
                // Set default FAISS configuration
                const defaultConfig = {
                    index_name: "default",
                    embed_model: "all-MiniLM-L6-v2"
                };
                localStorage.setItem("faiss_index_name", defaultConfig.index_name);
                localStorage.setItem("faiss_embed_model", defaultConfig.embed_model);
                return defaultConfig;
            }
            const data = await response.json();
            console.log("FAISS collection response:", data);
            // Check if user exists in FAISS collection and extract all their indexes
            if (data.statusCode === 200 && data.source && data.source.length > 0) {
                // Parse each item in the source array (they are JSON strings)
                const faissData = data.source.map((item)=>JSON.parse(item));
                // Extract all indexes and embedding models for this user
                const userIndexes = [];
                const userEmbedModels = [];
                let firstUserEntry = null;
                faissData.forEach((item)=>{
                    if (item.client && item.client.trim().toLowerCase() === userEmail.trim().toLowerCase()) {
                        if (!firstUserEntry) firstUserEntry = item; // Keep first entry for return
                        if (item.index_name && !userIndexes.includes(item.index_name)) {
                            userIndexes.push(item.index_name);
                        }
                        if (item.embed_model && !userEmbedModels.includes(item.embed_model)) {
                            userEmbedModels.push(item.embed_model);
                        }
                    }
                });
                if (userIndexes.length > 0) {
                    // User exists in FAISS collection - store all their configurations
                    localStorage.setItem("faiss_index_name", userIndexes[0]); // Store first index as default
                    localStorage.setItem("faiss_embed_model", userEmbedModels[0] || "all-MiniLM-L6-v2"); // Store first embed model as default
                    localStorage.setItem('userFaissIndexes', JSON.stringify(userIndexes)); // Store all indexes
                    localStorage.setItem('userEmbedModels', JSON.stringify(userEmbedModels)); // Store all embed models
                    console.log("Found existing FAISS data for user:", userEmail);
                    console.log("User indexes:", userIndexes);
                    console.log("User embed models:", userEmbedModels);
                    return firstUserEntry;
                } else {
                    // User doesn't exist in FAISS collection - use default values without auto-creation
                    console.log("No FAISS data found for user:", userEmail, "- using default configuration without auto-creation");
                    const defaultConfig = {
                        index_name: "default",
                        embed_model: "all-MiniLM-L6-v2"
                    };
                    localStorage.setItem("faiss_index_name", defaultConfig.index_name);
                    localStorage.setItem("faiss_embed_model", defaultConfig.embed_model);
                    localStorage.setItem("faiss_client_email", userEmail);
                    console.log("Using default FAISS configuration for user:", userEmail);
                    return defaultConfig;
                }
            } else {
                // No FAISS data found - use default values without auto-creation
                console.log("No FAISS data found for user:", userEmail, "- using default configuration without auto-creation");
                const defaultConfig = {
                    index_name: "default",
                    embed_model: "all-MiniLM-L6-v2"
                };
                localStorage.setItem("faiss_index_name", defaultConfig.index_name);
                localStorage.setItem("faiss_embed_model", defaultConfig.embed_model);
                localStorage.setItem("faiss_client_email", userEmail);
                console.log("Using default FAISS configuration for user:", userEmail);
                return defaultConfig;
            }
        } catch (error) {
            console.warn("Error fetching FAISS collection - using default configuration:", error);
            // Fallback to default values on error
            const defaultConfig = {
                index_name: "default",
                embed_model: "all-MiniLM-L6-v2"
            };
            localStorage.setItem("faiss_index_name", defaultConfig.index_name);
            localStorage.setItem("faiss_embed_model", defaultConfig.embed_model);
            return defaultConfig;
        }
    }
    // Function to get current user's email from session storage
    static getCurrentUserEmail() {
        try {
            if ("TURBOPACK compile-time truthy", 1) return null;
            "TURBOPACK unreachable";
            // Try multiple sources for user email
            const directEmail = undefined;
            // Try from user session data
            const userSession = undefined;
        } catch (error) {
            console.error('Error getting current user email:', error);
            return null;
        }
    }
    // Function to fetch user-specific FAISS indexes from the backend
    static async fetchUserIndexes() {
        try {
            console.log("Fetching user-specific FAISS indexes...");
            // Get current user's email
            const userEmail = this.getCurrentUserEmail();
            if (!userEmail) {
                console.warn("⚠️ No user email found, fetching all available indexes");
                // Fall back to GET request for all indexes if no user email
                const response = await fetch('http://localhost:5010/api/list-faiss-indexes', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.indexes && data.indexes.length > 0) {
                        console.log("✅ Retrieved all FAISS indexes:", data.indexes);
                        return data.indexes;
                    }
                }
                console.warn("⚠️ Falling back to default index");
                return [
                    "default"
                ];
            }
            console.log(`🔍 Fetching indexes for user: ${userEmail}`);
            // Call the list-faiss-indexes endpoint with user email for filtering
            const response = await fetch('http://localhost:5010/api/list-faiss-indexes', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    email: userEmail
                })
            });
            if (response.ok) {
                const data = await response.json();
                if (data.success && data.indexes && data.indexes.length > 0) {
                    console.log(`✅ Retrieved ${data.indexes.length} FAISS indexes for user ${userEmail}:`, data.indexes);
                    return data.indexes;
                } else {
                    console.warn(`⚠️ No FAISS indexes found for user ${userEmail}:`, data.error || "Unknown error");
                    // Return empty array for users with no indexes instead of default
                    return [];
                }
            } else {
                console.warn("⚠️ FAISS indexes API failed:", response.status);
            }
            // Fallback to empty array if user-specific request fails
            console.warn("⚠️ No indexes available for user");
            return [];
        } catch (error) {
            console.error("Error fetching user-specific FAISS indexes:", error);
            return [];
        }
    }
    // Function to send query to API
    static async sendQuery(requestBody) {
        const response = await fetch(API_CONFIG.ACTIVE_ENDPOINT, {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify(requestBody)
        });
        if (!response.ok) {
            // Check if the error is related to the index
            if (response.status === 404) {
                const errorData = await response.json();
                if (errorData.error && errorData.error.includes("No matching documents found or index not available")) {
                    throw new Error(`The selected index is not available or contains no relevant data. Please try another index.`);
                }
            }
            throw new Error(`API response error: ${response.status}`);
        }
        return await response.json();
    }
}
}}),
"[project]/components/chatComponents/services/TranslationService.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "TranslationService": (()=>TranslationService)
});
class TranslationService {
    static translationCache = new Map();
    // Language detection helper
    static detectLanguage(text) {
        if (!text || !text.trim()) return 'en';
        // Tamil detection using Unicode ranges
        if (/[\u0B80-\u0BFF]/.test(text)) return 'ta';
        // Hindi detection using Unicode ranges
        if (/[\u0900-\u097F]/.test(text)) return 'hi';
        // Arabic detection
        if (/[\u0600-\u06FF]/.test(text)) return 'ar';
        // Chinese detection
        if (/[\u4e00-\u9fff]/.test(text)) return 'zh';
        // Default to English
        return 'en';
    }
    // Function to translate text using multiple translation services with proper fallbacks
    static async translateText(text, sourceLang, targetLang) {
        console.log(`🔄 Translating from ${sourceLang} to ${targetLang}: ${text.substring(0, 50)}${text.length > 50 ? '...' : ''}`);
        // If source and target languages are the same, return original text
        if (sourceLang === targetLang) {
            console.log("⚠️ Source and target languages are the same, returning original text");
            return text;
        }
        // Check cache first
        const cacheKey = `${sourceLang}-${targetLang}-${text}`;
        const cachedTranslation = this.translationCache.get(cacheKey);
        if (cachedTranslation) {
            console.log("💾 Using cached translation");
            return cachedTranslation;
        }
        // Try multiple translation services in order of preference
        const translationServices = [
            // Service 1: MyMemory API (free and supports Tamil)
            async ()=>{
                const controller = new AbortController();
                const timeoutId = setTimeout(()=>controller.abort(), 8000);
                try {
                    const langPair = `${sourceLang}|${targetLang}`;
                    const url = `https://api.mymemory.translated.net/get?q=${encodeURIComponent(text)}&langpair=${langPair}`;
                    const response = await fetch(url, {
                        signal: controller.signal,
                        headers: {
                            'Accept': 'application/json'
                        }
                    });
                    clearTimeout(timeoutId);
                    if (!response.ok) throw new Error(`MyMemory API error: ${response.status}`);
                    const data = await response.json();
                    if (data.responseStatus === 200 && data.responseData && data.responseData.translatedText) {
                        console.log("✅ MyMemory translation successful");
                        return data.responseData.translatedText;
                    }
                    throw new Error('MyMemory API returned invalid response');
                } catch (error) {
                    clearTimeout(timeoutId);
                    console.log("❌ MyMemory translation failed:", error);
                    throw error;
                }
            },
            // Service 2: LibreTranslate (backup service)
            async ()=>{
                const controller = new AbortController();
                const timeoutId = setTimeout(()=>controller.abort(), 10000);
                try {
                    const url = 'https://libretranslate.de/translate';
                    const response = await fetch(url, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify({
                            q: text,
                            source: sourceLang,
                            target: targetLang,
                            format: 'text'
                        }),
                        signal: controller.signal
                    });
                    clearTimeout(timeoutId);
                    if (!response.ok) throw new Error(`LibreTranslate API error: ${response.status}`);
                    const data = await response.json();
                    if (data.translatedText) {
                        console.log("✅ LibreTranslate translation successful");
                        return data.translatedText;
                    }
                    throw new Error('LibreTranslate returned invalid response');
                } catch (error) {
                    clearTimeout(timeoutId);
                    console.log("❌ LibreTranslate translation failed:", error);
                    throw error;
                }
            },
            // Service 3: Enhanced dictionary-based translation (fallback)
            async ()=>{
                console.log("🔄 Using enhanced dictionary-based translation");
                return this.dictionaryBasedTranslation(text, sourceLang, targetLang);
            }
        ];
        // Try each translation service
        for(let i = 0; i < translationServices.length; i++){
            try {
                console.log(`Trying translation service ${i + 1}...`);
                const startTime = Date.now();
                const result = await translationServices[i]();
                const endTime = Date.now();
                if (result && result.trim().length > 0) {
                    console.log(`Translation successful with service ${i + 1} in ${endTime - startTime}ms`);
                    // Cache the successful translation
                    this.translationCache.set(cacheKey, result);
                    return result;
                }
            } catch (error) {
                console.log(`Translation service ${i + 1} failed:`, error);
                continue;
            }
        }
        // If all services fail, return original text with a note
        console.warn("All translation services failed, returning original text");
        return text;
    }
    // Enhanced dictionary-based translation with better coverage
    static dictionaryBasedTranslation(text, sourceLang, targetLang) {
        // Tamil-English dictionary mappings
        const tamilToEnglish = {
            // Basic words
            'என்ன': 'what',
            'எப்படி': 'how',
            'எங்கே': 'where',
            'எப்போது': 'when',
            'ஏன்': 'why',
            'யார்': 'who',
            'எது': 'which',
            'எவ்வளவு': 'how much',
            'எத்தனை': 'how many',
            // Financial terms
            'பணம்': 'money',
            'வங்கி': 'bank',
            'கடன்': 'loan',
            'முதலீடு': 'investment',
            'பங்கு': 'share',
            'சந்தை': 'market',
            'விலை': 'price',
            'வட்டி': 'interest',
            'லாபம்': 'profit',
            'நஷ்டம்': 'loss',
            'வருமானம்': 'income',
            'செலவு': 'expense',
            'பொருளாதாரம்': 'economy',
            'வணிகம்': 'business',
            'நிறுவனம்': 'company',
            // Common verbs
            'வேண்டும்': 'want',
            'தெரிய': 'know',
            'சொல்லு': 'tell',
            'கொடு': 'give',
            'வா': 'come',
            'போ': 'go',
            'பார்': 'see',
            'கேள்': 'ask',
            // Question words and phrases
            'பற்றி சொல்லுங்கள்': 'tell me about',
            'என்ன ஆகும்': 'what will happen',
            'எப்படி செய்வது': 'how to do',
            'ஏன் ஆகிறது': 'why is happening'
        };
        const englishToTamil = {
            // Reverse mapping for English to Tamil
            'what': 'என்ன',
            'how': 'எப்படி',
            'where': 'எங்கே',
            'when': 'எப்போது',
            'why': 'ஏன்',
            'who': 'யார்',
            'which': 'எது',
            'how much': 'எவ்வளவு',
            'how many': 'எத்தனை',
            // Financial terms
            'money': 'பணம்',
            'bank': 'வங்கி',
            'loan': 'கடன்',
            'investment': 'முதலீடு',
            'share': 'பங்கு',
            'market': 'சந்தை',
            'price': 'விலை',
            'interest': 'வட்டி',
            'profit': 'லாபம்',
            'loss': 'நஷ்டம்',
            'income': 'வருமானம்',
            'expense': 'செலவு',
            'economy': 'பொருளாதாரம்',
            'business': 'வணிகம்',
            'company': 'நிறுவனம்',
            // Common responses
            'the': 'அந்த',
            'and': 'மற்றும்',
            'is': 'உள்ளது',
            'are': 'உள்ளன',
            'this': 'இது',
            'that': 'அது',
            'with': 'உடன்',
            'for': 'க்காக',
            'from': 'இருந்து',
            'to': 'க்கு',
            'in': 'இல்',
            'on': 'மீது',
            'will': 'வேண்டும்',
            'can': 'முடியும்',
            'has': 'உள்ளது',
            'have': 'உள்ளது',
            // Financial context
            'economic': 'பொருளாதார',
            'financial': 'நிதி',
            'growth': 'வளர்ச்சி',
            'development': 'வளர்ச்சி',
            'government': 'அரசாங்கம்',
            'policy': 'கொள்கை',
            'rate': 'விகிதம்',
            'percent': 'சதவீதம்',
            'million': 'மில்லியன்',
            'billion': 'பில்லியன்',
            'thousand': 'ஆயிரம்',
            'hundred': 'நூறு'
        };
        let translatedText = text;
        let dictionary = {};
        // Select appropriate dictionary
        if (sourceLang === 'ta' && targetLang === 'en') {
            dictionary = tamilToEnglish;
        } else if (sourceLang === 'en' && targetLang === 'ta') {
            dictionary = englishToTamil;
        } else {
            // For other language pairs, return original text with a prefix
            return `[Dictionary translation not available for ${sourceLang} to ${targetLang}] ${text}`;
        }
        // Apply word-by-word translation while preserving sentence structure
        for (const [sourceWord, targetWord] of Object.entries(dictionary)){
            // Use word boundaries to avoid partial matches
            const regex = new RegExp(`\\b${sourceWord.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'gi');
            translatedText = translatedText.replace(regex, targetWord);
        }
        // Add translation indicator
        const indicator = sourceLang === 'ta' && targetLang === 'en' ? '[Tamil→English Dictionary]' : '[English→Tamil Dictionary]';
        return `${indicator} ${translatedText}`;
    }
    // Function to extract and preserve capital words during translation
    static extractCapitalWords(text) {
        const capitalWordsMatches = text.match(/\b[A-Z]{2,}\b/g) || [];
        const capitalWords = capitalWordsMatches.map((word)=>({
                word,
                placeholder: `__CAPITAL_WORD_${Math.random().toString(36).substring(2, 10)}__`
            }));
        let textWithPlaceholders = text;
        capitalWords.forEach((item)=>{
            textWithPlaceholders = textWithPlaceholders.replace(item.word, item.placeholder);
        });
        return {
            text: textWithPlaceholders,
            capitalWords
        };
    }
    // Function to restore capital words after translation
    static restoreCapitalWords(text, capitalWords) {
        let restoredText = text;
        capitalWords.forEach((item)=>{
            restoredText = restoredText.replace(item.placeholder, item.word);
        });
        return restoredText;
    }
    // Function to translate text while preserving capital words
    static async translateWithCapitalWordsPreservation(text, sourceLang, targetLang) {
        const { text: textWithPlaceholders, capitalWords } = this.extractCapitalWords(text);
        const translatedText = await this.translateText(textWithPlaceholders, sourceLang, targetLang);
        return this.restoreCapitalWords(translatedText, capitalWords);
    }
    // Function to translate entire response objects
    static async translateResponse(response, targetLang) {
        if (!response || !targetLang) return response;
        const translatedResponse = {
            ...response
        };
        try {
            // Detect source language from AI response
            const sourceLang = response.ai_response ? this.detectLanguage(response.ai_response) : 'en';
            // Skip translation if source and target are the same
            if (sourceLang === targetLang) {
                console.log(`⚠️ Source and target languages are the same (${targetLang}), skipping translation`);
                return response;
            }
            console.log(`🌐 Translating response from ${sourceLang} to ${targetLang}`);
            // Translate AI response
            if (response.ai_response) {
                translatedResponse.ai_response = await this.translateWithCapitalWordsPreservation(response.ai_response, sourceLang, targetLang);
            }
            // Translate related questions
            if (response.related_questions && Array.isArray(response.related_questions)) {
                translatedResponse.related_questions = await Promise.all(response.related_questions.map((question)=>this.translateWithCapitalWordsPreservation(question, sourceLang, targetLang)));
            }
            // Add translation metadata
            translatedResponse.translation_applied = true;
            translatedResponse.source_language = sourceLang;
            translatedResponse.target_language = targetLang;
            translatedResponse.translation_timestamp = new Date().toISOString();
            console.log(`✅ Response translation completed: ${sourceLang} -> ${targetLang}`);
            return translatedResponse;
        } catch (error) {
            console.error('❌ Error translating response:', error);
            // Return original response with error metadata
            return {
                ...response,
                translation_applied: false,
                translation_error: error instanceof Error ? error.message : 'Unknown translation error'
            };
        }
    }
    // Function to get language name from code
    static getLanguageName(langCode) {
        const languageNames = {
            'en': 'English',
            'ta': 'Tamil',
            'hi': 'Hindi',
            'es': 'Spanish',
            'fr': 'French',
            'de': 'German',
            'zh': 'Chinese',
            'ja': 'Japanese',
            'ko': 'Korean',
            'ar': 'Arabic'
        };
        return languageNames[langCode] || langCode;
    }
    // Function to clear translation cache
    static clearCache() {
        this.translationCache.clear();
        console.log('🗑️ Translation cache cleared');
    }
    // Function to get cache statistics
    static getCacheStats() {
        return {
            size: this.translationCache.size,
            keys: Array.from(this.translationCache.keys()).slice(0, 10) // Show first 10 keys
        };
    }
}
}}),
"[project]/components/chatComponents/services/ValidationService.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "ValidationService": (()=>ValidationService)
});
class ValidationService {
    // Function to detect if text is likely Tamil
    static isTamilText(text) {
        // Tamil Unicode range: \u0B80-\u0BFF
        const tamilRegex = /[\u0B80-\u0BFF]/;
        return tamilRegex.test(text);
    }
    // Function to detect if text is likely Telugu
    static isTeluguText(text) {
        // Telugu Unicode range: \u0C00-\u0C7F
        const teluguRegex = /[\u0C00-\u0C7F]/;
        return teluguRegex.test(text);
    }
    // Function to detect if text is likely Kannada
    static isKannadaText(text) {
        // Kannada Unicode range: \u0C80-\u0CFF
        const kannadaRegex = /[\u0C80-\u0CFF]/;
        return kannadaRegex.test(text);
    }
    // Function to validate if text matches the selected language
    static validateLanguageMatch(text, language) {
        if (!text || text.trim() === "") return true; // Empty text is valid for any language
        // First, remove continuous capital English words (acronyms, proper nouns, etc.)
        // These should be preserved in any language and not affect validation
        const textWithoutCapitalWords = text.replace(/\b[A-Z]{2,}\b/g, '');
        // Check if the remaining text contains characters from different languages
        const hasTamilChars = this.isTamilText(textWithoutCapitalWords);
        const hasTeluguChars = this.isTeluguText(textWithoutCapitalWords);
        const hasKannadaChars = this.isKannadaText(textWithoutCapitalWords);
        // Count how many different language scripts are present
        const scriptCount = (hasTamilChars ? 1 : 0) + (hasTeluguChars ? 1 : 0) + (hasKannadaChars ? 1 : 0);
        // If there are multiple scripts, it's likely a mismatch
        if (scriptCount > 1) {
            return false;
        }
        // English can contain any characters, so we only validate non-English languages
        if (language === "English") {
            // For English, we consider it valid if it doesn't contain Tamil, Telugu, or Kannada characters
            return !(hasTamilChars || hasTeluguChars || hasKannadaChars);
        } else if (language === "Tamil") {
            // For Tamil, it should contain Tamil characters
            return hasTamilChars || textWithoutCapitalWords.trim() === '';
        } else if (language === "Telugu") {
            // For Telugu, it should contain Telugu characters
            return hasTeluguChars || textWithoutCapitalWords.trim() === '';
        } else if (language === "Kannada") {
            // For Kannada, it should contain Kannada characters
            return hasKannadaChars || textWithoutCapitalWords.trim() === '';
        }
        return true; // Default case
    }
    // Function to detect the language of the text
    static detectLanguage(text) {
        if (this.isTamilText(text)) return "Tamil";
        if (this.isTeluguText(text)) return "Telugu";
        if (this.isKannadaText(text)) return "Kannada";
        return "English";
    }
}
}}),
"[project]/components/chatComponents/services/CacheService.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "CacheService": (()=>CacheService)
});
class CacheService {
    static CACHE_KEY = 'financial_query_cache';
    static MAX_CACHE_SIZE = 100;
    static CACHE_EXPIRY_HOURS = 24;
    static STATS_KEY = 'financial_query_cache_stats';
    static ARTIFICIAL_DELAY_MS = 2000;
    /**
   * Generate a cache key based on query, context, and language
   */ static generateCacheKey(query, context, language) {
        const normalizedQuery = query.trim().toLowerCase();
        const contextStr = context || '';
        const languageStr = language || 'en';
        return `${normalizedQuery}|${contextStr}|${languageStr}`;
    }
    /**
   * Get cached response for a query with language support
   */ static getCachedResponse(query, context, language) {
        try {
            if ("TURBOPACK compile-time truthy", 1) return null;
            "TURBOPACK unreachable";
            const cacheKey = undefined;
            const cacheData = undefined;
            const cache = undefined;
            const cachedItem = undefined;
            // Check if cache has expired
            const now = undefined;
            const expiryTime = undefined;
        } catch (error) {
            console.error('Error retrieving cached response:', error);
            this.updateStats('miss');
            return null;
        }
    }
    /**
   * Cache a response for a query with language support
   */ static setCachedResponse(query, response, context, language) {
        try {
            if ("TURBOPACK compile-time truthy", 1) return;
            "TURBOPACK unreachable";
            const cacheKey = undefined;
            const cacheData = undefined;
            let cache;
            // Create cached response object with language metadata
            const cachedResponse = undefined;
            // Implement LRU eviction if cache is too large
            const cacheKeys = undefined;
        } catch (error) {
            console.error('Error caching response:', error);
        }
    }
    /**
   * Update cache statistics
   */ static updateStats(type) {
        try {
            if ("TURBOPACK compile-time truthy", 1) return;
            "TURBOPACK unreachable";
            const statsData = undefined;
            let stats;
        } catch (error) {
            console.error('Error updating cache stats:', error);
        }
    }
    /**
   * Get cache statistics
   */ static getCacheStats() {
        try {
            if ("TURBOPACK compile-time truthy", 1) {
                return {
                    totalQueries: 0,
                    cacheHits: 0,
                    cacheMisses: 0,
                    hitRate: 0,
                    cacheSize: 0
                };
            }
            "TURBOPACK unreachable";
            const statsData = undefined;
            const cacheData = undefined;
            let stats;
            const cacheSize = undefined;
            const hitRate = undefined;
        } catch (error) {
            console.error('Error getting cache stats:', error);
            return {
                totalQueries: 0,
                cacheHits: 0,
                cacheMisses: 0,
                hitRate: 0,
                cacheSize: 0
            };
        }
    }
    /**
   * Clear all cached responses
   */ static clearCache() {
        try {
            if ("TURBOPACK compile-time truthy", 1) return;
            "TURBOPACK unreachable";
        } catch (error) {
            console.error('Error clearing cache:', error);
        }
    }
    /**
   * Remove expired cache entries
   */ static cleanupExpiredCache() {
        try {
            if ("TURBOPACK compile-time truthy", 1) return;
            "TURBOPACK unreachable";
            const cacheData = undefined;
            const cache = undefined;
            const now = undefined;
            const expiryTime = undefined;
            let removedCount;
        } catch (error) {
            console.error('Error cleaning up expired cache:', error);
        }
    }
    /**
   * Apply artificial delay for cached responses to ensure consistent UX
   */ static async applyCachedResponseDelay() {
        console.log(`⏳ Applying ${this.ARTIFICIAL_DELAY_MS}ms artificial delay for cached response...`);
        return new Promise((resolve)=>{
            setTimeout(()=>{
                console.log(`✅ Artificial delay completed - returning cached response`);
                resolve();
            }, this.ARTIFICIAL_DELAY_MS);
        });
    }
    /**
   * Get cached response with automatic delay application
   */ static async getCachedResponseWithDelay(query, context, language) {
        const cachedResponse = this.getCachedResponse(query, context, language);
        if (cachedResponse) {
            // Apply artificial delay for consistent UX
            await this.applyCachedResponseDelay();
            return cachedResponse;
        }
        return null;
    }
}
}}),
"[project]/components/debug/ConnectionTest.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$uploadService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/services/uploadService.ts [app-ssr] (ecmascript)");
;
;
;
const ConnectionTest = ({ onClose })=>{
    const [testResults, setTestResults] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isRunning, setIsRunning] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const addResult = (test, result)=>{
        setTestResults((prev)=>[
                ...prev,
                {
                    test,
                    result,
                    timestamp: new Date().toISOString()
                }
            ]);
    };
    const runTests = async ()=>{
        setIsRunning(true);
        setTestResults([]);
        try {
            // Test 1: Basic connection
            console.log('🔌 Running connection test...');
            const connectionResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$uploadService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["testConnection"])();
            addResult('Connection Test', connectionResult);
            // Test 2: Health check
            console.log('🏥 Running health check...');
            const healthResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$uploadService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["checkBackendHealth"])();
            addResult('Health Check', healthResult);
            // Test 3: YouTube URL processing (with a short test video)
            console.log('🎥 Testing YouTube processing...');
            const youtubeResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$services$2f$uploadService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processYouTubeURL"])('https://youtu.be/dQw4w9WgXcQ', {
                index_name: 'default',
                client_email: '<EMAIL>'
            });
            addResult('YouTube Processing', youtubeResult);
        } catch (error) {
            console.error('❌ Test suite error:', error);
            addResult('Test Suite Error', {
                success: false,
                error: error.message
            });
        } finally{
            setIsRunning(false);
        }
    };
    const clearResults = ()=>{
        setTestResults([]);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "bg-white dark:bg-gray-800 rounded-lg p-6 max-w-4xl max-h-[80vh] overflow-auto",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex justify-between items-center mb-4",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                            className: "text-xl font-bold text-gray-900 dark:text-white",
                            children: "Backend Connection Test"
                        }, void 0, false, {
                            fileName: "[project]/components/debug/ConnectionTest.tsx",
                            lineNumber: 55,
                            columnNumber: 11
                        }, this),
                        onClose && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: onClose,
                            className: "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200",
                            children: "✕"
                        }, void 0, false, {
                            fileName: "[project]/components/debug/ConnectionTest.tsx",
                            lineNumber: 59,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/debug/ConnectionTest.tsx",
                    lineNumber: 54,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-4",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex gap-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: runTests,
                                    disabled: isRunning,
                                    className: `px-4 py-2 rounded-lg text-white font-medium ${isRunning ? 'bg-gray-400 cursor-not-allowed' : 'bg-blue-500 hover:bg-blue-600'}`,
                                    children: isRunning ? 'Running Tests...' : 'Run Tests'
                                }, void 0, false, {
                                    fileName: "[project]/components/debug/ConnectionTest.tsx",
                                    lineNumber: 70,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: clearResults,
                                    className: "px-4 py-2 rounded-lg bg-gray-500 hover:bg-gray-600 text-white font-medium",
                                    children: "Clear Results"
                                }, void 0, false, {
                                    fileName: "[project]/components/debug/ConnectionTest.tsx",
                                    lineNumber: 81,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/debug/ConnectionTest.tsx",
                            lineNumber: 69,
                            columnNumber: 11
                        }, this),
                        testResults.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "space-y-3",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: "text-lg font-semibold text-gray-900 dark:text-white",
                                    children: "Test Results"
                                }, void 0, false, {
                                    fileName: "[project]/components/debug/ConnectionTest.tsx",
                                    lineNumber: 91,
                                    columnNumber: 15
                                }, this),
                                testResults.map((result, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: `p-4 rounded-lg border ${result.result.success ? 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800' : 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800'}`,
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center gap-2 mb-2",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: `w-3 h-3 rounded-full ${result.result.success ? 'bg-green-500' : 'bg-red-500'}`
                                                    }, void 0, false, {
                                                        fileName: "[project]/components/debug/ConnectionTest.tsx",
                                                        lineNumber: 104,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                        className: "font-medium text-gray-900 dark:text-white",
                                                        children: result.test
                                                    }, void 0, false, {
                                                        fileName: "[project]/components/debug/ConnectionTest.tsx",
                                                        lineNumber: 109,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-xs text-gray-500 dark:text-gray-400",
                                                        children: new Date(result.timestamp).toLocaleTimeString()
                                                    }, void 0, false, {
                                                        fileName: "[project]/components/debug/ConnectionTest.tsx",
                                                        lineNumber: 112,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/components/debug/ConnectionTest.tsx",
                                                lineNumber: 103,
                                                columnNumber: 19
                                            }, this),
                                            result.result.success ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-green-700 dark:text-green-300",
                                                children: [
                                                    "✅ ",
                                                    result.result.message || 'Success'
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/components/debug/ConnectionTest.tsx",
                                                lineNumber: 118,
                                                columnNumber: 21
                                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-red-700 dark:text-red-300",
                                                children: [
                                                    "❌ ",
                                                    result.result.error || 'Failed'
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/components/debug/ConnectionTest.tsx",
                                                lineNumber: 122,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("details", {
                                                className: "mt-2",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("summary", {
                                                        className: "cursor-pointer text-sm text-gray-600 dark:text-gray-400",
                                                        children: "Show Details"
                                                    }, void 0, false, {
                                                        fileName: "[project]/components/debug/ConnectionTest.tsx",
                                                        lineNumber: 129,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("pre", {
                                                        className: "mt-2 text-xs bg-gray-100 dark:bg-gray-700 p-2 rounded overflow-auto",
                                                        children: JSON.stringify(result.result, null, 2)
                                                    }, void 0, false, {
                                                        fileName: "[project]/components/debug/ConnectionTest.tsx",
                                                        lineNumber: 132,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/components/debug/ConnectionTest.tsx",
                                                lineNumber: 128,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, index, true, {
                                        fileName: "[project]/components/debug/ConnectionTest.tsx",
                                        lineNumber: 95,
                                        columnNumber: 17
                                    }, this))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/debug/ConnectionTest.tsx",
                            lineNumber: 90,
                            columnNumber: 13
                        }, this),
                        isRunning && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-2 text-blue-600 dark:text-blue-400",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "animate-spin w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full"
                                }, void 0, false, {
                                    fileName: "[project]/components/debug/ConnectionTest.tsx",
                                    lineNumber: 143,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    children: "Running tests..."
                                }, void 0, false, {
                                    fileName: "[project]/components/debug/ConnectionTest.tsx",
                                    lineNumber: 144,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/debug/ConnectionTest.tsx",
                            lineNumber: 142,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/debug/ConnectionTest.tsx",
                    lineNumber: 68,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                            className: "font-medium text-gray-900 dark:text-white mb-2",
                            children: "Debug Information"
                        }, void 0, false, {
                            fileName: "[project]/components/debug/ConnectionTest.tsx",
                            lineNumber: 150,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "text-sm text-gray-600 dark:text-gray-400 space-y-1",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                            children: "Backend URL:"
                                        }, void 0, false, {
                                            fileName: "[project]/components/debug/ConnectionTest.tsx",
                                            lineNumber: 154,
                                            columnNumber: 16
                                        }, this),
                                        " http://localhost:5010"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/debug/ConnectionTest.tsx",
                                    lineNumber: 154,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                            children: "Environment:"
                                        }, void 0, false, {
                                            fileName: "[project]/components/debug/ConnectionTest.tsx",
                                            lineNumber: 155,
                                            columnNumber: 16
                                        }, this),
                                        " ",
                                        ("TURBOPACK compile-time value", "development") || 'development'
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/debug/ConnectionTest.tsx",
                                    lineNumber: 155,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                            children: "User Agent:"
                                        }, void 0, false, {
                                            fileName: "[project]/components/debug/ConnectionTest.tsx",
                                            lineNumber: 156,
                                            columnNumber: 16
                                        }, this),
                                        " ",
                                        navigator.userAgent
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/debug/ConnectionTest.tsx",
                                    lineNumber: 156,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                            children: "Current Time:"
                                        }, void 0, false, {
                                            fileName: "[project]/components/debug/ConnectionTest.tsx",
                                            lineNumber: 157,
                                            columnNumber: 16
                                        }, this),
                                        " ",
                                        new Date().toISOString()
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/debug/ConnectionTest.tsx",
                                    lineNumber: 157,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/debug/ConnectionTest.tsx",
                            lineNumber: 153,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/debug/ConnectionTest.tsx",
                    lineNumber: 149,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/components/debug/ConnectionTest.tsx",
            lineNumber: 53,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/debug/ConnectionTest.tsx",
        lineNumber: 52,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = ConnectionTest;
}}),
"[project]/components/chatComponents/ChatBox.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$jsx$2f$style$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/styled-jsx/style.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$stores$2f$chatList$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/stores/chatList.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$speech$2d$recognition$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/react-speech-recognition/dist/index.js [app-ssr] (ecmascript)");
// Import components that are actually used
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$chatComponents$2f$ChatInputUpload$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/components/chatComponents/ChatInputUpload.tsx [app-ssr] (ecmascript)");
// import UploadedContentIndicator from "./UploadedContentIndicator";
// Import services that are actually used
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$chatComponents$2f$services$2f$ApiService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/components/chatComponents/services/ApiService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$chatComponents$2f$services$2f$TranslationService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/components/chatComponents/services/TranslationService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$chatComponents$2f$services$2f$ValidationService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/components/chatComponents/services/ValidationService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$chatComponents$2f$services$2f$CacheService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/components/chatComponents/services/CacheService.ts [app-ssr] (ecmascript)");
// Import debug component for connection testing
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$debug$2f$ConnectionTest$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/components/debug/ConnectionTest.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/react-icons/pi/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$v4$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__ = __turbopack_import__("[project]/node_modules/uuid/dist/esm/v4.js [app-ssr] (ecmascript) <export default as v4>");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const ChatBox = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ onLanguageChange }, ref)=>{
    const [inputText, setInputText] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("");
    const [showSuggestions, setShowSuggestions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Add language dropup state for responsive design
    const [showLanguageMenu, setShowLanguageMenu] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [selectedLanguage, setSelectedLanguage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("English");
    const [isListening, setIsListening] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [speaking, setSpeaking] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [wordCount, setWordCount] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(0);
    // Track recent words for word count
    const [recentWords, setRecentWords] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    // Add state for transcript editing
    const [isEditingTranscript, setIsEditingTranscript] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [editedTranscript, setEditedTranscript] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("");
    // Add state for language validation
    const [languageError, setLanguageError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    // Add state to track if language buttons should be disabled
    const [languageButtonsDisabled, setLanguageButtonsDisabled] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Add state for user email from localStorage
    const [userEmail, setUserEmail] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    // Add state for FAISS indexes - fetch from PINE collection
    const [pineconeIndexes, setPineconeIndexes] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [selectedIndex, setSelectedIndex] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    const [apiEnvironment, setApiEnvironment] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('production');
    // Add state for loading indexes
    const [indexesLoading, setIndexesLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    // Add state for index selector dropdown visibility
    const [showIndexSelector, setShowIndexSelector] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Add state for index selection confirmation
    const [showIndexConfirmation, setShowIndexConfirmation] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Add state to track if user has made first request - check localStorage for persistence
    const [hasUserMadeFirstRequest, setHasUserMadeFirstRequest] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(()=>{
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        return false;
    });
    // Add state to track upload component state
    const [uploadIsActive, setUploadIsActive] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [uploadDropdownVisible, setUploadDropdownVisible] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Add state for connection test dialog
    const [showConnectionTest, setShowConnectionTest] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Handle upload state changes
    const handleUploadStateChange = (isActive, showDropdown)=>{
        setUploadIsActive(isActive);
        setUploadDropdownVisible(showDropdown);
    };
    // Add state for uploaded content display
    const [uploadedFiles, setUploadedFiles] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [uploadedURLs, setUploadedURLs] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [showUploadedContent, setShowUploadedContent] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Add state for response data sources
    const [responseHasUploadedContent, setResponseHasUploadedContent] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [responseUploadSources, setResponseUploadSources] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    // Reference for the index selector dropdown
    const indexSelectorRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const suggestionsRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Add language menu ref for responsive design
    const languageMenuRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const transcriptRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const editableTranscriptRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    const path = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePathname"])();
    const { userQuery, handleSubmit, addMessage, setUserQuery, isLoading, setIsLoading, chatList } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$stores$2f$chatList$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useChatHandler"])();
    // Check if current chat has existing messages
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const chatIdUrl = path && path.includes("/chat/") ? path.split("/chat/")[1] : "";
        if (chatIdUrl) {
            const currentChat = chatList.find((chat)=>chat.id === chatIdUrl);
            if (currentChat && currentChat.messages && currentChat.messages.length > 0) {
                console.log("🔍 ChatBox: Found existing chat with messages, setting hasUserMadeFirstRequest to true");
                setHasUserMadeFirstRequest(true);
                if ("TURBOPACK compile-time falsy", 0) {
                    "TURBOPACK unreachable";
                }
            }
        }
    }, [
        chatList,
        path
    ]);
    // Speech detection timer
    const speakingTimerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Expose methods to parent component
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useImperativeHandle"])(ref, ()=>({
            setInputFromQuestion: (question)=>{
                console.log("🎯 ChatBox: setInputFromQuestion called with:", question);
                // Update both state variables
                setInputText(question);
                setUserQuery(question);
                // Use requestAnimationFrame to ensure DOM updates are complete
                requestAnimationFrame(()=>{
                    const inputElement = document.querySelector('input.w-full.outline-none.p-4.pr-12.bg-transparent');
                    if (inputElement) {
                        // Set the value directly on the input element
                        inputElement.value = question;
                        // Create and dispatch input event
                        const inputEvent = new Event('input', {
                            bubbles: true
                        });
                        inputElement.dispatchEvent(inputEvent);
                        // Create and dispatch change event
                        const changeEvent = new Event('change', {
                            bubbles: true
                        });
                        inputElement.dispatchEvent(changeEvent);
                        // Focus the input
                        inputElement.focus();
                        // Set cursor to end of text
                        inputElement.setSelectionRange(question.length, question.length);
                        console.log("✅ ChatBox: Input element updated and focused");
                    } else {
                        console.warn("❌ ChatBox: Could not find input element!");
                    }
                });
            }
        }));
    // Available languages for voice input with their language codes - English, Tamil, Telugu, and Kannada
    const languages = [
        {
            name: "English",
            code: "en-US",
            color: "blue"
        },
        {
            name: "Tamil",
            code: "ta-IN",
            color: "purple"
        },
        {
            name: "Telugu",
            code: "te-IN",
            color: "green"
        },
        {
            name: "Kannada",
            code: "kn-IN",
            color: "orange"
        }
    ];
    // Get the language code for the selected language
    const getLanguageCode = ()=>{
        const language = languages.find((lang)=>lang.name === selectedLanguage);
        return language ? language.code : "en-US"; // Default to English if not found
    };
    // Initialize with default values for server-side rendering
    const [transcript, setTranscript] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("");
    const [listening, setListening] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [browserSupportsSpeechRecognition, setBrowserSupportsSpeechRecognition] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isMicrophoneAvailable, setIsMicrophoneAvailable] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Speech recognition setup - only run on client side
    const { transcript: clientTranscript, listening: clientListening, resetTranscript, browserSupportsSpeechRecognition: clientBrowserSupport, isMicrophoneAvailable: clientMicrophoneAvailable } = ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : {
        transcript: "",
        listening: false,
        resetTranscript: ()=>{},
        browserSupportsSpeechRecognition: false,
        isMicrophoneAvailable: false
    };
    // Handle click outside to close dropdowns
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        function handleClickOutside(event) {
            // Close suggestions dropdown if clicked outside
            if (suggestionsRef.current && !suggestionsRef.current.contains(event.target)) {
                setShowSuggestions(false);
            }
            // Close language menu if clicked outside
            if (languageMenuRef.current && !languageMenuRef.current.contains(event.target)) {
                setShowLanguageMenu(false);
            }
            // Close index selector if clicked outside
            if (indexSelectorRef.current && !indexSelectorRef.current.contains(event.target)) {
                setShowIndexSelector(false);
            }
        }
        document.addEventListener("mousedown", handleClickOutside);
        return ()=>{
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, []);
    // Auto-scroll transcript to bottom when it gets too long
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (transcriptRef.current && transcript) {
            transcriptRef.current.scrollTop = transcriptRef.current.scrollHeight;
        }
    }, [
        transcript
    ]);
    // Update word count when transcript changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (transcript) {
            const words = transcript.trim().split(/\s+/).filter((word)=>word !== "");
            setWordCount(words.length);
            // Track most recent words for animation
            if (words.length > 0) {
                const lastWord = words[words.length - 1];
                if (lastWord && lastWord.length > 0) {
                    setRecentWords((prev)=>{
                        const newWords = [
                            ...prev,
                            lastWord
                        ];
                        return newWords.slice(-5); // Keep only the last 5 words
                    });
                }
            }
            // Set speaking state when new words are detected
            if (isListening) {
                setSpeaking(true);
                // Clear previous timer if it exists
                if (speakingTimerRef.current) {
                    clearTimeout(speakingTimerRef.current);
                }
                // Set a timer to detect when speaking has paused
                speakingTimerRef.current = setTimeout(()=>{
                    setSpeaking(false);
                }, 1500); // 1.5 seconds of silence is considered a pause
            }
        }
    }, [
        transcript,
        isListening
    ]);
    // Update input text when transcript changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Always update with the latest transcript when listening
        if (listening || isListening) {
            setInputText(transcript);
            setUserQuery(transcript);
            // Also update the edited transcript to match the current transcript
            // This ensures that when we switch to edit mode, we have the latest transcript
            setEditedTranscript(transcript);
            console.log("Speech recognized:", transcript);
            if (transcript && !isListening) {
                console.log("Got transcript but isListening was false. Fixing state...");
                setIsListening(true);
            }
        }
    }, [
        transcript,
        listening,
        isListening,
        setUserQuery
    ]);
    // Separate effect to handle listening state changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (isListening) {
            if (inputText !== "") {
                setInputText("");
                setUserQuery("");
            }
        } else {
            console.log("Stopped listening, transcript:", transcript);
            if (transcript && transcript.trim() !== "") {
                setInputText(transcript);
                setUserQuery(transcript);
            } else if ("TURBOPACK compile-time falsy", 0) {
                "TURBOPACK unreachable";
            }
            // Reset speaking state and clear any timers
            setSpeaking(false);
            if (speakingTimerRef.current) {
                clearTimeout(speakingTimerRef.current);
                speakingTimerRef.current = null;
            }
        // No longer need to close language dropdown
        // setShowLanguageDropdown(false);
        }
        console.log("Listening state changed:", isListening);
    }, [
        isListening,
        transcript,
        inputText
    ]);
    // Update listening state when speech recognition status changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if ("TURBOPACK compile-time truthy", 1) return; // Skip on server-side
        "TURBOPACK unreachable";
    }, [
        listening,
        isListening
    ]);
    // Effect to handle language changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if ("TURBOPACK compile-time truthy", 1) return; // Skip on server-side
        "TURBOPACK unreachable";
    }, [
        selectedLanguage
    ]);
    // Effect to hide index confirmation after 5 seconds
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        let timer;
        if (showIndexConfirmation) {
            timer = setTimeout(()=>{
                setShowIndexConfirmation(false);
            }, 5000);
        }
        return ()=>{
            if (timer) clearTimeout(timer);
        };
    }, [
        showIndexConfirmation
    ]);
    // Sample recommended suggestions
    const englishSuggestions = [
        "How does the new tax regime (post-2023) compare with the old tax regime in terms of benefits for salaried individuals?",
        "How has the rise of UPI (Unified Payments Interface) transformed retail banking and cashless transactions in India?",
        "What factors should a retail investor in India consider before investing in IPOs?",
        "How effective has the Pradhan Mantri Jan Dhan Yojana (PMJDY) been in achieving financial inclusion in rural India?",
        "How are fintech startups like Zerodha and Groww changing the investment landscape for young Indians?"
    ];
    // Tamil financial suggestions
    const tamilSuggestions = [
        "இந்தியாவில் டிஜிட்டல் வாலட் மற்றும் மொபைல் பேமெண்ட் பயன்பாடுகள் நிதி சேவைகளை அணுகுவதை எவ்வாறு மாற்றியுள்ளன?",
        "நீண்ட கால ஓய்வூதிய திட்டங்களில் முதலீடு செய்வதற்கான சிறந்த வழிகள் என்ன மற்றும் அவற்றின் வரி நன்மைகள் என்ன?",
        "சிறு மற்றும் நடுத்தர தொழில்களுக்கு (SMEs) இந்தியாவில் கிடைக்கும் நிதி ஆதரவு திட்டங்கள் என்னென்ன?",
        "பங்குச் சந்தை முதலீட்டிற்கும் தங்கம் மற்றும் நிலம் போன்ற பாரம்பரிய முதலீடுகளுக்கும் இடையே உள்ள முக்கிய வேறுபாடுகள் என்ன?",
        "இந்தியாவில் கிரிப்டோகரன்சி மற்றும் டிஜிட்டல் சொத்துக்களுக்கான தற்போதைய ஒழுங்குமுறை நிலைப்பாடு என்ன?"
    ];
    // Telugu financial suggestions
    const teluguSuggestions = [
        "భారతదేశంలో మ్యూచువల్ ఫండ్స్ లో పెట్టుబడి పెట్టడానికి ఉత్తమ వ్యూహాలు ఏమిటి మరియు వాటి ప్రయోజనాలు ఏమిటి?",
        "వ్యక్తిగత ఆర్థిక ప్రణాళిక కోసం డిజిటల్ టూల్స్ మరియు యాప్‌లు ఎలా ఉపయోగపడతాయి?",
        "భారతదేశంలో స్టార్టప్‌లకు వెంచర్ క్యాపిటల్ మరియు ఏంజెల్ ఇన్వెస్టర్‌ల నుండి నిధులు సేకరించడం ఎలా?",
        "రియల్ ఎస్టేట్ పెట్టుబడులకు REIT (రియల్ ఎస్టేట్ ఇన్వెస్ట్‌మెంట్ ట్రస్ట్‌లు) ఎలా ప్రత్యామ్నాయంగా పనిచేస్తాయి?",
        "భారతదేశంలో ఫినాన్షియల్ లిటరసీని మెరుగుపరచడానికి ప్రభుత్వం మరియు ప్రైవేట్ రంగం తీసుకుంటున్న చర్యలు ఏమిటి?"
    ];
    // Kannada financial suggestions
    const kannadaSuggestions = [
        "ಭಾರತದಲ್ಲಿ ಸಣ್ಣ ಉಳಿತಾಯ ಯೋಜನೆಗಳು ಮತ್ತು ಸರ್ಕಾರಿ ಬಾಂಡ್‌ಗಳಲ್ಲಿ ಹೂಡಿಕೆ ಮಾಡುವುದರ ಪ್ರಯೋಜನಗಳು ಯಾವುವು?",
        "ಹಣದುಬ್ಬರದ ಸಮಯದಲ್ಲಿ ಹಣಕಾಸು ಸ್ಥಿರತೆಯನ್ನು ಕಾಪಾಡಿಕೊಳ್ಳಲು ಉತ್ತಮ ಆರ್ಥಿಕ ತಂತ್ರಗಳು ಯಾವುವು?",
        "ಭಾರತದಲ್ಲಿ ಸ್ವಯಂ ಉದ್ಯೋಗಿಗಳು ಮತ್ತು ಫ್ರೀಲ್ಯಾನ್ಸರ್‌ಗಳಿಗೆ ಲಭ್ಯವಿರುವ ತೆರಿಗೆ ಯೋಜನೆ ಮತ್ತು ಹಣಕಾಸು ಸಾಧನಗಳು ಯಾವುವು?",
        "ಭಾರತದಲ್ಲಿ ಹೊಸ ಪೆನ್ಷನ್ ಯೋಜನೆ (NPS) ಮತ್ತು ಅಟಲ್ ಪೆನ್ಷನ್ ಯೋಜನೆ (APY) ನಡುವಿನ ವ್ಯತ್ಯಾಸಗಳು ಯಾವುವು?",
        "ಭಾರತದಲ್ಲಿ ಮಹಿಳಾ ಉದ್ಯಮಿಗಳಿಗೆ ಲಭ್ಯವಿರುವ ವಿಶೇಷ ಹಣಕಾಸು ಯೋಜನೆಗಳು ಮತ್ತು ಸಾಲ ಕಾರ್ಯಕ್ರಮಗಳು ಯಾವುವು?"
    ];
    // Get suggestions based on selected language
    const getSuggestionsByLanguage = ()=>{
        switch(selectedLanguage){
            case "Tamil":
                return tamilSuggestions;
            case "Telugu":
                return teluguSuggestions;
            case "Kannada":
                return kannadaSuggestions;
            default:
                return englishSuggestions;
        }
    };
    const recommendedSuggestions = getSuggestionsByLanguage();
    // Extract chat ID from URL
    const chatIdUrl = path && path.includes("/chat/") ? path.split("/chat/")[1] : "";
    // Handle selecting a suggestion
    const handleSelectSuggestion = (suggestion)=>{
        setInputText(suggestion);
        setUserQuery(suggestion);
        setShowSuggestions(false);
    };
    // Function to get language-specific text for uploaded content display
    const getUploadDisplayText = ()=>{
        switch(selectedLanguage){
            case "Tamil":
                return {
                    uploadedFiles: 'பதிவேற்றப்பட்ட கோப்புகள்',
                    uploadedUrls: 'பதிவேற்றப்பட்ட இணைப்புகள்',
                    removeFile: 'கோப்பை அகற்று',
                    removeUrl: 'இணைப்பை அகற்று',
                    pdfDocument: 'PDF ஆவணம்',
                    mp3Audio: 'MP3 ஆடியோ',
                    youtubeVideo: 'YouTube வீடியோ',
                    articleLink: 'கட்டுரை இணைப்பு'
                };
            case "Telugu":
                return {
                    uploadedFiles: 'అప్‌లోడ్ చేసిన ఫైల్‌లు',
                    uploadedUrls: 'అప్‌లోడ్ చేసిన లింక్‌లు',
                    removeFile: 'ఫైల్‌ను తొలగించండి',
                    removeUrl: 'లింక్‌ను తొలగించండి',
                    pdfDocument: 'PDF డాక్యుమెంట్',
                    mp3Audio: 'MP3 ఆడియో',
                    youtubeVideo: 'YouTube వీడియో',
                    articleLink: 'వ్యాసం లింక్'
                };
            case "Kannada":
                return {
                    uploadedFiles: 'ಅಪ್‌ಲೋಡ್ ಮಾಡಿದ ಫೈಲ್‌ಗಳು',
                    uploadedUrls: 'ಅಪ್‌ಲೋಡ್ ಮಾಡಿದ ಲಿಂಕ್‌ಗಳು',
                    removeFile: 'ಫೈಲ್ ತೆಗೆದುಹಾಕಿ',
                    removeUrl: 'ಲಿಂಕ್ ತೆಗೆದುಹಾಕಿ',
                    pdfDocument: 'PDF ಡಾಕ್ಯುಮೆಂಟ್',
                    mp3Audio: 'MP3 ಆಡಿಯೋ',
                    youtubeVideo: 'YouTube ವೀಡಿಯೊ',
                    articleLink: 'ಲೇಖನ ಲಿಂಕ್'
                };
            default:
                return {
                    uploadedFiles: 'Uploaded Files',
                    uploadedUrls: 'Uploaded URLs',
                    removeFile: 'Remove file',
                    removeUrl: 'Remove URL',
                    pdfDocument: 'PDF Document',
                    mp3Audio: 'MP3 Audio',
                    youtubeVideo: 'YouTube Video',
                    articleLink: 'Article Link'
                };
        }
    };
    // Function to get file icon based on file type
    const getFileIcon = (fileName)=>{
        const extension = fileName.split('.').pop()?.toLowerCase();
        // Document files
        if ([
            'pdf',
            'doc',
            'docx',
            'txt',
            'rtf',
            'odt'
        ].includes(extension || '')) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiFileText"], {
                className: "w-5 h-5"
            }, void 0, false, {
                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                lineNumber: 530,
                columnNumber: 14
            }, this);
        }
        // Audio files
        if ([
            'mp3',
            'wav',
            'm4a',
            'aac',
            'flac',
            'ogg',
            'wma'
        ].includes(extension || '')) {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiMusicNote"], {
                className: "w-5 h-5"
            }, void 0, false, {
                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                lineNumber: 535,
                columnNumber: 14
            }, this);
        }
        // Default file icon
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiFile"], {
            className: "w-5 h-5"
        }, void 0, false, {
            fileName: "[project]/components/chatComponents/ChatBox.tsx",
            lineNumber: 539,
            columnNumber: 12
        }, this);
    };
    // Function to get URL icon based on type
    const getUrlIcon = (type)=>{
        if (type === 'youtube') {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiYoutubeLogo"], {
                className: "w-5 h-5"
            }, void 0, false, {
                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                lineNumber: 545,
                columnNumber: 14
            }, this);
        }
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiLink"], {
            className: "w-5 h-5"
        }, void 0, false, {
            fileName: "[project]/components/chatComponents/ChatBox.tsx",
            lineNumber: 547,
            columnNumber: 12
        }, this);
    };
    // Function to remove uploaded file
    const removeUploadedFile = (index)=>{
        setUploadedFiles((prev)=>prev.filter((_, i)=>i !== index));
        if (uploadedFiles.length === 1 && uploadedURLs.length === 0) {
            setShowUploadedContent(false);
        }
    };
    // Function to remove uploaded URL
    const removeUploadedURL = (index)=>{
        setUploadedURLs((prev)=>prev.filter((_, i)=>i !== index));
        if (uploadedURLs.length === 1 && uploadedFiles.length === 0) {
            setShowUploadedContent(false);
        }
    };
    // Handle selecting a language for voice input and UI
    const handleSelectLanguage = async (e, language)=>{
        // Prevent the event from bubbling up and triggering form submission
        e.preventDefault();
        e.stopPropagation();
        setSelectedLanguage(language);
        // Close language menu when a language is selected
        setShowLanguageMenu(false);
        // Clear any language error when the user changes the language
        if (languageError) {
            setLanguageError(null);
        }
        // Update placeholder and UI based on language
        console.log(`Language set to: ${language}`);
        // Notify parent component about language change if callback is provided
        if (onLanguageChange) {
            onLanguageChange(language);
        }
        // If suggestions are showing, close and reopen to refresh the content
        if (showSuggestions) {
            setShowSuggestions(false);
            setTimeout(()=>setShowSuggestions(true), 100);
        }
        // If currently listening, restart with new language
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    };
    // Toggle voice recognition
    const toggleListening = async ()=>{
        // Skip on server-side
        if ("TURBOPACK compile-time truthy", 1) {
            console.warn("Attempted to toggle listening on server-side");
            return;
        }
        "TURBOPACK unreachable";
    };
    // Toggle between edit and view modes for the transcript
    const toggleTranscriptEditMode = ()=>{
        if (isEditingTranscript) {
            // Save the edited transcript
            if (editedTranscript.trim() !== "") {
                setInputText(editedTranscript);
                setUserQuery(editedTranscript);
            }
            setIsEditingTranscript(false);
        } else {
            // Enter edit mode
            setIsEditingTranscript(true);
            // Focus the editable textarea after a short delay to ensure it's rendered
            setTimeout(()=>{
                if (editableTranscriptRef.current) {
                    editableTranscriptRef.current.focus();
                }
            }, 50);
        }
    };
    // Handle changes to the editable transcript
    const handleTranscriptChange = (e)=>{
        setEditedTranscript(e.target.value);
        // Update word count
        const words = e.target.value.trim().split(/\s+/).filter((word)=>word !== "");
        setWordCount(words.length);
        // Clear any language error when the user edits the transcript
        if (languageError) {
            setLanguageError(null);
        }
    };
    // Start listening with the selected language
    const startListening = async ()=>{
        // Skip on server-side
        if ("TURBOPACK compile-time truthy", 1) {
            console.warn("Attempted to start listening on server-side");
            return;
        }
        "TURBOPACK unreachable";
        const languageCode = undefined;
    };
    const handleSendMessage = async (e)=>{
        e.preventDefault();
        // If we're in edit mode, use the edited transcript
        // Otherwise, use the transcript if listening, or the input text
        const textToSend = isEditingTranscript ? editedTranscript : isListening ? transcript : inputText;
        if (!textToSend || !textToSend.trim()) {
            return;
        }
        // Validate that the input text matches the selected language
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$chatComponents$2f$services$2f$ValidationService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationService"].validateLanguageMatch(textToSend, selectedLanguage)) {
            setLanguageError("Please select the proper language or type in the currently selected language.");
            return;
        }
        // Clear any previous language errors
        setLanguageError(null);
        // If we're in edit mode, exit edit mode
        if (isEditingTranscript) {
            setIsEditingTranscript(false);
        }
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        // Close any open dropdowns/popups
        setShowLanguageMenu(false);
        setShowSuggestions(false);
        // Disable language buttons during query processing
        setLanguageButtonsDisabled(true);
        // Mark that user has made their first request and persist it
        setHasUserMadeFirstRequest(true);
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        setIsLoading(true);
        const currentChatId = chatIdUrl || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$v4$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])();
        if (!chatIdUrl) {
            router.push(`/chat/${currentChatId}`);
        }
        const queryText = textToSend.trim();
        // Determine the language of the query
        const isTamil = __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$chatComponents$2f$services$2f$ValidationService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationService"].isTamilText(queryText) || selectedLanguage === "Tamil";
        const isTelugu = __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$chatComponents$2f$services$2f$ValidationService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationService"].isTeluguText(queryText) || selectedLanguage === "Telugu";
        const isKannada = __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$chatComponents$2f$services$2f$ValidationService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationService"].isKannadaText(queryText) || selectedLanguage === "Kannada";
        // Log language detection for Tamil support
        if (isTamil) {
            console.log(`🌏 Tamil language detected - Selected: ${selectedLanguage}, Text detection: ${__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$chatComponents$2f$services$2f$ValidationService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ValidationService"].isTamilText(queryText)}`);
        }
        // Store the original query for display purposes (commented out as it's not currently used)
        // const originalQuery = queryText;
        let translatedQuery = queryText;
        let needsTranslation = false;
        // Use the financial_query endpoint for all languages
        // For Tamil, Telugu, and Kannada, we'll translate the query and response
        // Extract continuous capital English words that should not be translated
        const capitalWordsMatches = queryText.match(/\b[A-Z]{2,}\b/g) || [];
        const capitalWords = capitalWordsMatches.map((word)=>({
                word,
                placeholder: `__CAPITAL_WORD_${Math.random().toString(36).substring(2, 10)}__`
            }));
        // Create a version of the query with placeholders for capital words
        let queryWithPlaceholders = queryText;
        capitalWords.forEach((item)=>{
            queryWithPlaceholders = queryWithPlaceholders.replace(item.word, item.placeholder);
        });
        if (isTamil) {
            // For Tamil, we need to translate the query to English before sending
            needsTranslation = true;
            // Translate Tamil to English
            try {
                console.log(`Translating Tamil query to English (preserving capital words): "${queryWithPlaceholders}"`);
                translatedQuery = await __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$chatComponents$2f$services$2f$TranslationService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TranslationService"].translateText(queryWithPlaceholders, "ta", "en");
                // After translation, restore the capital words
                capitalWords.forEach((item)=>{
                    translatedQuery = translatedQuery.replace(item.placeholder, item.word);
                });
            } catch (error) {
                console.error("Error translating Tamil query:", error);
            }
        } else if (isTelugu) {
            // For Telugu, we need to translate the query to English before sending
            needsTranslation = true;
            // Translate Telugu to English
            try {
                console.log(`Translating Telugu query to English (preserving capital words): "${queryWithPlaceholders}"`);
                translatedQuery = await __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$chatComponents$2f$services$2f$TranslationService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TranslationService"].translateText(queryWithPlaceholders, "te", "en");
                // After translation, restore the capital words
                capitalWords.forEach((item)=>{
                    translatedQuery = translatedQuery.replace(item.placeholder, item.word);
                });
            } catch (error) {
                console.error("Error translating Telugu query:", error);
            }
        } else if (isKannada) {
            // For Kannada, we need to translate the query to English before sending
            needsTranslation = true;
            // Translate Kannada to English
            try {
                console.log(`Translating Kannada query to English (preserving capital words): "${queryWithPlaceholders}"`);
                translatedQuery = await __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$chatComponents$2f$services$2f$TranslationService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TranslationService"].translateText(queryWithPlaceholders, "kn", "en");
                // After translation, restore the capital words
                capitalWords.forEach((item)=>{
                    translatedQuery = translatedQuery.replace(item.placeholder, item.word);
                });
            } catch (error) {
                console.error("Error translating Kannada query:", error);
            }
        }
        console.log(`Query detected as ${isTamil ? 'Tamil' : isTelugu ? 'Telugu' : isKannada ? 'Kannada' : 'English'}`);
        setInputText("");
        setUserQuery("");
        resetTranscript();
        setWordCount(0);
        setRecentWords([]);
        // Note: Uploaded content is intentionally NOT cleared here to allow users to send multiple messages
        // with the same uploaded files/URLs. Content will only be cleared when explicitly removed by user
        // or when starting a new chat session.
        const userMessageTimestamp = new Date().toISOString();
        // Convert uploaded files to the format expected by the Message interface
        const messageUploadedFiles = uploadedFiles.map((file)=>({
                name: file.name,
                size: file.size,
                type: file.type,
                lastModified: file.lastModified
            }));
        addMessage({
            isUser: true,
            text: queryText,
            timestamp: userMessageTimestamp,
            uploadedFiles: messageUploadedFiles.length > 0 ? messageUploadedFiles : undefined,
            uploadedURLs: uploadedURLs.length > 0 ? uploadedURLs : undefined
        }, currentChatId);
        const loadingMessageTimestamp = new Date().toISOString();
        const loadingMessageId = `loading-${currentChatId}-${Date.now()}`;
        addMessage({
            isUser: false,
            text: "__LOADING__",
            timestamp: loadingMessageTimestamp,
            messageId: loadingMessageId
        }, currentChatId);
        try {
            // Use the translated query for Telugu, otherwise use the original query
            const queryToSend = needsTranslation ? translatedQuery : queryText;
            // Get FAISS configuration from localStorage if available
            const faissIndexName = ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : null;
            const faissEmbedModel = ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : null;
            const faissClientEmail = ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : null;
            // Prepare request body with all available data including language preference
            const requestBody = {
                query: queryToSend,
                language: selectedLanguage // Add language preference for Tamil support
            };
            // Add client email if available
            if (userEmail) {
                requestBody.client_email = userEmail;
                requestBody.user_email = userEmail; // Also add for direct FAISS query validation
                console.log(`Including user email in request: ${userEmail}`);
            }
            // Determine which index to use for FAISS
            const indexToUse = selectedIndex || faissIndexName;
            console.log(`🎯 Selected index from UI: ${selectedIndex}`);
            console.log(`💾 Stored index from localStorage: ${faissIndexName}`);
            console.log(`📌 Final index to use: ${indexToUse}`);
            // Add context about uploaded content if any
            if (uploadedFiles.length > 0 || uploadedURLs.length > 0) {
                const uploadContext = [];
                if (uploadedFiles.length > 0) {
                    uploadContext.push(`Recently uploaded files: ${uploadedFiles.map((f)=>f.name).join(', ')}`);
                }
                if (uploadedURLs.length > 0) {
                    uploadContext.push(`Recently processed URLs: ${uploadedURLs.map((u)=>u.url).join(', ')}`);
                }
                requestBody.upload_context = uploadContext.join('. ');
                requestBody.has_recent_uploads = true;
                console.log(`📎 Including upload context: ${requestBody.upload_context}`);
            }
            // For FAISS, we don't need API keys, just the index name
            if (indexToUse) {
                requestBody.index_name = indexToUse;
                console.log(`✅ SENDING REQUEST WITH - Selected FAISS Index: "${indexToUse}"`);
            } else {
                requestBody.index_name = 'default';
                console.log(`🔁 FALLING BACK TO DEFAULT INDEX - Using: "default"`);
                console.log(`💡 Note: Default index is accessible to all users`);
            }
            // Detect query language for better caching and translation
            const queryLanguage = __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$chatComponents$2f$services$2f$TranslationService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TranslationService"].detectLanguage(queryToSend);
            const targetLanguage = selectedLanguage === 'Tamil' ? 'ta' : selectedLanguage === 'Telugu' ? 'te' : selectedLanguage === 'Kannada' ? 'kn' : 'en';
            // 🚀 CACHE-FIRST APPROACH: Check cache before making API call with language support
            const cacheContext = `${requestBody.index_name || 'default'}|${userEmail || 'anonymous'}|${requestBody.upload_context || ''}`;
            const cachedResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$chatComponents$2f$services$2f$CacheService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CacheService"].getCachedResponseWithDelay(queryToSend, cacheContext, targetLanguage);
            let data;
            if (cachedResponse) {
                // Use cached response (delay already applied by getCachedResponseWithDelay)
                console.log(`⚡ Using cached response for query: "${queryToSend.substring(0, 50)}..." (Language: ${targetLanguage})`);
                data = {
                    ai_response: cachedResponse.ai_response,
                    related_questions: cachedResponse.related_questions,
                    sentence_analysis: cachedResponse.sentence_analysis,
                    pinecone_indexes: cachedResponse.pinecone_indexes,
                    faiss_categories: cachedResponse.faiss_categories,
                    has_uploaded_content: cachedResponse.has_uploaded_content,
                    upload_sources: cachedResponse.upload_sources,
                    translation_applied: cachedResponse.translation_applied,
                    query_language: cachedResponse.query_language
                };
                console.log(`✅ Cached response ready with language support`);
            } else {
                // No cache hit - make API call
                console.log(`🌐 Making API call for query: "${queryToSend.substring(0, 50)}..." (Language: ${selectedLanguage})`);
                // Add translation parameters to request if needed
                if (targetLanguage !== 'en') {
                    requestBody.target_language = targetLanguage;
                    requestBody.enable_translation = true;
                }
                data = await __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$chatComponents$2f$services$2f$ApiService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiService"].sendQuery(requestBody);
                // Apply client-side translation if backend translation wasn't applied
                if (!data.translation_applied && targetLanguage !== 'en') {
                    console.log(`🌐 Applying client-side translation to ${targetLanguage}`);
                    data = await __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$chatComponents$2f$services$2f$TranslationService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TranslationService"].translateResponse(data, targetLanguage);
                }
                // Cache the response for future use with language context
                __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$chatComponents$2f$services$2f$CacheService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CacheService"].setCachedResponse(queryToSend, data, cacheContext, targetLanguage);
            }
            console.log("API Response received:", data);
            // Explicitly check for related_questions
            if (data.related_questions) {
                console.log("Found related_questions in API response:", data.related_questions);
            } else {
                console.warn("No related_questions found in API response");
            }
            // Check if the response includes a list of FAISS indexes/categories
            if (data.faiss_categories && Array.isArray(data.faiss_categories) && data.faiss_categories.length > 0) {
                console.log("Received FAISS categories from API:", data.faiss_categories);
                // Update the dropdown options with the available categories
                setPineconeIndexes(data.faiss_categories.map((cat)=>cat.index_name || cat));
            }
            let aiResponse = data.ai_response;
            console.log("Original AI Response:", aiResponse);
            // For Tamil, Telugu, or Kannada, translate the response from English
            if ((isTamil || isTelugu || isKannada) && aiResponse) {
                try {
                    // Extract continuous capital English words that should not be translated
                    const responseCapitalWordsMatches = aiResponse.match(/\b[A-Z]{2,}\b/g) || [];
                    const responseCapitalWords = responseCapitalWordsMatches.map((word)=>({
                            word,
                            placeholder: `__CAPITAL_WORD_${Math.random().toString(36).substring(2, 10)}__`
                        }));
                    // Create a version of the response with placeholders for capital words
                    let responseWithPlaceholders = aiResponse;
                    responseCapitalWords.forEach((item)=>{
                        responseWithPlaceholders = responseWithPlaceholders.replace(item.word, item.placeholder);
                    });
                    // Translate the response based on the detected language
                    if (isTamil) {
                        console.log(`Translating response from English to Tamil (preserving capital words): "${responseWithPlaceholders.substring(0, 50)}..."`);
                        const translatedResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$chatComponents$2f$services$2f$TranslationService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TranslationService"].translateText(responseWithPlaceholders, "en", "ta");
                        aiResponse = translatedResponse;
                        // After translation, restore the capital words
                        responseCapitalWords.forEach((item)=>{
                            aiResponse = aiResponse.replace(item.placeholder, item.word);
                        });
                    } else if (isTelugu) {
                        console.log(`Translating response from English to Telugu (preserving capital words): "${responseWithPlaceholders.substring(0, 50)}..."`);
                        const translatedResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$chatComponents$2f$services$2f$TranslationService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TranslationService"].translateText(responseWithPlaceholders, "en", "te");
                        aiResponse = translatedResponse;
                        // After translation, restore the capital words
                        responseCapitalWords.forEach((item)=>{
                            aiResponse = aiResponse.replace(item.placeholder, item.word);
                        });
                    } else if (isKannada) {
                        console.log(`Translating response from English to Kannada (preserving capital words): "${responseWithPlaceholders.substring(0, 50)}..."`);
                        const translatedResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$chatComponents$2f$services$2f$TranslationService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TranslationService"].translateText(responseWithPlaceholders, "en", "kn");
                        aiResponse = translatedResponse;
                        // After translation, restore the capital words
                        responseCapitalWords.forEach((item)=>{
                            aiResponse = aiResponse.replace(item.placeholder, item.word);
                        });
                    }
                } catch (error) {
                    console.error(`Error translating response to ${isTamil ? 'Tamil' : isTelugu ? 'Telugu' : 'Kannada'}:`, error);
                }
            }
            console.log("AI Response to display:", aiResponse);
            // Update response data sources state
            const extendedData = data;
            if (extendedData.has_uploaded_content !== undefined) {
                setResponseHasUploadedContent(extendedData.has_uploaded_content);
            }
            if (extendedData.upload_sources && Array.isArray(extendedData.upload_sources)) {
                setResponseUploadSources(extendedData.upload_sources);
            }
            if (data.sentence_analysis && Array.isArray(data.sentence_analysis)) {
                console.log("Sentence analysis data:", data.sentence_analysis);
                // For Tamil, Telugu, or Kannada, we might want to translate the sentence analysis too
                if (isTamil || isTelugu || isKannada) {
                    // In a real app, you would translate each sentence and summary
                    // This is just a placeholder for the actual implementation
                    console.log(`Would translate sentence analysis for ${isTamil ? 'Tamil' : isTelugu ? 'Telugu' : 'Kannada'} here`);
                // Example of how you might translate each item in a real implementation:
                // for (let i = 0; i < data.sentence_analysis.length; i++) {
                //   const item = data.sentence_analysis[i];
                //   if (item.summary) {
                //     item.summary = await translateText(item.summary, "en", isTamil ? "ta" : "te");
                //   }
                //   if (item.sentence) {
                //     item.sentence = await translateText(item.sentence, "en", isTamil ? "ta" : "te");
                //   }
                // }
                }
                data.sentence_analysis.forEach((item, index)=>{
                    const sentence = item.sentence;
                    const url = item.url;
                    const summary = item.summary || "";
                    console.log(`Sentence ${index + 1}: ${sentence}`);
                    console.log(`URL ${index + 1}: ${url}`);
                    console.log(`Summary ${index + 1}: ${summary}`);
                });
            } else {
                console.log("No sentence_analysis data available.");
            }
            // Log and translate related questions if available
            if (data.related_questions && Array.isArray(data.related_questions)) {
                console.log("Related questions data:", data.related_questions);
                // For Tamil, Telugu, or Kannada, translate the related questions
                if (isTamil || isTelugu || isKannada) {
                    console.log(`Translating related questions for ${isTamil ? 'Tamil' : isTelugu ? 'Telugu' : 'Kannada'}`);
                    try {
                        // Translate each related question
                        const translatedQuestions = await Promise.all(data.related_questions.map(async (question)=>{
                            // Extract continuous capital English words that should not be translated
                            const questionCapitalWordsMatches = question.match(/\b[A-Z]{2,}\b/g) || [];
                            const questionCapitalWords = questionCapitalWordsMatches.map((word)=>({
                                    word,
                                    placeholder: `__CAPITAL_WORD_${Math.random().toString(36).substring(2, 10)}__`
                                }));
                            // Create a version of the question with placeholders for capital words
                            let questionWithPlaceholders = question;
                            questionCapitalWords.forEach((item)=>{
                                questionWithPlaceholders = questionWithPlaceholders.replace(item.word, item.placeholder);
                            });
                            let translatedQuestion = question;
                            // Translate the question based on the detected language
                            if (isTamil) {
                                translatedQuestion = await __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$chatComponents$2f$services$2f$TranslationService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TranslationService"].translateText(questionWithPlaceholders, "en", "ta");
                            } else if (isTelugu) {
                                translatedQuestion = await __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$chatComponents$2f$services$2f$TranslationService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TranslationService"].translateText(questionWithPlaceholders, "en", "te");
                            } else if (isKannada) {
                                translatedQuestion = await __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$chatComponents$2f$services$2f$TranslationService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TranslationService"].translateText(questionWithPlaceholders, "en", "kn");
                            }
                            // After translation, restore the capital words
                            questionCapitalWords.forEach((item)=>{
                                translatedQuestion = translatedQuestion.replace(item.placeholder, item.word);
                            });
                            return translatedQuestion;
                        }));
                        // Update the data with translated questions
                        data.related_questions = translatedQuestions;
                        console.log(`✅ Successfully translated ${translatedQuestions.length} related questions to ${isTamil ? 'Tamil' : isTelugu ? 'Telugu' : 'Kannada'}`);
                    } catch (error) {
                        console.error(`❌ Error translating related questions to ${isTamil ? 'Tamil' : isTelugu ? 'Telugu' : 'Kannada'}:`, error);
                    // Keep original questions if translation fails
                    }
                }
                data.related_questions.forEach((question, index)=>{
                    console.log(`Related Question ${index + 1}: ${question}`);
                });
            } else {
                console.log("No related_questions data available.");
            }
            if (aiResponse === undefined || aiResponse === null) {
                console.warn("API returned null/undefined ai_response");
                handleSubmit(queryText, currentChatId, "Sorry, I couldn't process your request properly.");
                return;
            }
            // Create a properly structured response object with all fields
            const responseObject = {
                ai_response: aiResponse,
                sentence_analysis: data.sentence_analysis || [],
                related_questions: data.related_questions || []
            };
            // Explicitly log the related_questions in the response object
            console.log("Related questions in response object:", responseObject.related_questions);
            console.log("Sending structured response to handleSubmit:", responseObject);
            handleSubmit(queryText, currentChatId, responseObject);
        } catch (error) {
            console.error("Error fetching AI response:", error);
            // Provide a more specific error message if it's related to the FAISS index
            let errorMessage = "I'm sorry, I couldn't process your request at the moment. Please try again later.";
            if (error instanceof Error) {
                const errorText = error.message;
                // Check if the error is related to the FAISS index
                if (errorText.includes("index") || errorText.includes("Index") || errorText.includes("FAISS")) {
                    errorMessage = errorText;
                    console.log("Index-related error detected:", errorText);
                }
            }
            handleSubmit(queryText, currentChatId, errorMessage);
        } finally{
            setIsLoading(false);
            // Re-enable language buttons once the response is received or if there's an error
            setLanguageButtonsDisabled(false);
        }
    };
    // Initialize client-side values after mount to prevent hydration errors
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    }, [
        clientBrowserSupport,
        clientMicrophoneAvailable,
        clientListening,
        clientTranscript
    ]);
    // Get user email from localStorage and set up API environment
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    }, []);
    // Clear uploaded content when navigating to a different chat
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Clear uploaded content when chat ID changes (new chat session)
        setUploadedFiles([]);
        setUploadedURLs([]);
        setShowUploadedContent(false);
        console.log('Cleared uploaded content for new chat session:', chatIdUrl);
    }, [
        chatIdUrl
    ]);
    // Check for browser support and microphone availability
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        // Cleanup function to ensure microphone is stopped when component unmounts
        return ()=>{
            if ("TURBOPACK compile-time falsy", 0) {
                "TURBOPACK unreachable";
            }
            // Clear any timers
            if (speakingTimerRef.current) {
                clearTimeout(speakingTimerRef.current);
                speakingTimerRef.current = null;
            }
        };
    }, [
        browserSupportsSpeechRecognition,
        isMicrophoneAvailable,
        isListening
    ]);
    // Function to log speech recognition state for debugging - uncomment the debug button below to use
    // const logSpeechRecognitionState = () => {
    //   console.group("Speech Recognition Debug Info");
    //   console.log("Browser supports speech recognition:", browserSupportsSpeechRecognition);
    //   console.log("Microphone available:", isMicrophoneAvailable);
    //   console.log("Current listening state (from hook):", listening);
    //   console.log("Current isListening state (component):", isListening);
    //   console.log("Current transcript:", transcript);
    //   console.log("Selected language:", selectedLanguage);
    //   console.log("Language code:", getLanguageCode());
    //   console.groupEnd();
    // };
    // Generate sound wave animation elements
    // Debug log for hasUserMadeFirstRequest state
    console.log('🔍 ChatBox render: hasUserMadeFirstRequest =', hasUserMadeFirstRequest);
    const renderSoundWave = ()=>{
        // Use different colors based on selected language
        let waveColor = "bg-red-500"; // Default color
        if (selectedLanguage === "Tamil") {
            waveColor = "bg-purple-500";
        } else if (selectedLanguage === "Telugu") {
            waveColor = "bg-green-500";
        } else if (selectedLanguage === "Kannada") {
            waveColor = "bg-orange-500";
        }
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center gap-[2px] h-5",
            children: Array.from({
                length: 5
            }).map((_, i)=>{
                const isActive = speaking || i === 2; // Middle bar always active when not speaking
                const delay = i * 0.1;
                const duration = isActive ? 0.5 + i * 0.1 : 1; // Use deterministic duration
                const height = isActive ? i % 2 === 0 ? 10 + i * 2 : 6 + i * 1.5 : i === 2 ? 6 : 4;
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: `w-1 ${waveColor} rounded-full transition-all`,
                    style: {
                        height: `${height}px`,
                        animationName: isActive ? 'soundWavePulse' : 'none',
                        animationDuration: `${duration}s`,
                        animationIterationCount: 'infinite',
                        animationDirection: 'alternate',
                        animationDelay: `${delay}s`,
                        opacity: isActive ? 1 : 0.5
                    }
                }, `wave-${i}`, false, {
                    fileName: "[project]/components/chatComponents/ChatBox.tsx",
                    lineNumber: 1401,
                    columnNumber: 13
                }, this);
            })
        }, void 0, false, {
            fileName: "[project]/components/chatComponents/ChatBox.tsx",
            lineNumber: 1391,
            columnNumber: 7
        }, this);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "jsx-efe5c9d818f3f82d" + " " + "w-full max-w-[1070px] mx-auto px-2 sm:px-4 md:px-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$jsx$2f$style$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                id: "f690196061de6bbd",
                children: "@keyframes fadeIn{0%{opacity:0;transform:translateY(-5px)}to{opacity:1;transform:translateY(0)}}.animate-fadeIn{animation:.3s ease-out forwards fadeIn}"
            }, void 0, false, void 0, this),
            isListening && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "jsx-efe5c9d818f3f82d" + " " + `mb-4 p-3 rounded-lg flex items-center justify-between shadow-sm relative z-[10]
          ${selectedLanguage === "Tamil" ? "bg-gradient-to-r from-purple-50 to-purple-50/70 border border-purple-200" : selectedLanguage === "Telugu" ? "bg-gradient-to-r from-green-50 to-green-50/70 border border-green-200" : selectedLanguage === "Kannada" ? "bg-gradient-to-r from-orange-50 to-orange-50/70 border border-orange-200" : "bg-gradient-to-r from-red-50 to-red-50/70 border border-red-200"}`,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "jsx-efe5c9d818f3f82d" + " " + "flex items-center gap-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "jsx-efe5c9d818f3f82d" + " " + "flex items-center bg-white p-1 rounded-full shadow-sm",
                                children: renderSoundWave()
                            }, void 0, false, {
                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                lineNumber: 1444,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "jsx-efe5c9d818f3f82d",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "jsx-efe5c9d818f3f82d" + " " + `text-sm font-medium ${selectedLanguage === "Tamil" ? "text-purple-700" : selectedLanguage === "Telugu" ? "text-green-700" : selectedLanguage === "Kannada" ? "text-orange-700" : "text-red-700"}`,
                                        children: [
                                            selectedLanguage === "Tamil" ? "குரல் பதிவு செயலில் உள்ளது" : selectedLanguage === "Telugu" ? "వాయిస్ రికార్డింగ్ యాక్టివ్" : selectedLanguage === "Kannada" ? "ಧ್ವನಿ ರೆಕಾರ್ಡಿಂಗ್ ಸಕ್ರಿಯವಾಗಿದೆ" : "Voice recording active in",
                                            " ",
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                className: "jsx-efe5c9d818f3f82d",
                                                children: selectedLanguage
                                            }, void 0, false, {
                                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                lineNumber: 1463,
                                                columnNumber: 54
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                        lineNumber: 1448,
                                        columnNumber: 15
                                    }, this),
                                    wordCount > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-efe5c9d818f3f82d" + " " + `text-xs mt-0.5 ${selectedLanguage === "Tamil" ? "text-purple-500/80" : selectedLanguage === "Telugu" ? "text-green-500/80" : selectedLanguage === "Kannada" ? "text-orange-500/80" : "text-red-500/80"}`,
                                        children: selectedLanguage === "Tamil" ? `இதுவரை ${wordCount} சொற்கள் பதிவு செய்யப்பட்டுள்ளன` : selectedLanguage === "Telugu" ? `ఇప్పటివరకు ${wordCount} పదాలు క్యాప్చర్ చేయబడ్డాయి` : selectedLanguage === "Kannada" ? `ಇಲ್ಲಿಯವರೆಗೆ ${wordCount} ಪದಗಳನ್ನು ಸೆರೆಹಿಡಿಯಲಾಗಿದೆ` : `Captured ${wordCount} ${wordCount === 1 ? 'word' : 'words'} so far`
                                    }, void 0, false, {
                                        fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                        lineNumber: 1466,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                lineNumber: 1447,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/chatComponents/ChatBox.tsx",
                        lineNumber: 1443,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: ()=>{
                            toggleListening();
                            // Additional cleanup to ensure UI is reset
                            setIsListening(false);
                            resetTranscript();
                            setSpeaking(false);
                        },
                        className: "jsx-efe5c9d818f3f82d" + " " + `text-xs px-3 py-1.5 bg-white rounded-full transition-colors shadow-sm hover:shadow flex items-center gap-1.5
              ${selectedLanguage === "Tamil" ? "border border-purple-300 text-purple-600 hover:bg-purple-100" : selectedLanguage === "Telugu" ? "border border-green-300 text-green-600 hover:bg-green-100" : selectedLanguage === "Kannada" ? "border border-orange-300 text-orange-600 hover:bg-orange-100" : "border border-red-300 text-red-600 hover:bg-red-100"}`,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiStop"], {
                                className: selectedLanguage === "Tamil" ? "text-purple-600" : selectedLanguage === "Telugu" ? "text-green-600" : selectedLanguage === "Kannada" ? "text-orange-600" : "text-red-600"
                            }, void 0, false, {
                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                lineNumber: 1503,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "jsx-efe5c9d818f3f82d",
                                children: selectedLanguage === "Tamil" ? "பதிவை மீட்டமை" : selectedLanguage === "Telugu" ? "రీసెట్ రికార్డింగ్" : selectedLanguage === "Kannada" ? "ರಿಸೆಟ್ ರೆಕಾರ್ಡಿಂಗ್" : "Reset Recording"
                            }, void 0, false, {
                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                lineNumber: 1512,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/chatComponents/ChatBox.tsx",
                        lineNumber: 1486,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                lineNumber: 1435,
                columnNumber: 9
            }, this),
            hasUserMadeFirstRequest && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "jsx-efe5c9d818f3f82d" + " " + "w-full flex justify-between items-center mb-2",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        ref: indexSelectorRef,
                        className: "jsx-efe5c9d818f3f82d" + " " + "relative",
                        children: showIndexSelector && !indexesLoading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "jsx-efe5c9d818f3f82d" + " " + "absolute top-full left-0 mt-1 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg z-[50] max-h-60 overflow-y-auto",
                            children: pineconeIndexes.length > 0 ? pineconeIndexes.map((index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>{
                                        setSelectedIndex(index);
                                        setShowIndexSelector(false);
                                        setShowIndexConfirmation(true);
                                        localStorage.setItem('selectedFaissIndex', index);
                                        localStorage.setItem('faiss_index_name', index);
                                        console.log(`Selected index: ${index}`);
                                    },
                                    className: "jsx-efe5c9d818f3f82d" + " " + `w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex items-center justify-between
                        ${selectedIndex === index ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' : 'text-gray-700 dark:text-gray-300'}`,
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "jsx-efe5c9d818f3f82d",
                                            children: index
                                        }, void 0, false, {
                                            fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                            lineNumber: 1566,
                                            columnNumber: 23
                                        }, this),
                                        selectedIndex === index && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                            fill: "currentColor",
                                            viewBox: "0 0 20 20",
                                            className: "jsx-efe5c9d818f3f82d" + " " + "w-4 h-4",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                fillRule: "evenodd",
                                                d: "M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",
                                                clipRule: "evenodd",
                                                className: "jsx-efe5c9d818f3f82d"
                                            }, void 0, false, {
                                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                lineNumber: 1569,
                                                columnNumber: 27
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                            lineNumber: 1568,
                                            columnNumber: 25
                                        }, this)
                                    ]
                                }, index, true, {
                                    fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                    lineNumber: 1553,
                                    columnNumber: 21
                                }, this)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "jsx-efe5c9d818f3f82d" + " " + "px-3 py-2 text-sm text-gray-500 dark:text-gray-400",
                                children: "No indexes available"
                            }, void 0, false, {
                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                lineNumber: 1575,
                                columnNumber: 19
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/components/chatComponents/ChatBox.tsx",
                            lineNumber: 1550,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/components/chatComponents/ChatBox.tsx",
                        lineNumber: 1528,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "jsx-efe5c9d818f3f82d" + " " + "px-2 py-0.5 bg-blue-50 dark:bg-blue-900/20 rounded-md text-xs font-medium text-gray-600 dark:text-gray-400 border border-blue-200 dark:border-blue-800/50 shadow-sm flex items-center gap-1",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "jsx-efe5c9d818f3f82d",
                                children: "Active Category:"
                            }, void 0, false, {
                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                lineNumber: 1585,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "jsx-efe5c9d818f3f82d" + " " + "text-blue-600 dark:text-blue-400 font-semibold",
                                children: selectedIndex || "Default"
                            }, void 0, false, {
                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                lineNumber: 1586,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/chatComponents/ChatBox.tsx",
                        lineNumber: 1584,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                lineNumber: 1526,
                columnNumber: 9
            }, this),
            showIndexConfirmation && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "jsx-efe5c9d818f3f82d" + " " + "mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg animate-fadeIn",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "jsx-efe5c9d818f3f82d" + " " + "flex items-center gap-2 text-green-700 dark:text-green-400",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                            fill: "currentColor",
                            viewBox: "0 0 20 20",
                            className: "jsx-efe5c9d818f3f82d" + " " + "w-5 h-5",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                fillRule: "evenodd",
                                d: "M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",
                                clipRule: "evenodd",
                                className: "jsx-efe5c9d818f3f82d"
                            }, void 0, false, {
                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                lineNumber: 1598,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/components/chatComponents/ChatBox.tsx",
                            lineNumber: 1597,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "jsx-efe5c9d818f3f82d" + " " + "text-sm font-medium",
                            children: [
                                'Index "',
                                selectedIndex,
                                '" selected! Your responses will be filtered based on this index.'
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/chatComponents/ChatBox.tsx",
                            lineNumber: 1600,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/components/chatComponents/ChatBox.tsx",
                    lineNumber: 1596,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                lineNumber: 1595,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                onSubmit: handleSendMessage,
                className: "jsx-efe5c9d818f3f82d" + " " + "w-full bg-primaryColor/5 p-2 sm:p-3 lg:p-4 rounded-xl border border-primaryColor/20",
                children: [
                    languageError && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "jsx-efe5c9d818f3f82d" + " " + "mb-3 p-3 bg-red-50 border border-red-200 rounded-lg text-red-600 text-sm flex items-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                xmlns: "http://www.w3.org/2000/svg",
                                viewBox: "0 0 20 20",
                                fill: "currentColor",
                                className: "jsx-efe5c9d818f3f82d" + " " + "h-5 w-5 mr-2 flex-shrink-0",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                    fillRule: "evenodd",
                                    d: "M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z",
                                    clipRule: "evenodd",
                                    className: "jsx-efe5c9d818f3f82d"
                                }, void 0, false, {
                                    fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                    lineNumber: 1623,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                lineNumber: 1622,
                                columnNumber: 13
                            }, this),
                            languageError
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/chatComponents/ChatBox.tsx",
                        lineNumber: 1621,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "jsx-efe5c9d818f3f82d" + " " + "w-full bg-white rounded-lg max-lg:text-sm block dark:bg-n0 relative z-[5]",
                        children: isListening || transcript ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "jsx-efe5c9d818f3f82d" + " " + "w-full p-4 min-h-[56px] max-h-[200px] overflow-y-auto relative flex flex-col",
                            children: [
                                isEditingTranscript ? // Editable textarea for transcript
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "jsx-efe5c9d818f3f82d" + " " + "flex-grow relative",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                            ref: editableTranscriptRef,
                                            value: editedTranscript,
                                            onChange: handleTranscriptChange,
                                            onKeyDown: (e)=>{
                                                if (e.key === 'Enter' && !e.shiftKey) {
                                                    e.preventDefault();
                                                    // Only submit if there's text to send and not currently loading
                                                    if (editedTranscript.trim() && !isLoading) {
                                                        // Create a synthetic form event to trigger handleSendMessage
                                                        const syntheticEvent = {
                                                            preventDefault: ()=>{},
                                                            stopPropagation: ()=>{},
                                                            nativeEvent: e.nativeEvent,
                                                            target: e.target,
                                                            currentTarget: e.currentTarget,
                                                            bubbles: false,
                                                            cancelable: false,
                                                            defaultPrevented: false,
                                                            eventPhase: 0,
                                                            isTrusted: false,
                                                            timeStamp: Date.now(),
                                                            type: 'submit',
                                                            isDefaultPrevented: ()=>false,
                                                            isPropagationStopped: ()=>false,
                                                            persist: ()=>{}
                                                        };
                                                        handleSendMessage(syntheticEvent);
                                                    }
                                                }
                                            },
                                            placeholder: "Edit your transcribed text here...",
                                            className: "jsx-efe5c9d818f3f82d" + " " + "w-full h-full min-h-[80px] p-2 border border-primaryColor/30 rounded-md focus:outline-none focus:ring-2 focus:ring-primaryColor/50 text-gray-800 resize-none"
                                        }, void 0, false, {
                                            fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                            lineNumber: 1638,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: toggleTranscriptEditMode,
                                            title: "Save edits",
                                            className: "jsx-efe5c9d818f3f82d" + " " + `absolute top-2 right-2 p-1.5 rounded-full transition-colors
                      ${selectedLanguage === "Tamil" ? 'bg-purple-100 text-purple-600 hover:bg-purple-200' : selectedLanguage === "Telugu" ? 'bg-green-100 text-green-600 hover:bg-green-200' : 'bg-blue-100 text-blue-600 hover:bg-blue-200'}`,
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiCheck"], {
                                                className: "text-lg"
                                            }, void 0, false, {
                                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                lineNumber: 1683,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                            lineNumber: 1672,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                    lineNumber: 1637,
                                    columnNumber: 17
                                }, this) : // View mode for transcript
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    ref: transcriptRef,
                                    className: "jsx-efe5c9d818f3f82d" + " " + "flex-grow text-gray-800 dark:text-white break-words relative",
                                    children: transcript && transcript.trim() !== "" ? // Show transcript with animated cursor
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-efe5c9d818f3f82d" + " " + "relative",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "jsx-efe5c9d818f3f82d" + " " + "m-0 leading-relaxed",
                                                children: [
                                                    transcript ? transcript : "",
                                                    speaking && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        style: {
                                                            animationName: 'pulse',
                                                            animationDuration: '1s',
                                                            animationIterationCount: 'infinite',
                                                            animationDirection: 'alternate'
                                                        },
                                                        className: "jsx-efe5c9d818f3f82d" + " " + "inline-block w-1 h-4 bg-primaryColor ml-1"
                                                    }, void 0, false, {
                                                        fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                        lineNumber: 1698,
                                                        columnNumber: 27
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                lineNumber: 1695,
                                                columnNumber: 23
                                            }, this),
                                            !isListening && transcript && transcript.trim() !== "" && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "jsx-efe5c9d818f3f82d" + " " + "absolute top-0 right-0 flex gap-1",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                        onClick: ()=>{
                                                            setTranscript("");
                                                            setInputText("");
                                                            setUserQuery("");
                                                            resetTranscript();
                                                            setWordCount(0);
                                                            setRecentWords([]);
                                                        },
                                                        title: "Clear transcript",
                                                        className: "jsx-efe5c9d818f3f82d" + " " + `p-1.5 rounded-full transition-colors
                              ${selectedLanguage === "Tamil" ? 'bg-red-100 text-red-600 hover:bg-red-200' : selectedLanguage === "Telugu" ? 'bg-red-100 text-red-600 hover:bg-red-200' : 'bg-red-100 text-red-600 hover:bg-red-200'}`,
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                            xmlns: "http://www.w3.org/2000/svg",
                                                            fill: "none",
                                                            viewBox: "0 0 24 24",
                                                            stroke: "currentColor",
                                                            className: "jsx-efe5c9d818f3f82d" + " " + "h-4 w-4",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                                strokeLinecap: "round",
                                                                strokeLinejoin: "round",
                                                                strokeWidth: 2,
                                                                d: "M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16",
                                                                className: "jsx-efe5c9d818f3f82d"
                                                            }, void 0, false, {
                                                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                                lineNumber: 1731,
                                                                columnNumber: 31
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                            lineNumber: 1730,
                                                            columnNumber: 29
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                        lineNumber: 1712,
                                                        columnNumber: 27
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                        onClick: toggleTranscriptEditMode,
                                                        title: "Edit transcript",
                                                        className: "jsx-efe5c9d818f3f82d" + " " + `p-1.5 rounded-full transition-colors
                              ${selectedLanguage === "Tamil" ? 'bg-purple-100 text-purple-600 hover:bg-purple-200' : selectedLanguage === "Telugu" ? 'bg-green-100 text-green-600 hover:bg-green-200' : 'bg-blue-100 text-blue-600 hover:bg-blue-200'}`,
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiPencilSimple"], {
                                                            className: "text-lg"
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                            lineNumber: 1745,
                                                            columnNumber: 29
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                        lineNumber: 1734,
                                                        columnNumber: 27
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                lineNumber: 1711,
                                                columnNumber: 25
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                        lineNumber: 1694,
                                        columnNumber: 21
                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-efe5c9d818f3f82d" + " " + "flex items-center gap-2 text-gray-400 italic",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-efe5c9d818f3f82d",
                                                children: selectedLanguage === "Tamil" ? "தமிழில் கேட்கிறது... இப்போது பேசவும்" : selectedLanguage === "Telugu" ? "తెలుగులో వింటున్నాము... ఇప్పుడు మాట్లాడండి" : selectedLanguage === "Kannada" ? "ಕನ್ನಡದಲ್ಲಿ ಆಲಿಸುತ್ತಿದ್ದೇವೆ... ಈಗ ಮಾತನಾಡಿ" : `Listening in ${selectedLanguage}... Speak now`
                                            }, void 0, false, {
                                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                lineNumber: 1752,
                                                columnNumber: 23
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "jsx-efe5c9d818f3f82d" + " " + "flex space-x-1 ml-2",
                                                children: [
                                                    0,
                                                    1,
                                                    2
                                                ].map((i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        style: {
                                                            animationName: 'pulse',
                                                            animationDuration: '1s',
                                                            animationIterationCount: 'infinite',
                                                            animationDirection: 'alternate',
                                                            animationDelay: `${i * 0.2}s`
                                                        },
                                                        className: "jsx-efe5c9d818f3f82d" + " " + `h-2 w-2 rounded-full ${selectedLanguage === "Tamil" ? "bg-purple-500" : selectedLanguage === "Telugu" ? "bg-green-500" : selectedLanguage === "Kannada" ? "bg-orange-500" : "bg-red-500"}`
                                                    }, `dot-${i}`, false, {
                                                        fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                        lineNumber: 1763,
                                                        columnNumber: 27
                                                    }, this))
                                            }, void 0, false, {
                                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                lineNumber: 1761,
                                                columnNumber: 23
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                        lineNumber: 1751,
                                        columnNumber: 21
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                    lineNumber: 1688,
                                    columnNumber: 17
                                }, this),
                                transcript && transcript.trim() !== "" && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "jsx-efe5c9d818f3f82d" + " " + "mt-2 flex items-center justify-between text-xs text-gray-500 border-t pt-2 border-gray-100",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "jsx-efe5c9d818f3f82d" + " " + "flex items-center gap-2",
                                            children: isEditingTranscript ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-efe5c9d818f3f82d" + " " + "text-blue-500 flex items-center gap-1",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiPencilSimple"], {}, void 0, false, {
                                                        fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                        lineNumber: 1795,
                                                        columnNumber: 25
                                                    }, this),
                                                    selectedLanguage === "Tamil" ? "திருத்துகிறது" : selectedLanguage === "Telugu" ? "సవరిస్తోంది" : selectedLanguage === "Kannada" ? "ಸಂಪಾದಿಸುತ್ತಿದೆ" : "Editing"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                lineNumber: 1794,
                                                columnNumber: 23
                                            }, this) : speaking ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-efe5c9d818f3f82d" + " " + "text-green-500 flex items-center gap-1",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiWaveform"], {
                                                        className: "animate-pulse"
                                                    }, void 0, false, {
                                                        fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                        lineNumber: 1806,
                                                        columnNumber: 25
                                                    }, this),
                                                    selectedLanguage === "Tamil" ? "செயலில்" : selectedLanguage === "Telugu" ? "యాక్టివ్" : selectedLanguage === "Kannada" ? "ಸಕ್ರಿಯ" : "Active"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                lineNumber: 1805,
                                                columnNumber: 23
                                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-efe5c9d818f3f82d" + " " + "text-gray-400 flex items-center gap-1",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiWaveform"], {}, void 0, false, {
                                                        fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                        lineNumber: 1817,
                                                        columnNumber: 25
                                                    }, this),
                                                    selectedLanguage === "Tamil" ? "இடைநிறுத்தப்பட்டது" : selectedLanguage === "Telugu" ? "నిలిపివేయబడింది" : selectedLanguage === "Kannada" ? "ವಿರಾಮಗೊಳಿಸಲಾಗಿದೆ" : "Paused"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                lineNumber: 1816,
                                                columnNumber: 23
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                            lineNumber: 1792,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "jsx-efe5c9d818f3f82d" + " " + "bg-gray-100 px-2 py-1 rounded-full text-xs",
                                            children: selectedLanguage === "Tamil" ? `${wordCount} சொற்கள்` : selectedLanguage === "Telugu" ? `${wordCount} పదాలు` : selectedLanguage === "Kannada" ? `${wordCount} ಪದಗಳು` : `${wordCount} words`
                                        }, void 0, false, {
                                            fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                            lineNumber: 1828,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                    lineNumber: 1791,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/chatComponents/ChatBox.tsx",
                            lineNumber: 1634,
                            columnNumber: 13
                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "jsx-efe5c9d818f3f82d" + " " + "relative w-full",
                            children: [
                                showUploadedContent && (uploadedFiles.length > 0 || uploadedURLs.length > 0) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "jsx-efe5c9d818f3f82d" + " " + "px-4 pt-3 pb-2 border-b border-gray-100 dark:border-gray-700",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-efe5c9d818f3f82d" + " " + "space-y-2",
                                        children: [
                                            uploadedFiles.map((file, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "jsx-efe5c9d818f3f82d" + " " + "flex items-center gap-2 text-sm text-gray-700 dark:text-gray-300",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "jsx-efe5c9d818f3f82d" + " " + `flex-shrink-0 ${selectedLanguage === "Tamil" ? 'text-purple-600' : selectedLanguage === "Telugu" ? 'text-green-600' : selectedLanguage === "Kannada" ? 'text-orange-600' : 'text-blue-600'}`,
                                                            children: getFileIcon(file.name)
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                            lineNumber: 1852,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "jsx-efe5c9d818f3f82d" + " " + "flex-grow truncate font-medium",
                                                            children: file.name
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                            lineNumber: 1863,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "jsx-efe5c9d818f3f82d" + " " + "text-xs text-gray-500 dark:text-gray-400",
                                                            children: [
                                                                (file.size / 1024 / 1024).toFixed(1),
                                                                "MB"
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                            lineNumber: 1866,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                            onClick: ()=>removeUploadedFile(index),
                                                            title: getUploadDisplayText().removeFile,
                                                            className: "jsx-efe5c9d818f3f82d" + " " + "flex-shrink-0 p-1 text-gray-400 hover:text-red-500 transition-colors rounded",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiX"], {
                                                                className: "w-3 h-3"
                                                            }, void 0, false, {
                                                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                                lineNumber: 1874,
                                                                columnNumber: 27
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                            lineNumber: 1869,
                                                            columnNumber: 25
                                                        }, this)
                                                    ]
                                                }, `file-${index}`, true, {
                                                    fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                    lineNumber: 1848,
                                                    columnNumber: 23
                                                }, this)),
                                            uploadedURLs.map((urlItem, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "jsx-efe5c9d818f3f82d" + " " + "flex items-center gap-2 text-sm text-gray-700 dark:text-gray-300",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "jsx-efe5c9d818f3f82d" + " " + `flex-shrink-0 ${urlItem.type === 'youtube' ? 'text-red-600' : selectedLanguage === "Tamil" ? 'text-purple-600' : selectedLanguage === "Telugu" ? 'text-green-600' : selectedLanguage === "Kannada" ? 'text-orange-600' : 'text-blue-600'}`,
                                                            children: getUrlIcon(urlItem.type)
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                            lineNumber: 1885,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "jsx-efe5c9d818f3f82d" + " " + "flex-grow truncate font-medium",
                                                            children: urlItem.type === 'youtube' ? getUploadDisplayText().youtubeVideo : getUploadDisplayText().articleLink
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                            lineNumber: 1897,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "jsx-efe5c9d818f3f82d" + " " + "text-xs text-gray-500 dark:text-gray-400 truncate max-w-[100px]",
                                                            children: urlItem.url.replace(/^https?:\/\//, '')
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                            lineNumber: 1902,
                                                            columnNumber: 25
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                            onClick: ()=>removeUploadedURL(index),
                                                            title: getUploadDisplayText().removeUrl,
                                                            className: "jsx-efe5c9d818f3f82d" + " " + "flex-shrink-0 p-1 text-gray-400 hover:text-red-500 transition-colors rounded",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiX"], {
                                                                className: "w-3 h-3"
                                                            }, void 0, false, {
                                                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                                lineNumber: 1910,
                                                                columnNumber: 27
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                            lineNumber: 1905,
                                                            columnNumber: 25
                                                        }, this)
                                                    ]
                                                }, `url-${index}`, true, {
                                                    fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                    lineNumber: 1881,
                                                    columnNumber: 23
                                                }, this))
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                        lineNumber: 1845,
                                        columnNumber: 19
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                    lineNumber: 1844,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "jsx-efe5c9d818f3f82d" + " " + "relative flex items-center w-full",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                            placeholder: selectedLanguage === "Tamil" ? "நிதி தொடர்பான கேள்வியை தமிழில் கேளுங்கள்..." : selectedLanguage === "Telugu" ? "ఒక ఆర్థిక విషయంపై నేను ఒక ప్రశ్నను అడగదలుచుకున్నాను..." : selectedLanguage === "Kannada" ? "ಹಣಕಾಸು ವಿಷಯದ ಬಗ್ಗೆ ಪ್ರಶ్ನೆಯನ್ನು ಕೇಳಿ..." : "ask question based financial topic..",
                                            value: inputText,
                                            onChange: (e)=>{
                                                setUserQuery(e.target.value);
                                                setInputText(e.target.value);
                                                // Clear any language error when the user starts typing
                                                if (languageError) {
                                                    setLanguageError(null);
                                                }
                                            },
                                            onKeyDown: (e)=>{
                                                if (e.key === 'Enter' && !e.shiftKey) {
                                                    e.preventDefault();
                                                    // Only submit if there's text to send and not currently loading
                                                    if (inputText.trim() && !isLoading) {
                                                        // Create a synthetic form event to trigger handleSendMessage
                                                        const syntheticEvent = {
                                                            preventDefault: ()=>{},
                                                            stopPropagation: ()=>{},
                                                            nativeEvent: e.nativeEvent,
                                                            target: e.target,
                                                            currentTarget: e.currentTarget,
                                                            bubbles: false,
                                                            cancelable: false,
                                                            defaultPrevented: false,
                                                            eventPhase: 0,
                                                            isTrusted: false,
                                                            timeStamp: Date.now(),
                                                            type: 'submit',
                                                            isDefaultPrevented: ()=>false,
                                                            isPropagationStopped: ()=>false,
                                                            persist: ()=>{}
                                                        };
                                                        handleSendMessage(syntheticEvent);
                                                    }
                                                }
                                            },
                                            disabled: isLoading,
                                            onClick: ()=>{
                                                // Close any open dropdowns when clicking in the input field
                                                // No longer need to close language dropdown since we're using horizontal buttons
                                                // setShowLanguageDropdown(false);
                                                setShowSuggestions(false);
                                            },
                                            className: "jsx-efe5c9d818f3f82d" + " " + "w-full outline-none p-4 pr-12 bg-transparent relative z-[1]"
                                        }, void 0, false, {
                                            fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                            lineNumber: 1919,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "jsx-efe5c9d818f3f82d" + " " + "absolute right-3 top-1/2 transform -translate-y-1/2 z-[2]",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$chatComponents$2f$ChatInputUpload$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                selectedLanguage: selectedLanguage,
                                                disabled: isLoading,
                                                onUploadStateChange: handleUploadStateChange,
                                                onNetworkError: ()=>setShowConnectionTest(true),
                                                onFileUpload: (files)=>{
                                                    console.log('Files uploaded:', files);
                                                    // Only process if we have valid files
                                                    if (files && files.length > 0) {
                                                        // Prevent duplicates by checking if files already exist
                                                        setUploadedFiles((prevFiles)=>{
                                                            const newFiles = files.filter((newFile)=>!prevFiles.some((existingFile)=>existingFile.name === newFile.name && existingFile.size === newFile.size && existingFile.lastModified === newFile.lastModified));
                                                            if (newFiles.length > 0) {
                                                                setShowUploadedContent(true);
                                                                return [
                                                                    ...prevFiles,
                                                                    ...newFiles
                                                                ];
                                                            }
                                                            return prevFiles;
                                                        });
                                                    }
                                                },
                                                onURLSubmit: (url, type)=>{
                                                    console.log('URL submitted:', url, 'Type:', type);
                                                    // Validate URL before adding
                                                    if (url && url.trim()) {
                                                        // Prevent duplicate URLs
                                                        setUploadedURLs((prev)=>{
                                                            const urlExists = prev.some((existingUrl)=>existingUrl.url === url.trim());
                                                            if (urlExists) {
                                                                console.log('URL already exists:', url);
                                                                return prev;
                                                            }
                                                            setShowUploadedContent(true);
                                                            return [
                                                                ...prev,
                                                                {
                                                                    url: url.trim(),
                                                                    type
                                                                }
                                                            ];
                                                        });
                                                    }
                                                }
                                            }, void 0, false, {
                                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                lineNumber: 1977,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                            lineNumber: 1976,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                    lineNumber: 1918,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/components/chatComponents/ChatBox.tsx",
                            lineNumber: 1841,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/components/chatComponents/ChatBox.tsx",
                        lineNumber: 1630,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "jsx-efe5c9d818f3f82d" + " " + "flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-0 pt-3 sm:pt-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "jsx-efe5c9d818f3f82d" + " " + "flex justify-start items-center gap-3 relative",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        type: "button",
                                        onClick: ()=>setShowSuggestions(!showSuggestions),
                                        className: "jsx-efe5c9d818f3f82d" + " " + `bg-white px-3 py-2 rounded-lg flex items-center gap-2 border
                ${showSuggestions ? 'border-primaryColor bg-primaryColor/5' : 'border-primaryColor/20'}
                hover:bg-primaryColor/5 hover:border-primaryColor transition-all shadow-sm hover:shadow dark:bg-n0
                text-sm font-medium
                ${selectedLanguage === "Tamil" ? 'text-purple-700' : selectedLanguage === "Telugu" ? 'text-green-700' : selectedLanguage === "Kannada" ? 'text-orange-700' : 'text-gray-700'}
                dark:text-gray-300`,
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiLightbulb"], {
                                                className: `${showSuggestions ? 'text-primaryColor' : selectedLanguage === "Tamil" ? 'text-purple-500' : selectedLanguage === "Telugu" ? 'text-green-500' : selectedLanguage === "Kannada" ? 'text-orange-500' : 'text-amber-500'}`
                                            }, void 0, false, {
                                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                lineNumber: 2043,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "jsx-efe5c9d818f3f82d",
                                                children: selectedLanguage === "Tamil" ? 'பரிந்துரைகள்' : selectedLanguage === "Telugu" ? 'సూచనలు' : selectedLanguage === "Kannada" ? 'ಸಲಹೆಗಳು' : 'Suggestions'
                                            }, void 0, false, {
                                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                lineNumber: 2052,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiSparkle"], {
                                                className: `${showSuggestions ? 'text-primaryColor' : selectedLanguage === "Tamil" ? 'text-purple-400' : selectedLanguage === "Telugu" ? 'text-green-400' : selectedLanguage === "Kannada" ? 'text-orange-400' : 'text-amber-400'}
                ${!showSuggestions ? 'animate-pulse' : ''}`
                                            }, void 0, false, {
                                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                lineNumber: 2061,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                        lineNumber: 2028,
                                        columnNumber: 13
                                    }, this),
                                    showSuggestions && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        onClick: ()=>setShowSuggestions(false),
                                        style: {
                                            backdropFilter: 'blur(3px)',
                                            animationDuration: '0.2s'
                                        },
                                        className: "jsx-efe5c9d818f3f82d" + " " + `fixed inset-0 flex items-center justify-center z-[60] animate-fadeIn ${selectedLanguage === "Tamil" ? 'bg-gradient-to-br from-purple-900/30 to-black/40' : selectedLanguage === "Telugu" ? 'bg-gradient-to-br from-green-900/30 to-black/40' : selectedLanguage === "Kannada" ? 'bg-gradient-to-br from-orange-900/30 to-black/40' : 'bg-gradient-to-br from-blue-900/30 to-black/40'}`,
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            ref: suggestionsRef,
                                            onClick: (e)=>e.stopPropagation(),
                                            style: {
                                                animationDuration: '0.3s',
                                                boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.05)'
                                            },
                                            className: "jsx-efe5c9d818f3f82d" + " " + `rounded-xl shadow-2xl border max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden animate-scaleIn ${selectedLanguage === "Tamil" ? 'border-purple-200/50 bg-gradient-to-b from-white to-purple-50/30' : selectedLanguage === "Telugu" ? 'border-green-200/50 bg-gradient-to-b from-white to-green-50/30' : selectedLanguage === "Kannada" ? 'border-orange-200/50 bg-gradient-to-b from-white to-orange-50/30' : 'border-blue-200/50 bg-gradient-to-b from-white to-blue-50/30'}`,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "jsx-efe5c9d818f3f82d" + " " + `p-5 border-b flex justify-between items-center rounded-t-xl ${selectedLanguage === "Tamil" ? 'bg-gradient-to-r from-purple-50 to-purple-100 border-purple-100' : selectedLanguage === "Telugu" ? 'bg-gradient-to-r from-green-50 to-green-100 border-green-100' : selectedLanguage === "Kannada" ? 'bg-gradient-to-r from-orange-50 to-orange-100 border-orange-100' : 'bg-gradient-to-r from-blue-50 to-blue-100 border-blue-100'}`,
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                            className: "jsx-efe5c9d818f3f82d" + " " + "text-lg font-semibold flex items-center gap-2",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "jsx-efe5c9d818f3f82d" + " " + `p-2 rounded-full ${selectedLanguage === "Tamil" ? 'bg-purple-100' : selectedLanguage === "Telugu" ? 'bg-green-100' : selectedLanguage === "Kannada" ? 'bg-orange-100' : 'bg-blue-100'}`,
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiLightbulb"], {
                                                                        className: `text-xl ${selectedLanguage === "Tamil" ? 'text-purple-500' : selectedLanguage === "Telugu" ? 'text-green-500' : selectedLanguage === "Kannada" ? 'text-orange-500' : 'text-blue-500'}`
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                                        lineNumber: 2124,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                                    lineNumber: 2115,
                                                                    columnNumber: 23
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                    className: "jsx-efe5c9d818f3f82d" + " " + ((selectedLanguage === "Tamil" ? 'text-purple-800' : selectedLanguage === "Telugu" ? 'text-green-800' : selectedLanguage === "Kannada" ? 'text-orange-800' : 'text-blue-800') || ""),
                                                                    children: selectedLanguage === "Tamil" ? 'பரிந்துரைக்கப்பட்ட நிதி கேள்விகள்' : selectedLanguage === "Telugu" ? 'సిఫార్సు చేయబడిన ఆర్థిక ప్రశ్నలు' : selectedLanguage === "Kannada" ? 'ಶಿఫಾರಸು ಮಾಡಲಾದ ಹಣಕಾಸು ಪ್ರಶ್ನೆಗಳು' : 'Recommended Financial Questions'
                                                                }, void 0, false, {
                                                                    fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                                    lineNumber: 2134,
                                                                    columnNumber: 23
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                            lineNumber: 2114,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                            onClick: ()=>setShowSuggestions(false),
                                                            "aria-label": "Close",
                                                            className: "jsx-efe5c9d818f3f82d" + " " + `p-2 rounded-full transition-colors ${selectedLanguage === "Tamil" ? 'bg-purple-100/50 text-purple-500 hover:bg-purple-200 hover:text-purple-700' : selectedLanguage === "Telugu" ? 'bg-green-100/50 text-green-500 hover:bg-green-200 hover:text-green-700' : selectedLanguage === "Kannada" ? 'bg-orange-100/50 text-orange-500 hover:bg-orange-200 hover:text-orange-700' : 'bg-blue-100/50 text-blue-500 hover:bg-blue-200 hover:text-blue-700'}`,
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                                xmlns: "http://www.w3.org/2000/svg",
                                                                fill: "none",
                                                                viewBox: "0 0 24 24",
                                                                stroke: "currentColor",
                                                                className: "jsx-efe5c9d818f3f82d" + " " + "h-5 w-5",
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                                    strokeLinecap: "round",
                                                                    strokeLinejoin: "round",
                                                                    strokeWidth: 2,
                                                                    d: "M6 18L18 6M6 6l12 12",
                                                                    className: "jsx-efe5c9d818f3f82d"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                                    lineNumber: 2166,
                                                                    columnNumber: 25
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                                lineNumber: 2165,
                                                                columnNumber: 23
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                            lineNumber: 2152,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                    lineNumber: 2105,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "jsx-efe5c9d818f3f82d" + " " + "p-5 overflow-y-auto max-h-[60vh]",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "jsx-efe5c9d818f3f82d" + " " + `rounded-lg p-3 mb-5 ${selectedLanguage === "Tamil" ? 'bg-gradient-to-r from-purple-50 to-white border border-purple-100' : selectedLanguage === "Telugu" ? 'bg-gradient-to-r from-green-50 to-white border border-green-100' : selectedLanguage === "Kannada" ? 'bg-gradient-to-r from-orange-50 to-white border border-orange-100' : 'bg-gradient-to-r from-blue-50 to-white border border-blue-100'}`,
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                className: "jsx-efe5c9d818f3f82d" + " " + `text-sm font-medium ${selectedLanguage === "Tamil" ? 'text-purple-700' : selectedLanguage === "Telugu" ? 'text-green-700' : selectedLanguage === "Kannada" ? 'text-orange-700' : 'text-blue-700'}`,
                                                                children: selectedLanguage === "Tamil" ? 'உங்கள் நிதி தேவைகளுக்கான பரிந்துரைக்கப்பட்ட கேள்விகளைப் பார்க்கவும்' : selectedLanguage === "Telugu" ? 'మీ ఆర్థిక అవసరాలకు సిఫార్సు చేయబడిన ప్రశ్నలను చూడండి' : selectedLanguage === "Kannada" ? 'ನಿಮ್ಮ ಹಣಕಾಸು ಅಗತ್ಯಗಳಿಗಾಗಿ ಈ ಶಿಫಾರಸು ಮಾಡಲಾದ ಪ್ರಶ್ನೆಗಳನ್ನು ಆಯ್ಕೆಮಾಡಿ' : 'Select from these recommended questions for your financial needs'
                                                            }, void 0, false, {
                                                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                                lineNumber: 2181,
                                                                columnNumber: 23
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                            lineNumber: 2172,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "jsx-efe5c9d818f3f82d" + " " + "space-y-3",
                                                            children: recommendedSuggestions.map((suggestion, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                    onClick: ()=>{
                                                                        handleSelectSuggestion(suggestion);
                                                                        setShowSuggestions(false);
                                                                    },
                                                                    style: {
                                                                        animationDelay: `${index * 0.05}s`,
                                                                        animationDuration: '0.3s'
                                                                    },
                                                                    className: "jsx-efe5c9d818f3f82d" + " " + `w-full text-left px-5 py-4 text-sm rounded-lg border shadow-sm hover:shadow-md transition-all
                            ${selectedLanguage === "Tamil" ? 'border-purple-200 bg-white hover:bg-gradient-to-r hover:from-purple-50 hover:to-white hover:border-purple-300' : selectedLanguage === "Telugu" ? 'border-green-200 bg-white hover:bg-gradient-to-r hover:from-green-50 hover:to-white hover:border-green-300' : selectedLanguage === "Kannada" ? 'border-orange-200 bg-white hover:bg-gradient-to-r hover:from-orange-50 hover:to-white hover:border-orange-300' : 'border-blue-200 bg-white hover:bg-gradient-to-r hover:from-blue-50 hover:to-white hover:border-blue-300'}
                            text-gray-700 animate-fadeInUp`,
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        className: "jsx-efe5c9d818f3f82d" + " " + "flex items-start",
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                className: "jsx-efe5c9d818f3f82d" + " " + `inline-block p-1.5 rounded-full mr-3 mt-0.5 ${selectedLanguage === "Tamil" ? 'bg-gradient-to-br from-purple-100 to-purple-200 text-purple-600' : selectedLanguage === "Telugu" ? 'bg-gradient-to-br from-green-100 to-green-200 text-green-600' : selectedLanguage === "Kannada" ? 'bg-gradient-to-br from-orange-100 to-orange-200 text-orange-600' : 'bg-gradient-to-br from-blue-100 to-blue-200 text-blue-600'}`,
                                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                                                    xmlns: "http://www.w3.org/2000/svg",
                                                                                    viewBox: "0 0 20 20",
                                                                                    fill: "currentColor",
                                                                                    className: "jsx-efe5c9d818f3f82d" + " " + "h-4 w-4",
                                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                                                        fillRule: "evenodd",
                                                                                        d: "M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z",
                                                                                        clipRule: "evenodd",
                                                                                        className: "jsx-efe5c9d818f3f82d"
                                                                                    }, void 0, false, {
                                                                                        fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                                                        lineNumber: 2230,
                                                                                        columnNumber: 33
                                                                                    }, this)
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                                                    lineNumber: 2229,
                                                                                    columnNumber: 31
                                                                                }, this)
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                                                lineNumber: 2220,
                                                                                columnNumber: 29
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                className: "jsx-efe5c9d818f3f82d" + " " + "font-medium",
                                                                                children: suggestion
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                                                lineNumber: 2233,
                                                                                columnNumber: 29
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                                        lineNumber: 2219,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                }, index, false, {
                                                                    fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                                    lineNumber: 2202,
                                                                    columnNumber: 25
                                                                }, this))
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                            lineNumber: 2200,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                    lineNumber: 2171,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "jsx-efe5c9d818f3f82d" + " " + `p-4 border-t rounded-b-xl text-right ${selectedLanguage === "Tamil" ? 'bg-gradient-to-r from-purple-50 to-purple-100 border-purple-100' : selectedLanguage === "Telugu" ? 'bg-gradient-to-r from-green-50 to-green-100 border-green-100' : selectedLanguage === "Kannada" ? 'bg-gradient-to-r from-orange-50 to-orange-100 border-orange-100' : 'bg-gradient-to-r from-blue-50 to-blue-100 border-blue-100'}`,
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                        onClick: ()=>setShowSuggestions(false),
                                                        className: "jsx-efe5c9d818f3f82d" + " " + `px-5 py-2 rounded-lg text-white font-medium shadow-sm transition-all hover:shadow-md
                        ${selectedLanguage === "Tamil" ? 'bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700' : selectedLanguage === "Telugu" ? 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700' : selectedLanguage === "Kannada" ? 'bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700' : 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700'}`,
                                                        children: selectedLanguage === "Tamil" ? 'மூடு' : selectedLanguage === "Telugu" ? 'మూసివేయండి' : selectedLanguage === "Kannada" ? 'ಮುಚ್ಚಿರಿ' : 'Close'
                                                    }, void 0, false, {
                                                        fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                        lineNumber: 2249,
                                                        columnNumber: 21
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                    lineNumber: 2240,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                            lineNumber: 2088,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                        lineNumber: 2075,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                lineNumber: 2027,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "jsx-efe5c9d818f3f82d" + " " + "flex justify-between items-center gap-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-efe5c9d818f3f82d" + " " + "flex-grow",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "jsx-efe5c9d818f3f82d" + " " + "hidden md:flex md:items-center",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "jsx-efe5c9d818f3f82d" + " " + "flex space-x-4",
                                                    children: languages.map((language, index)=>{
                                                        const isSelected = selectedLanguage === language.name;
                                                        // Get color classes based on language
                                                        const getColorClass = ()=>{
                                                            if (language.name === "Tamil") return "text-purple-700 border-purple-300 bg-purple-50";
                                                            if (language.name === "Telugu") return "text-green-700 border-green-300 bg-green-50";
                                                            if (language.name === "Kannada") return "text-orange-700 border-orange-300 bg-orange-50";
                                                            return "text-blue-700 border-blue-300 bg-blue-50";
                                                        };
                                                        const getHoverClass = ()=>{
                                                            if (language.name === "Tamil") return "hover:bg-purple-100 hover:border-purple-400";
                                                            if (language.name === "Telugu") return "hover:bg-green-100 hover:border-green-400";
                                                            if (language.name === "Kannada") return "hover:bg-orange-100 hover:border-orange-400";
                                                            return "hover:bg-blue-100 hover:border-blue-400";
                                                        };
                                                        const getIconColor = ()=>{
                                                            if (language.name === "Tamil") return "text-purple-600";
                                                            if (language.name === "Telugu") return "text-green-600";
                                                            if (language.name === "Kannada") return "text-orange-600";
                                                            return "text-blue-600";
                                                        };
                                                        const getBorderColor = ()=>{
                                                            if (language.name === "Tamil") return "bg-purple-500";
                                                            if (language.name === "Telugu") return "bg-green-500";
                                                            if (language.name === "Kannada") return "bg-orange-500";
                                                            return "bg-blue-500";
                                                        };
                                                        // Using type="button" to prevent form submission when clicking language buttons
                                                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                            type: "button",
                                                            onClick: (e)=>handleSelectLanguage(e, language.name),
                                                            disabled: languageButtonsDisabled || isLoading,
                                                            style: {
                                                                animationDelay: `${index * 0.05}s`,
                                                                animationDuration: '0.3s'
                                                            },
                                                            className: "jsx-efe5c9d818f3f82d" + " " + `px-5 py-2 rounded-lg text-sm font-medium transition-all border relative
                          ${isSelected ? getColorClass() : 'bg-white text-gray-600 border-gray-200 ' + getHoverClass()}
                          transform transition-all duration-300 ease-in-out
                          ${isSelected ? 'scale-105 shadow-md' : 'hover:scale-105 hover:shadow-sm'}
                          ${languageButtonsDisabled || isLoading ? 'opacity-50 cursor-not-allowed' : ''}
                          animate-fadeIn
                        `,
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "jsx-efe5c9d818f3f82d" + " " + "flex items-center gap-2",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiGlobe"], {
                                                                            className: `${getIconColor()} text-lg ${isSelected ? 'animate-fadeIn' : ''}`,
                                                                            style: {
                                                                                animationDuration: '0.3s'
                                                                            }
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                                            lineNumber: 2339,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                            className: "jsx-efe5c9d818f3f82d" + " " + "font-medium",
                                                                            children: language.name
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                                            lineNumber: 2342,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                                    lineNumber: 2338,
                                                                    columnNumber: 25
                                                                }, this),
                                                                isSelected && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    style: {
                                                                        width: '100%',
                                                                        transformOrigin: 'left',
                                                                        animationDuration: '0.4s'
                                                                    },
                                                                    className: "jsx-efe5c9d818f3f82d" + " " + `absolute bottom-0 left-0 h-0.5 rounded-full animate-scaleInHorizontal ${getBorderColor()}`
                                                                }, void 0, false, {
                                                                    fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                                    lineNumber: 2347,
                                                                    columnNumber: 27
                                                                }, this),
                                                                isSelected && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    style: {
                                                                        width: '100%',
                                                                        transformOrigin: 'right',
                                                                        animationDuration: '0.4s',
                                                                        animationDelay: '0.1s'
                                                                    },
                                                                    className: "jsx-efe5c9d818f3f82d" + " " + `absolute top-0 right-0 h-0.5 rounded-full animate-scaleInHorizontal ${getBorderColor()}`
                                                                }, void 0, false, {
                                                                    fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                                    lineNumber: 2359,
                                                                    columnNumber: 27
                                                                }, this)
                                                            ]
                                                        }, index, true, {
                                                            fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                            lineNumber: 2319,
                                                            columnNumber: 23
                                                        }, this);
                                                    })
                                                }, void 0, false, {
                                                    fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                    lineNumber: 2283,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                lineNumber: 2277,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                ref: languageMenuRef,
                                                className: "jsx-efe5c9d818f3f82d" + " " + "md:hidden relative",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                        type: "button",
                                                        onClick: ()=>setShowLanguageMenu(!showLanguageMenu),
                                                        disabled: languageButtonsDisabled || isLoading,
                                                        className: "jsx-efe5c9d818f3f82d" + " " + `flex items-center gap-2 px-4 py-2 rounded-lg border transition-all
                    ${selectedLanguage === "Tamil" ? "text-purple-700 border-purple-300 bg-purple-50" : selectedLanguage === "Telugu" ? "text-green-700 border-green-300 bg-green-50" : selectedLanguage === "Kannada" ? "text-orange-700 border-orange-300 bg-orange-50" : "text-blue-700 border-blue-300 bg-blue-50"}
                    hover:shadow-sm
                    ${languageButtonsDisabled || isLoading ? 'opacity-50 cursor-not-allowed' : ''}
                  `,
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiGlobe"], {
                                                                className: `text-lg ${selectedLanguage === "Tamil" ? "text-purple-600" : selectedLanguage === "Telugu" ? "text-green-600" : selectedLanguage === "Kannada" ? "text-orange-600" : "text-blue-600"}`
                                                            }, void 0, false, {
                                                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                                lineNumber: 2394,
                                                                columnNumber: 19
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "jsx-efe5c9d818f3f82d" + " " + "font-medium text-sm",
                                                                children: selectedLanguage
                                                            }, void 0, false, {
                                                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                                lineNumber: 2403,
                                                                columnNumber: 19
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "jsx-efe5c9d818f3f82d" + " " + `transition-transform duration-300 ${showLanguageMenu ? 'rotate-180' : ''}`,
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                                    xmlns: "http://www.w3.org/2000/svg",
                                                                    fill: "none",
                                                                    viewBox: "0 0 24 24",
                                                                    stroke: "currentColor",
                                                                    className: "jsx-efe5c9d818f3f82d" + " " + "h-4 w-4",
                                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                                        strokeLinecap: "round",
                                                                        strokeLinejoin: "round",
                                                                        strokeWidth: 2,
                                                                        d: "M5 15l7-7 7 7",
                                                                        className: "jsx-efe5c9d818f3f82d"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                                        lineNumber: 2406,
                                                                        columnNumber: 23
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                                    lineNumber: 2405,
                                                                    columnNumber: 21
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                                lineNumber: 2404,
                                                                columnNumber: 19
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                        lineNumber: 2377,
                                                        columnNumber: 17
                                                    }, this),
                                                    showLanguageMenu && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        style: {
                                                            animationDuration: '0.2s'
                                                        },
                                                        className: "jsx-efe5c9d818f3f82d" + " " + "absolute bottom-full left-0 mb-1 w-full bg-white dark:bg-n0 rounded-lg border shadow-lg z-[40] overflow-hidden animate-fadeIn",
                                                        children: languages.map((language, index)=>{
                                                            const isSelected = selectedLanguage === language.name;
                                                            // Get color classes based on language
                                                            const getItemColorClass = ()=>{
                                                                if (language.name === "Tamil") return isSelected ? "bg-purple-50 text-purple-700" : "hover:bg-purple-50";
                                                                if (language.name === "Telugu") return isSelected ? "bg-green-50 text-green-700" : "hover:bg-green-50";
                                                                if (language.name === "Kannada") return isSelected ? "bg-orange-50 text-orange-700" : "hover:bg-orange-50";
                                                                return isSelected ? "bg-blue-50 text-blue-700" : "hover:bg-blue-50";
                                                            };
                                                            const getIconColor = ()=>{
                                                                if (language.name === "Tamil") return "text-purple-600";
                                                                if (language.name === "Telugu") return "text-green-600";
                                                                if (language.name === "Kannada") return "text-orange-600";
                                                                return "text-blue-600";
                                                            };
                                                            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                type: "button",
                                                                onClick: (e)=>{
                                                                    handleSelectLanguage(e, language.name);
                                                                    setShowLanguageMenu(false);
                                                                },
                                                                disabled: languageButtonsDisabled || isLoading,
                                                                style: {
                                                                    animationDelay: `${index * 0.05}s`
                                                                },
                                                                className: "jsx-efe5c9d818f3f82d" + " " + `w-full flex items-center gap-2 px-4 py-3 text-left text-sm ${getItemColorClass()} transition-colors
                            ${languageButtonsDisabled || isLoading ? 'opacity-50 cursor-not-allowed' : ''}
                          `,
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiGlobe"], {
                                                                        className: `${getIconColor()} text-lg`
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                                        lineNumber: 2450,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "jsx-efe5c9d818f3f82d" + " " + "font-medium",
                                                                        children: language.name
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                                        lineNumber: 2451,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    isSelected && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                                        xmlns: "http://www.w3.org/2000/svg",
                                                                        fill: "none",
                                                                        viewBox: "0 0 24 24",
                                                                        stroke: "currentColor",
                                                                        className: "jsx-efe5c9d818f3f82d" + " " + "h-4 w-4 ml-auto",
                                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                                            strokeLinecap: "round",
                                                                            strokeLinejoin: "round",
                                                                            strokeWidth: 2,
                                                                            d: "M5 13l4 4L19 7",
                                                                            className: "jsx-efe5c9d818f3f82d"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                                            lineNumber: 2454,
                                                                            columnNumber: 31
                                                                        }, this)
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                                        lineNumber: 2453,
                                                                        columnNumber: 29
                                                                    }, this)
                                                                ]
                                                            }, index, true, {
                                                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                                lineNumber: 2435,
                                                                columnNumber: 25
                                                            }, this);
                                                        })
                                                    }, void 0, false, {
                                                        fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                        lineNumber: 2413,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                lineNumber: 2376,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                        lineNumber: 2275,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-efe5c9d818f3f82d" + " " + "flex items-center gap-4",
                                        children: [
                                            !uploadIsActive && !uploadDropdownVisible && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "jsx-efe5c9d818f3f82d" + " " + "relative z-[3] flex items-center justify-center",
                                                children: browserSupportsSpeechRecognition ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    type: "button",
                                                    onClick: isListening ? toggleListening : toggleListening,
                                                    title: isListening ? "Stop voice recording" : `Start voice recording in ${selectedLanguage}`,
                                                    className: "jsx-efe5c9d818f3f82d" + " " + `p-3 rounded-full flex justify-center items-center border-2 transition-all duration-200 shadow-md hover:shadow-lg relative z-[3] min-w-[48px] min-h-[48px]
                      ${isListening ? 'border-red-500 bg-gradient-to-r from-red-50 to-red-100 hover:from-red-100 hover:to-red-200 scale-105' : selectedLanguage === "Tamil" ? 'border-purple-400 bg-white hover:bg-purple-50 hover:border-purple-500 hover:scale-105' : selectedLanguage === "Telugu" ? 'border-green-400 bg-white hover:bg-green-50 hover:border-green-500 hover:scale-105' : selectedLanguage === "Kannada" ? 'border-orange-400 bg-white hover:bg-orange-50 hover:border-orange-500 hover:scale-105' : 'border-blue-400 bg-white hover:bg-blue-50 hover:border-blue-500 hover:scale-105'}
                      dark:bg-n0 transform`,
                                                    children: isListening ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "jsx-efe5c9d818f3f82d" + " " + "relative flex items-center justify-center",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                style: {
                                                                    animationName: 'pulse',
                                                                    animationDuration: '1.5s',
                                                                    animationIterationCount: 'infinite',
                                                                    animationDirection: 'alternate'
                                                                },
                                                                className: "jsx-efe5c9d818f3f82d" + " " + "absolute -inset-2 bg-red-200 rounded-full opacity-60"
                                                            }, void 0, false, {
                                                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                                lineNumber: 2490,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiStop"], {
                                                                className: "text-red-600 relative z-[4] w-5 h-5"
                                                            }, void 0, false, {
                                                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                                lineNumber: 2499,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                style: {
                                                                    animationName: 'ping',
                                                                    animationDuration: '1.5s',
                                                                    animationIterationCount: 'infinite'
                                                                },
                                                                className: "jsx-efe5c9d818f3f82d" + " " + "absolute -top-1 -right-1 h-3 w-3 rounded-full bg-red-500"
                                                            }, void 0, false, {
                                                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                                lineNumber: 2500,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                        lineNumber: 2489,
                                                        columnNumber: 23
                                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiMicrophone"], {
                                                        className: `w-5 h-5 ${selectedLanguage === "Tamil" ? 'text-purple-600' : selectedLanguage === "Telugu" ? 'text-green-600' : selectedLanguage === "Kannada" ? 'text-orange-600' : 'text-blue-600'}`
                                                    }, void 0, false, {
                                                        fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                        lineNumber: 2510,
                                                        columnNumber: 23
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                    lineNumber: 2471,
                                                    columnNumber: 19
                                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    type: "button",
                                                    title: "Speech recognition not supported in this browser",
                                                    disabled: true,
                                                    className: "jsx-efe5c9d818f3f82d" + " " + "bg-white p-3 rounded-full flex justify-center items-center border-2 border-gray-300 text-gray-400 cursor-not-allowed min-w-[48px] min-h-[48px] shadow-md",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiMicrophone"], {
                                                        className: "w-5 h-5"
                                                    }, void 0, false, {
                                                        fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                        lineNumber: 2528,
                                                        columnNumber: 21
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                    lineNumber: 2522,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                lineNumber: 2469,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                type: "submit",
                                                disabled: isLoading,
                                                title: isLoading ? selectedLanguage === "Tamil" ? "அனுப்புகிறது..." : selectedLanguage === "Telugu" ? "పంపుతోంది..." : selectedLanguage === "Kannada" ? "ಕಳುಹಿಸಲಾಗುತ್ತಿದೆ..." : "Sending..." : selectedLanguage === "Tamil" ? "கேள்வியை அனுப்பு" : selectedLanguage === "Telugu" ? "ప్రశ్నను పంపండి" : selectedLanguage === "Kannada" ? "ಪ್ರಶ್ನೆಯನ್ನು ಕಳುಹಿಸಿ" : "Send question",
                                                className: "jsx-efe5c9d818f3f82d" + " " + `rounded-full flex justify-center items-center border-2 text-white shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105 min-h-[48px]
                  ${selectedLanguage === "Tamil" ? "px-5 py-3 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 border-purple-600" : selectedLanguage === "Telugu" ? "px-5 py-3 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 border-green-600" : selectedLanguage === "Kannada" ? "px-5 py-3 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 border-orange-600" : "px-5 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 border-blue-600"}
                  ${isLoading ? 'opacity-80 cursor-not-allowed scale-100' : ''}`,
                                                children: isLoading ? // Loading state with spinner
                                                selectedLanguage === "Tamil" ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "jsx-efe5c9d818f3f82d" + " " + "text-sm font-medium flex items-center gap-1.5",
                                                    children: [
                                                        "அனுப்புகிறது... ",
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiSpinner"], {
                                                            className: "animate-spin"
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                            lineNumber: 2569,
                                                            columnNumber: 39
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                    lineNumber: 2568,
                                                    columnNumber: 21
                                                }, this) : selectedLanguage === "Telugu" ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "jsx-efe5c9d818f3f82d" + " " + "text-sm font-medium flex items-center gap-1.5",
                                                    children: [
                                                        "పంపుతోంది... ",
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiSpinner"], {
                                                            className: "animate-spin"
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                            lineNumber: 2573,
                                                            columnNumber: 36
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                    lineNumber: 2572,
                                                    columnNumber: 21
                                                }, this) : selectedLanguage === "Kannada" ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "jsx-efe5c9d818f3f82d" + " " + "text-sm font-medium flex items-center gap-1.5",
                                                    children: [
                                                        "ಕಳುಹಿಸಲಾಗುತ್ತಿದೆ... ",
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiSpinner"], {
                                                            className: "animate-spin"
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                            lineNumber: 2577,
                                                            columnNumber: 43
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                    lineNumber: 2576,
                                                    columnNumber: 21
                                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "jsx-efe5c9d818f3f82d" + " " + "text-sm font-medium flex items-center gap-1.5",
                                                    children: [
                                                        "Sending... ",
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiSpinner"], {
                                                            className: "animate-spin"
                                                        }, void 0, false, {
                                                            fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                            lineNumber: 2581,
                                                            columnNumber: 34
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                    lineNumber: 2580,
                                                    columnNumber: 21
                                                }, this) : // Normal state
                                                selectedLanguage === "Tamil" ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "jsx-efe5c9d818f3f82d" + " " + "text-sm font-medium flex items-center gap-1.5",
                                                    children: [
                                                        "அனுப்பு ",
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiArrowUp"], {}, void 0, false, {
                                                            fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                            lineNumber: 2588,
                                                            columnNumber: 31
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                    lineNumber: 2587,
                                                    columnNumber: 21
                                                }, this) : selectedLanguage === "Telugu" ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "jsx-efe5c9d818f3f82d" + " " + "text-sm font-medium flex items-center gap-1.5",
                                                    children: [
                                                        "పంపండి ",
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiArrowUp"], {}, void 0, false, {
                                                            fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                            lineNumber: 2592,
                                                            columnNumber: 30
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                    lineNumber: 2591,
                                                    columnNumber: 21
                                                }, this) : selectedLanguage === "Kannada" ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "jsx-efe5c9d818f3f82d" + " " + "text-sm font-medium flex items-center gap-1.5",
                                                    children: [
                                                        "ಕಳುಹಿಸಿ ",
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiArrowUp"], {}, void 0, false, {
                                                            fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                            lineNumber: 2596,
                                                            columnNumber: 31
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                    lineNumber: 2595,
                                                    columnNumber: 21
                                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "jsx-efe5c9d818f3f82d" + " " + "text-sm font-medium flex items-center gap-1.5",
                                                    children: [
                                                        "Send ",
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$pi$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PiArrowUp"], {}, void 0, false, {
                                                            fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                            lineNumber: 2600,
                                                            columnNumber: 28
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                    lineNumber: 2599,
                                                    columnNumber: 21
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                                lineNumber: 2535,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                        lineNumber: 2466,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                                lineNumber: 2273,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/chatComponents/ChatBox.tsx",
                        lineNumber: 2026,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                lineNumber: 1615,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$jsx$2f$style$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                id: "bb71987326e11f28",
                children: "@keyframes pulse{0%{height:4px}to{height:16px}}@keyframes spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.animate-spin.jsx-efe5c9d818f3f82d{animation:1s linear infinite spin}.relative.z-50.jsx-efe5c9d818f3f82d{z-index:50;position:relative}.absolute.z-50.jsx-efe5c9d818f3f82d{z-index:50;position:absolute}input.jsx-efe5c9d818f3f82d{z-index:30;position:relative}textarea.jsx-efe5c9d818f3f82d{font-family:inherit;line-height:1.5;transition:all .2s ease-in-out}button.jsx-efe5c9d818f3f82d:hover svg.jsx-efe5c9d818f3f82d{transition:transform .2s ease-in-out;transform:scale(1.1)}@keyframes fadeIn{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}.animate-fadeIn.jsx-efe5c9d818f3f82d{animation:.2s ease-out forwards fadeIn}@keyframes scaleInHorizontal{0%{transform:scaleX(0)}to{transform:scaleX(1)}}.animate-scaleInHorizontal.jsx-efe5c9d818f3f82d{animation:.4s ease-out forwards scaleInHorizontal}@keyframes fadeInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}.animate-fadeInUp.jsx-efe5c9d818f3f82d{animation:.3s ease-out forwards fadeInUp}@keyframes scaleIn{0%{opacity:0;transform:scale(.95)}to{opacity:1;transform:scale(1)}}.animate-scaleIn.jsx-efe5c9d818f3f82d{animation:.2s ease-out forwards scaleIn}"
            }, void 0, false, void 0, this),
            showConnectionTest && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$debug$2f$ConnectionTest$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                onClose: ()=>setShowConnectionTest(false)
            }, void 0, false, {
                fileName: "[project]/components/chatComponents/ChatBox.tsx",
                lineNumber: 2712,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/chatComponents/ChatBox.tsx",
        lineNumber: 1421,
        columnNumber: 5
    }, this);
});
ChatBox.displayName = 'ChatBox';
const __TURBOPACK__default__export__ = ChatBox;
}}),
"[project]/app/(with-layout)/new-chat/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$jsx$2f$style$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/styled-jsx/style.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$favicon$2e$ico$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$favicon$2e$ico__$5b$app$2d$ssr$5d$__$28$static$2922$__$7d$__$5b$app$2d$ssr$5d$__$28$structured__image__object$2c$__ecmascript$29$__ = __turbopack_import__('[project]/public/images/favicon.ico.mjs { IMAGE => "[project]/public/images/favicon.ico [app-ssr] (static)" } [app-ssr] (structured image object, ecmascript)');
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/image.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$chatComponents$2f$ChatBox$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/components/chatComponents/ChatBox.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$chatComponents$2f$services$2f$ApiService$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/components/chatComponents/services/ApiService.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$v4$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__ = __turbopack_import__("[project]/node_modules/uuid/dist/esm/v4.js [app-ssr] (ecmascript) <export default as v4>");
"use client";
;
;
;
;
;
;
;
;
;
function NewChat() {
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    // State for FAISS index selector and fetched indexes
    const [faissIndexes, setFaissIndexes] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [selectedIndex, setSelectedIndex] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(()=>{
        // Initialize from localStorage if available
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        return '';
    });
    const [showConfirmation, setShowConfirmation] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    // Fetch FAISS indexes on mount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const fetchFilteredFaissData = async ()=>{
            // Only run on client side to prevent hydration errors
            if ("TURBOPACK compile-time truthy", 1) return;
            "TURBOPACK unreachable";
        };
        fetchFilteredFaissData();
    }, []);
    // Hide confirmation message after 5 seconds
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        let timer;
        if (showConfirmation) {
            timer = setTimeout(()=>{
                setShowConfirmation(false);
            }, 5000);
        }
        return ()=>{
            if (timer) clearTimeout(timer);
        };
    }, [
        showConfirmation
    ]);
    // Handle FAISS index selection
    const handleIndexSelect = (index)=>{
        setSelectedIndex(index);
        setShowConfirmation(true);
        // Store the selected index in localStorage for use in other components
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        console.log(`Selected FAISS index: ${index}`);
    };
    // Function to start a new chat with the selected index
    const startNewChat = ()=>{
        const chatId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$uuid$2f$dist$2f$esm$2f$v4$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__v4$3e$__["v4"])();
        const currentChatId = chatId;
        // Store the selected index in localStorage directly
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        // Navigate to the chat page without creating a message
        router.push(`/chat/${currentChatId}`);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$jsx$2f$style$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                id: "2e44817524a78e16",
                children: "@keyframes fadeIn{0%{opacity:0;transform:translateY(10px)}to{opacity:1;transform:translateY(0)}}.animate-fadeIn{animation:.3s ease-out forwards fadeIn}"
            }, void 0, false, void 0, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "jsx-2e44817524a78e16" + " " + "w-full h-full flex items-center justify-center",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "jsx-2e44817524a78e16" + " " + "w-full max-w-[1090px] mx-auto px-6 flex flex-col",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "jsx-2e44817524a78e16" + " " + `flex flex-col justify-center items-center text-center pb-8 `,
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "jsx-2e44817524a78e16" + " " + "flex justify-start items-center gap-3",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                            src: __TURBOPACK__imported__module__$5b$project$5d2f$public$2f$images$2f$favicon$2e$ico$2e$mjs__$7b$__IMAGE__$3d3e$__$225b$project$5d2f$public$2f$images$2f$favicon$2e$ico__$5b$app$2d$ssr$5d$__$28$static$2922$__$7d$__$5b$app$2d$ssr$5d$__$28$structured__image__object$2c$__ecmascript$29$__["default"],
                                            alt: ""
                                        }, void 0, false, {
                                            fileName: "[project]/app/(with-layout)/new-chat/page.tsx",
                                            lineNumber: 140,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "jsx-2e44817524a78e16" + " " + "text-2xl font-semibold text-n700 dark:text-n30",
                                            children: "Hello, I'm QueryOne"
                                        }, void 0, false, {
                                            fileName: "[project]/app/(with-layout)/new-chat/page.tsx",
                                            lineNumber: 141,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/(with-layout)/new-chat/page.tsx",
                                    lineNumber: 139,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "jsx-2e44817524a78e16" + " " + "text-n700 pt-4 dark:text-n30",
                                    children: "How can I make things easier for you?"
                                }, void 0, false, {
                                    fileName: "[project]/app/(with-layout)/new-chat/page.tsx",
                                    lineNumber: 145,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/(with-layout)/new-chat/page.tsx",
                            lineNumber: 136,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$chatComponents$2f$ChatBox$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                            fileName: "[project]/app/(with-layout)/new-chat/page.tsx",
                            lineNumber: 150,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "jsx-2e44817524a78e16" + " " + "w-full max-w-[800px] mx-auto mt-6 flex justify-center",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "jsx-2e44817524a78e16" + " " + "bg-gray-100 dark:bg-gray-800 rounded-lg p-2 flex flex-wrap justify-center gap-2",
                                children: isLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "jsx-2e44817524a78e16" + " " + "py-2 px-4 text-gray-500 dark:text-gray-400 flex items-center gap-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "jsx-2e44817524a78e16" + " " + "animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"
                                        }, void 0, false, {
                                            fileName: "[project]/app/(with-layout)/new-chat/page.tsx",
                                            lineNumber: 157,
                                            columnNumber: 19
                                        }, this),
                                        "Loading FAISS indexes..."
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/(with-layout)/new-chat/page.tsx",
                                    lineNumber: 156,
                                    columnNumber: 17
                                }, this) : faissIndexes.length > 0 ? faissIndexes.map((index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: ()=>handleIndexSelect(index),
                                        className: "jsx-2e44817524a78e16" + " " + `py-2 px-4 rounded-lg flex items-center gap-2 transition-colors
                      ${selectedIndex === index ? 'bg-white dark:bg-gray-700 shadow-sm text-blue-600 dark:text-blue-400' : 'hover:bg-white dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'}`,
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "jsx-2e44817524a78e16" + " " + "font-medium",
                                            children: index
                                        }, void 0, false, {
                                            fileName: "[project]/app/(with-layout)/new-chat/page.tsx",
                                            lineNumber: 170,
                                            columnNumber: 21
                                        }, this)
                                    }, index, false, {
                                        fileName: "[project]/app/(with-layout)/new-chat/page.tsx",
                                        lineNumber: 162,
                                        columnNumber: 19
                                    }, this)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "jsx-2e44817524a78e16" + " " + "py-2 px-4 text-gray-500 dark:text-gray-400",
                                    children: "No FAISS indexes found. Please create an index first."
                                }, void 0, false, {
                                    fileName: "[project]/app/(with-layout)/new-chat/page.tsx",
                                    lineNumber: 174,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/app/(with-layout)/new-chat/page.tsx",
                                lineNumber: 154,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/app/(with-layout)/new-chat/page.tsx",
                            lineNumber: 153,
                            columnNumber: 11
                        }, this),
                        showConfirmation && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "jsx-2e44817524a78e16" + " " + "w-full max-w-[800px] mx-auto mt-6",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "jsx-2e44817524a78e16" + " " + "bg-gray-100 dark:bg-gray-800 rounded-lg p-4 text-center animate-fadeIn",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-2e44817524a78e16" + " " + "text-green-600 dark:text-green-400 font-medium mb-2",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                xmlns: "http://www.w3.org/2000/svg",
                                                fill: "none",
                                                viewBox: "0 0 24 24",
                                                stroke: "currentColor",
                                                className: "jsx-2e44817524a78e16" + " " + "h-6 w-6 inline-block mr-2",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                    strokeLinecap: "round",
                                                    strokeLinejoin: "round",
                                                    strokeWidth: 2,
                                                    d: "M5 13l4 4L19 7",
                                                    className: "jsx-2e44817524a78e16"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/(with-layout)/new-chat/page.tsx",
                                                    lineNumber: 187,
                                                    columnNumber: 21
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/app/(with-layout)/new-chat/page.tsx",
                                                lineNumber: 186,
                                                columnNumber: 19
                                            }, this),
                                            'FAISS index "',
                                            selectedIndex,
                                            '" is selected, you can now ask questions based on this!'
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/(with-layout)/new-chat/page.tsx",
                                        lineNumber: 185,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-2e44817524a78e16" + " " + "flex justify-center mt-4",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: startNewChat,
                                            className: "jsx-2e44817524a78e16" + " " + "py-2 px-6 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition-colors flex items-center gap-2",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "jsx-2e44817524a78e16",
                                                    children: "Start Chat"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/(with-layout)/new-chat/page.tsx",
                                                    lineNumber: 196,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                    xmlns: "http://www.w3.org/2000/svg",
                                                    fill: "none",
                                                    viewBox: "0 0 24 24",
                                                    stroke: "currentColor",
                                                    className: "jsx-2e44817524a78e16" + " " + "h-4 w-4",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                        strokeLinecap: "round",
                                                        strokeLinejoin: "round",
                                                        strokeWidth: 2,
                                                        d: "M14 5l7 7m0 0l-7 7m7-7H3",
                                                        className: "jsx-2e44817524a78e16"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/(with-layout)/new-chat/page.tsx",
                                                        lineNumber: 198,
                                                        columnNumber: 23
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/app/(with-layout)/new-chat/page.tsx",
                                                    lineNumber: 197,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/(with-layout)/new-chat/page.tsx",
                                            lineNumber: 192,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/app/(with-layout)/new-chat/page.tsx",
                                        lineNumber: 191,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(with-layout)/new-chat/page.tsx",
                                lineNumber: 184,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/app/(with-layout)/new-chat/page.tsx",
                            lineNumber: 183,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/(with-layout)/new-chat/page.tsx",
                    lineNumber: 135,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/(with-layout)/new-chat/page.tsx",
                lineNumber: 134,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
}
const __TURBOPACK__default__export__ = NewChat;
}}),
"[project]/app/(with-layout)/new-chat/page.tsx [app-rsc] (ecmascript, Next.js server component, client modules ssr)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
}}),

};

//# sourceMappingURL=%5Broot%20of%20the%20server%5D__d1dc5a._.js.map