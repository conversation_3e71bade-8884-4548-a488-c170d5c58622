{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/node_modules/react-icons/lib/iconContext.mjs"], "sourcesContent": ["import React from \"react\";\nexport var DefaultContext = {\n  color: undefined,\n  size: undefined,\n  className: undefined,\n  style: undefined,\n  attr: undefined\n};\nexport var IconContext = React.createContext && /*#__PURE__*/React.createContext(DefaultContext);"], "names": [], "mappings": ";;;;AAAA;;AACO,IAAI,iBAAiB;IAC1B,OAAO;IACP,MAAM;IACN,WAAW;IACX,OAAO;IACP,MAAM;AACR;AACO,IAAI,cAAc,qMAAA,CAAA,UAAK,CAAC,aAAa,IAAI,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC", "ignoreList": [0]}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 27, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/node_modules/react-icons/lib/iconBase.mjs"], "sourcesContent": ["var _excluded = [\"attr\", \"size\", \"title\"];\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } } return target; }\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport React from \"react\";\nimport { IconContext, DefaultContext } from \"./iconContext.mjs\";\nfunction Tree2Element(tree) {\n  return tree && tree.map((node, i) => /*#__PURE__*/React.createElement(node.tag, _objectSpread({\n    key: i\n  }, node.attr), Tree2Element(node.child)));\n}\nexport function GenIcon(data) {\n  return props => /*#__PURE__*/React.createElement(IconBase, _extends({\n    attr: _objectSpread({}, data.attr)\n  }, props), Tree2Element(data.child));\n}\nexport function IconBase(props) {\n  var elem = conf => {\n    var {\n        attr,\n        size,\n        title\n      } = props,\n      svgProps = _objectWithoutProperties(props, _excluded);\n    var computedSize = size || conf.size || \"1em\";\n    var className;\n    if (conf.className) className = conf.className;\n    if (props.className) className = (className ? className + \" \" : \"\") + props.className;\n    return /*#__PURE__*/React.createElement(\"svg\", _extends({\n      stroke: \"currentColor\",\n      fill: \"currentColor\",\n      strokeWidth: \"0\"\n    }, conf.attr, attr, svgProps, {\n      className: className,\n      style: _objectSpread(_objectSpread({\n        color: props.color || conf.color\n      }, conf.style), props.style),\n      height: computedSize,\n      width: computedSize,\n      xmlns: \"http://www.w3.org/2000/svg\"\n    }), title && /*#__PURE__*/React.createElement(\"title\", null, title), props.children);\n  };\n  return IconContext !== undefined ? /*#__PURE__*/React.createElement(IconContext.Consumer, null, conf => elem(conf)) : elem(DefaultContext);\n}"], "names": [], "mappings": ";;;;AASA;AACA;AAVA,IAAI,YAAY;IAAC;IAAQ;IAAQ;CAAQ;AACzC,SAAS,yBAAyB,MAAM,EAAE,QAAQ;IAAI,IAAI,UAAU,MAAM,OAAO,CAAC;IAAG,IAAI,SAAS,8BAA8B,QAAQ;IAAW,IAAI,KAAK;IAAG,IAAI,OAAO,qBAAqB,EAAE;QAAE,IAAI,mBAAmB,OAAO,qBAAqB,CAAC;QAAS,IAAK,IAAI,GAAG,IAAI,iBAAiB,MAAM,EAAE,IAAK;YAAE,MAAM,gBAAgB,CAAC,EAAE;YAAE,IAAI,SAAS,OAAO,CAAC,QAAQ,GAAG;YAAU,IAAI,CAAC,OAAO,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,MAAM;YAAU,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;QAAE;IAAE;IAAE,OAAO;AAAQ;AAC3e,SAAS,8BAA8B,MAAM,EAAE,QAAQ;IAAI,IAAI,UAAU,MAAM,OAAO,CAAC;IAAG,IAAI,SAAS,CAAC;IAAG,IAAK,IAAI,OAAO,OAAQ;QAAE,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,MAAM;YAAE,IAAI,SAAS,OAAO,CAAC,QAAQ,GAAG;YAAU,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;QAAE;IAAE;IAAE,OAAO;AAAQ;AACtR,SAAS;IAAa,WAAW,uCAAgB,OAAO,MAAM,CAAC,IAAI;IAAuO,OAAO,SAAS,KAAK,CAAC,IAAI,EAAE;AAAY;AAClV,SAAS,QAAQ,CAAC,EAAE,CAAC;IAAI,IAAI,IAAI,OAAO,IAAI,CAAC;IAAI,IAAI,OAAO,qBAAqB,EAAE;QAAE,IAAI,IAAI,OAAO,qBAAqB,CAAC;QAAI,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,SAAU,CAAC;YAAI,OAAO,OAAO,wBAAwB,CAAC,GAAG,GAAG,UAAU;QAAE,EAAE,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG;IAAI;IAAE,OAAO;AAAG;AAC9P,SAAS,cAAc,CAAC;IAAI,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;QAAE,IAAI,IAAI,QAAQ,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,CAAC;QAAG,IAAI,IAAI,QAAQ,OAAO,IAAI,CAAC,GAAG,OAAO,CAAC,SAAU,CAAC;YAAI,gBAAgB,GAAG,GAAG,CAAC,CAAC,EAAE;QAAG,KAAK,OAAO,yBAAyB,GAAG,OAAO,gBAAgB,CAAC,GAAG,OAAO,yBAAyB,CAAC,MAAM,QAAQ,OAAO,IAAI,OAAO,CAAC,SAAU,CAAC;YAAI,OAAO,cAAc,CAAC,GAAG,GAAG,OAAO,wBAAwB,CAAC,GAAG;QAAK;IAAI;IAAE,OAAO;AAAG;AACtb,SAAS,gBAAgB,GAAG,EAAE,GAAG,EAAE,KAAK;IAAI,MAAM,eAAe;IAAM,IAAI,OAAO,KAAK;QAAE,OAAO,cAAc,CAAC,KAAK,KAAK;YAAE,OAAO;YAAO,YAAY;YAAM,cAAc;YAAM,UAAU;QAAK;IAAI,OAAO;QAAE,GAAG,CAAC,IAAI,GAAG;IAAO;IAAE,OAAO;AAAK;AAC3O,SAAS,eAAe,CAAC;IAAI,IAAI,IAAI,aAAa,GAAG;IAAW,OAAO,YAAY,OAAO,IAAI,IAAI,IAAI;AAAI;AAC1G,SAAS,aAAa,CAAC,EAAE,CAAC;IAAI,IAAI,YAAY,OAAO,KAAK,CAAC,GAAG,OAAO;IAAG,IAAI,IAAI,CAAC,CAAC,OAAO,WAAW,CAAC;IAAE,IAAI,KAAK,MAAM,GAAG;QAAE,IAAI,IAAI,EAAE,IAAI,CAAC,GAAG,KAAK;QAAY,IAAI,YAAY,OAAO,GAAG,OAAO;QAAG,MAAM,IAAI,UAAU;IAAiD;IAAE,OAAO,CAAC,aAAa,IAAI,SAAS,MAAM,EAAE;AAAI;;;AAGvT,SAAS,aAAa,IAAI;IACxB,OAAO,QAAQ,KAAK,GAAG,CAAC,CAAC,MAAM,IAAM,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,KAAK,GAAG,EAAE,cAAc;YAC5F,KAAK;QACP,GAAG,KAAK,IAAI,GAAG,aAAa,KAAK,KAAK;AACxC;AACO,SAAS,QAAQ,IAAI;IAC1B,OAAO,CAAA,QAAS,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,UAAU,SAAS;YAClE,MAAM,cAAc,CAAC,GAAG,KAAK,IAAI;QACnC,GAAG,QAAQ,aAAa,KAAK,KAAK;AACpC;AACO,SAAS,SAAS,KAAK;IAC5B,IAAI,OAAO,CAAA;QACT,IAAI,EACA,IAAI,EACJ,IAAI,EACJ,KAAK,EACN,GAAG,OACJ,WAAW,yBAAyB,OAAO;QAC7C,IAAI,eAAe,QAAQ,KAAK,IAAI,IAAI;QACxC,IAAI;QACJ,IAAI,KAAK,SAAS,EAAE,YAAY,KAAK,SAAS;QAC9C,IAAI,MAAM,SAAS,EAAE,YAAY,CAAC,YAAY,YAAY,MAAM,EAAE,IAAI,MAAM,SAAS;QACrF,OAAO,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO,SAAS;YACtD,QAAQ;YACR,MAAM;YACN,aAAa;QACf,GAAG,KAAK,IAAI,EAAE,MAAM,UAAU;YAC5B,WAAW;YACX,OAAO,cAAc,cAAc;gBACjC,OAAO,MAAM,KAAK,IAAI,KAAK,KAAK;YAClC,GAAG,KAAK,KAAK,GAAG,MAAM,KAAK;YAC3B,QAAQ;YACR,OAAO;YACP,OAAO;QACT,IAAI,SAAS,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,SAAS,MAAM,QAAQ,MAAM,QAAQ;IACrF;IACA,OAAO,qJAAA,CAAA,cAAW,KAAK,YAAY,WAAW,GAAE,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,qJAAA,CAAA,cAAW,CAAC,QAAQ,EAAE,MAAM,CAAA,OAAQ,KAAK,SAAS,KAAK,qJAAA,CAAA,iBAAc;AAC3I", "ignoreList": [0]}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}