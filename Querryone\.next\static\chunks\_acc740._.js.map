{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/hooks/useModalOpen.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useEffect, useRef, useState } from \"react\";\r\n\r\nexport default function useModalOpen() {\r\n  const [modal, setModal] = useState(false);\r\n  const modalRef = useRef<HTMLDivElement | null>(null);\r\n\r\n  useEffect(() => {\r\n    document.addEventListener(\"mousedown\", handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    };\r\n  }, []);\r\n  const handleClickOutside = (event: MouseEvent) => {\r\n    if (modalRef.current && !modalRef.current.contains(event.target as Node)) {\r\n      setModal(false);\r\n    }\r\n  };\r\n  return { modal, setModal, modalRef };\r\n}\r\n"], "names": [], "mappings": ";;;AACA;;AADA;;AAGe,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAE/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,SAAS,gBAAgB,CAAC,aAAa;YACvC;0CAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;iCAAG,EAAE;IACL,MAAM,qBAAqB,CAAC;QAC1B,IAAI,SAAS,OAAO,IAAI,CAAC,SAAS,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;YACxE,SAAS;QACX;IACF;IACA,OAAO;QAAE;QAAO;QAAU;IAAS;AACrC;GAhBwB"}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 49, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/stores/modal.ts"], "sourcesContent": ["import { create } from \"zustand\";\r\n\r\ntype MainModalType = {\r\n  show: boolean;\r\n  modalName: string;\r\n  modalOpen: (name: string) => void;\r\n  modalClose: () => void;\r\n};\r\n\r\nexport const useMainModal = create<MainModalType>((set) => ({\r\n  show: false,\r\n  modalName: \"\",\r\n  modalOpen: (name) => set({ show: true, modalName: name }),\r\n  modalClose: () => set({ show: false, modalName: \"\" }),\r\n}));\r\n"], "names": [], "mappings": ";;;AAAA;;AASO,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAiB,CAAC,MAAQ,CAAC;QAC1D,MAAM;QACN,WAAW;QACX,WAAW,CAAC,OAAS,IAAI;gBAAE,MAAM;gBAAM,WAAW;YAAK;QACvD,YAAY,IAAM,IAAI;gBAAE,MAAM;gBAAO,WAAW;YAAG;IACrD,CAAC"}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/stores/chatList.ts"], "sourcesContent": ["import { create } from \"zustand\";\r\nimport { baseUrl, uid } from \"../components/api/api\";\r\n\r\n\r\nexport interface UploadedFile {\r\n  name: string;\r\n  size: number;\r\n  type: string;\r\n  lastModified: number;\r\n}\r\n\r\nexport interface UploadedURL {\r\n  url: string;\r\n  type: 'youtube' | 'article';\r\n}\r\n\r\nexport interface Message {\r\n  isUser: boolean;\r\n  text: string | any;\r\n  timestamp: string;\r\n  messageId?: string;\r\n  uploadedFiles?: UploadedFile[];\r\n  uploadedURLs?: UploadedURL[];\r\n}\r\n\r\nexport interface Chat {\r\n  id: string;\r\n  title: string;\r\n  createdAt: string;\r\n  messages: Message[];\r\n  indexUsed?: string; // Store which FAISS index was used for this chat\r\n  embedModel?: string; // Store which embedding model was used for this chat\r\n}\r\n\r\ninterface ChatState {\r\n  chatList: Chat[];\r\n  userQuery: string;\r\n  isLoading: boolean;\r\n  isAnimation: boolean;\r\n  updateChatList: () => Promise<void>;\r\n  handleSubmit: (userQuery: string, chatId: string, aiResponse?: any, options?: { index?: string, embedModel?: string }) => void;\r\n  addMessage: (message: Message, chatId: string) => void;\r\n  addChat: (chat: Chat) => void;\r\n  setUserQuery: (query: string) => void;\r\n  setIsLoading: (isLoading: boolean) => void;\r\n}\r\n\r\n\r\nconst syncToServer = async (chatList: Chat[]) => {\r\n\r\n  const resultUser = JSON.parse(sessionStorage.getItem(\"resultUser\") || '{}');\r\n  const userId = resultUser._id?.$oid;\r\n  const username = resultUser.username;\r\n  const mobileno = resultUser.mobileno;\r\n\r\n  if (!userId) return;\r\n\r\n  try {\r\n    // Step 1: Fetch existing data\r\n    const fetchRes = await fetch(`${baseUrl}/eSearch?userId=${userId}`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        \"xxxid\": uid,\r\n      },\r\n    });\r\n\r\n    const fetchData = await fetchRes.json();\r\n\r\n    // Step 2: Only delete resources that have the `chats` key\r\n    if (Array.isArray(fetchData?.source)) {\r\n      for (const item of fetchData.source) {\r\n        try {\r\n          const parsed = JSON.parse(item);\r\n          const resourceId = parsed?._id?.$oid;\r\n          const hasChats = Array.isArray(parsed?.chats);\r\n\r\n          if (resourceId && hasChats) {\r\n            await fetch(`${baseUrl}/eDelete?resourceId=${resourceId}&userId=${userId}`, {\r\n              method: \"DELETE\",\r\n              headers: {\r\n                \"Content-Type\": \"application/json\",\r\n                \"xxxid\": uid,\r\n              },\r\n            });\r\n          }\r\n        } catch (err) {\r\n          console.warn(\"Skipping invalid or non-chat item:\", item);\r\n        }\r\n      }\r\n    }\r\n\r\n    // Step 3: Save new chat list with extra user info\r\n    const bodyData = {\r\n      chats: chatList,\r\n      username: username || \"\",      // fallback to empty string if missing\r\n      mobileno: mobileno || \"\",      // fallback to empty string if missing\r\n    };\r\n\r\n    await fetch(`${baseUrl}/eCreate?userId=${userId}`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        \"xxxid\": uid,\r\n      },\r\n      body: JSON.stringify(bodyData),\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Failed to sync chats to server:\", error);\r\n  }\r\n};\r\n\r\n\r\nexport const useChatHandler = create<ChatState>()((set, get) => ({\r\n  chatList: [],\r\n  userQuery: \"\",\r\n  isLoading: false,\r\n  isAnimation: false,\r\n\r\n\r\n  updateChatList: async () => {\r\n    // Only run on client side to prevent hydration errors\r\n    if (typeof window === 'undefined') return;\r\n\r\n    const resultUser = JSON.parse(sessionStorage.getItem(\"resultUser\") || '{}');\r\n    const userId = resultUser._id?.$oid;\r\n    if (!userId) return;\r\n\r\n    try {\r\n      const response = await fetch(`${baseUrl}/eSearch?userId=${userId}`, {\r\n        method: \"GET\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          \"xxxid\": uid,\r\n        },\r\n      });\r\n\r\n      const data = await response.json();\r\n      const seenChatIds = new Set();\r\n      const chatList: Chat[] = [];\r\n\r\n      if (Array.isArray(data?.source)) {\r\n        for (const item of data.source) {\r\n          try {\r\n            const parsed = JSON.parse(item);\r\n            const chats = parsed?.chats;\r\n\r\n            if (Array.isArray(chats)) {\r\n              for (const chat of chats) {\r\n                if (!seenChatIds.has(chat.id)) {\r\n                  seenChatIds.add(chat.id);\r\n\r\n                  // Here is the key fix:\r\n                  const processedMessages = (chat.messages || []).map((msg: Message) => {\r\n                    if (!msg.isUser && msg.text && typeof msg.text === \"object\" && \"ai_response\" in msg.text) {\r\n                      // Preserve the full structured AI response object\r\n                      return {\r\n                        ...msg,\r\n                        text: {\r\n                          ai_response: msg.text.ai_response,\r\n                          sentence_analysis: msg.text.sentence_analysis || [],\r\n                          related_questions: msg.text.related_questions || [],\r\n                        },\r\n                      };\r\n                    } else {\r\n                      // Leave user messages or plain strings unchanged\r\n                      return msg;\r\n                    }\r\n                  });\r\n\r\n\r\n                  chatList.push({\r\n                    id: chat.id,\r\n                    title: chat.title || \"Untitled\",\r\n                    createdAt: chat.createdAt || new Date().toISOString(),\r\n                    messages: processedMessages,\r\n                    indexUsed: chat.indexUsed || 'default', // Preserve the index used for this chat\r\n                  });\r\n                }\r\n              }\r\n            }\r\n          } catch (err) {\r\n            console.warn(\"Failed to parse one chat source item:\", item);\r\n          }\r\n        }\r\n      }\r\n\r\n      set((state) => {\r\n        // Preserve any recently added chats that might not be on the server yet\r\n        const serverChatIds = new Set(chatList.map(chat => chat.id));\r\n        const recentLocalChats = state.chatList.filter(chat => {\r\n          // Keep chats that are not on the server and were created in the last 5 minutes\r\n          if (serverChatIds.has(chat.id)) return false;\r\n\r\n          const chatAge = Date.now() - new Date(chat.createdAt).getTime();\r\n          return chatAge < 5 * 60 * 1000; // 5 minutes\r\n        });\r\n\r\n        // Merge server chats with recent local chats\r\n        const mergedChatList = [...recentLocalChats, ...chatList];\r\n\r\n        return {\r\n          ...state,\r\n          chatList: mergedChatList,\r\n        };\r\n      });\r\n    } catch (err) {\r\n      console.error(\"Failed to fetch chat data:\", err);\r\n    }\r\n  },\r\n\r\n\r\n\r\n  handleSubmit: (userQuery, chatId, aiResponse, options = {}) => {\r\n    const timestamp = new Date().toISOString();\r\n    let messageText: string;\r\n    let sentenceAnalysis = [];\r\n    let relatedQuestions = [];\r\n\r\n    // Get the current index being used\r\n    const currentIndex = options?.index ||\r\n      (typeof window !== 'undefined' ? (localStorage.getItem('faiss_index_name') || localStorage.getItem('selectedFaissIndex')) : null) ||\r\n      'default';\r\n\r\n    // Store the selected index in localStorage if provided in options\r\n    if (options?.index && typeof window !== 'undefined') {\r\n      localStorage.setItem('faiss_index_name', options.index);\r\n      localStorage.setItem('selectedFaissIndex', options.index);\r\n      console.log(`Stored selected index in localStorage: ${options.index}`);\r\n\r\n      // If this is just an index selection with no real query, return early without creating a message\r\n      if (userQuery === \"text\" || !userQuery) {\r\n        console.log(\"Skipping message creation for index selection\");\r\n        return;\r\n      }\r\n    }\r\n\r\n    if (aiResponse === undefined || aiResponse === null) {\r\n      messageText = \"Sorry, there was an issue with the response.\";\r\n    } else if (typeof aiResponse === \"string\") {\r\n      messageText = aiResponse;\r\n    } else if (typeof aiResponse === \"object\") {\r\n      if (\"sentence_analysis\" in aiResponse && Array.isArray(aiResponse.sentence_analysis)) {\r\n        sentenceAnalysis = aiResponse.sentence_analysis;\r\n      }\r\n      if (\"related_questions\" in aiResponse && Array.isArray(aiResponse.related_questions)) {\r\n        relatedQuestions = aiResponse.related_questions;\r\n      }\r\n\r\n      if (\"ai_response\" in aiResponse) {\r\n        const response = aiResponse.ai_response;\r\n        messageText = typeof response === \"string\" ? response : JSON.stringify(response);\r\n      } else {\r\n        try {\r\n          messageText = JSON.stringify(aiResponse);\r\n        } catch (e) {\r\n          messageText = \"Error: Could not process the response.\";\r\n        }\r\n      }\r\n    } else {\r\n      messageText = String(aiResponse);\r\n    }\r\n\r\n    set((state) => {\r\n      const existingChatIndex = state.chatList.findIndex((chat) => chat.id === chatId);\r\n\r\n      if (existingChatIndex !== -1) {\r\n        const updatedChatList = [...state.chatList];\r\n        const messages = updatedChatList[existingChatIndex].messages;\r\n        const loadingMessageIndex = messages.findIndex((msg) => !msg.isUser && msg.text === \"__LOADING__\");\r\n\r\n        if (loadingMessageIndex !== -1) {\r\n          const loadingMessage = messages[loadingMessageIndex];\r\n          messages[loadingMessageIndex] = {\r\n            isUser: false,\r\n            text: {\r\n              ai_response: messageText,\r\n              sentence_analysis: sentenceAnalysis,\r\n              related_questions: relatedQuestions,\r\n            },\r\n            timestamp,\r\n            messageId: loadingMessage.messageId,\r\n          };\r\n        } else {\r\n          messages.push({\r\n            isUser: false,\r\n            text: {\r\n              ai_response: messageText,\r\n              sentence_analysis: sentenceAnalysis,\r\n              related_questions: relatedQuestions,\r\n            },\r\n            timestamp,\r\n          });\r\n        }\r\n\r\n        const newState = {\r\n          ...state,\r\n          chatList: updatedChatList,\r\n        };\r\n        syncToServer(newState.chatList);\r\n        return newState;\r\n      } else {\r\n        const chatTitle = userQuery\r\n          ? userQuery.slice(0, 30) + (userQuery.length > 30 ? \"...\" : \"\")\r\n          : \"New Chat\";\r\n\r\n        const newChat: Chat = {\r\n          id: chatId,\r\n          title: chatTitle,\r\n          createdAt: timestamp,\r\n          indexUsed: currentIndex, // Store the index used for this chat\r\n          messages: [\r\n            {\r\n              isUser: false,\r\n              text: {\r\n                ai_response: messageText,\r\n                sentence_analysis: sentenceAnalysis,\r\n                related_questions: relatedQuestions,\r\n              },\r\n              timestamp,\r\n            },\r\n          ],\r\n        };\r\n\r\n        const newState = {\r\n          ...state,\r\n          chatList: [newChat, ...state.chatList],\r\n        };\r\n        syncToServer(newState.chatList);\r\n        return newState;\r\n      }\r\n    });\r\n  },\r\n\r\n\r\n\r\n\r\n  addMessage: (message, chatId) => {\r\n    set((state) => {\r\n      const existingChatIndex = state.chatList.findIndex((chat) => chat.id === chatId);\r\n\r\n      if (existingChatIndex !== -1) {\r\n        const updatedChatList = [...state.chatList];\r\n        updatedChatList[existingChatIndex].messages.push(message);\r\n\r\n        return {\r\n          ...state,\r\n          chatList: updatedChatList,\r\n        };\r\n      } else {\r\n        // Get the current index for new chats\r\n        const currentIndex = typeof window !== 'undefined'\r\n          ? localStorage.getItem('faiss_index_name') || localStorage.getItem('selectedFaissIndex') || 'default'\r\n          : 'default';\r\n\r\n        const newChat: Chat = {\r\n          id: chatId,\r\n          title:\r\n            typeof message.text === \"string\"\r\n              ? message.text.slice(0, 30) + (message.text.length > 30 ? \"...\" : \"\")\r\n              : \"New Chat\",\r\n          createdAt: message.timestamp,\r\n          indexUsed: currentIndex, // Store the index used for this chat\r\n          messages: [message],\r\n        };\r\n\r\n        return {\r\n          ...state,\r\n          chatList: [newChat, ...state.chatList],\r\n        };\r\n      }\r\n    });\r\n  },\r\n\r\n\r\n  addChat: (chat) => {\r\n    set((state) => {\r\n      // Check if chat already exists to avoid duplicates\r\n      const existingChatIndex = state.chatList.findIndex(existingChat => existingChat.id === chat.id);\r\n\r\n      let newChatList;\r\n      if (existingChatIndex !== -1) {\r\n        // Update existing chat\r\n        newChatList = [...state.chatList];\r\n        newChatList[existingChatIndex] = chat;\r\n      } else {\r\n        // Add new chat to the beginning\r\n        newChatList = [chat, ...state.chatList];\r\n      }\r\n\r\n      const newState = {\r\n        ...state,\r\n        chatList: newChatList,\r\n      };\r\n\r\n      // Sync to server asynchronously\r\n      syncToServer(newState.chatList).catch(error => {\r\n        console.error(\"Failed to sync new chat to server:\", error);\r\n      });\r\n\r\n      return newState;\r\n    });\r\n  },\r\n\r\n  setUserQuery: (query) => set({ userQuery: query }),\r\n  setIsLoading: (isLoading) => set({ isLoading }),\r\n}));\r\n"], "names": [], "mappings": ";;;AACA;AADA;;;AAgDA,MAAM,eAAe,OAAO;IAE1B,MAAM,aAAa,KAAK,KAAK,CAAC,eAAe,OAAO,CAAC,iBAAiB;IACtE,MAAM,SAAS,WAAW,GAAG,EAAE;IAC/B,MAAM,WAAW,WAAW,QAAQ;IACpC,MAAM,WAAW,WAAW,QAAQ;IAEpC,IAAI,CAAC,QAAQ;IAEb,IAAI;QACF,8BAA8B;QAC9B,MAAM,WAAW,MAAM,MAAM,GAAG,4HAAA,CAAA,UAAO,CAAC,gBAAgB,EAAE,QAAQ,EAAE;YAClE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,SAAS,4HAAA,CAAA,MAAG;YACd;QACF;QAEA,MAAM,YAAY,MAAM,SAAS,IAAI;QAErC,0DAA0D;QAC1D,IAAI,MAAM,OAAO,CAAC,WAAW,SAAS;YACpC,KAAK,MAAM,QAAQ,UAAU,MAAM,CAAE;gBACnC,IAAI;oBACF,MAAM,SAAS,KAAK,KAAK,CAAC;oBAC1B,MAAM,aAAa,QAAQ,KAAK;oBAChC,MAAM,WAAW,MAAM,OAAO,CAAC,QAAQ;oBAEvC,IAAI,cAAc,UAAU;wBAC1B,MAAM,MAAM,GAAG,4HAAA,CAAA,UAAO,CAAC,oBAAoB,EAAE,WAAW,QAAQ,EAAE,QAAQ,EAAE;4BAC1E,QAAQ;4BACR,SAAS;gCACP,gBAAgB;gCAChB,SAAS,4HAAA,CAAA,MAAG;4BACd;wBACF;oBACF;gBACF,EAAE,OAAO,KAAK;oBACZ,QAAQ,IAAI,CAAC,sCAAsC;gBACrD;YACF;QACF;QAEA,kDAAkD;QAClD,MAAM,WAAW;YACf,OAAO;YACP,UAAU,YAAY;YACtB,UAAU,YAAY;QACxB;QAEA,MAAM,MAAM,GAAG,4HAAA,CAAA,UAAO,CAAC,gBAAgB,EAAE,QAAQ,EAAE;YACjD,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,SAAS,4HAAA,CAAA,MAAG;YACd;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;IACnD;AACF;AAGO,MAAM,iBAAiB,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAAe,CAAC,KAAK,MAAQ,CAAC;QAC/D,UAAU,EAAE;QACZ,WAAW;QACX,WAAW;QACX,aAAa;QAGb,gBAAgB;YACd,sDAAsD;YACtD,uCAAmC;;YAAM;YAEzC,MAAM,aAAa,KAAK,KAAK,CAAC,eAAe,OAAO,CAAC,iBAAiB;YACtE,MAAM,SAAS,WAAW,GAAG,EAAE;YAC/B,IAAI,CAAC,QAAQ;YAEb,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,GAAG,4HAAA,CAAA,UAAO,CAAC,gBAAgB,EAAE,QAAQ,EAAE;oBAClE,QAAQ;oBACR,SAAS;wBACP,gBAAgB;wBAChB,SAAS,4HAAA,CAAA,MAAG;oBACd;gBACF;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,cAAc,IAAI;gBACxB,MAAM,WAAmB,EAAE;gBAE3B,IAAI,MAAM,OAAO,CAAC,MAAM,SAAS;oBAC/B,KAAK,MAAM,QAAQ,KAAK,MAAM,CAAE;wBAC9B,IAAI;4BACF,MAAM,SAAS,KAAK,KAAK,CAAC;4BAC1B,MAAM,QAAQ,QAAQ;4BAEtB,IAAI,MAAM,OAAO,CAAC,QAAQ;gCACxB,KAAK,MAAM,QAAQ,MAAO;oCACxB,IAAI,CAAC,YAAY,GAAG,CAAC,KAAK,EAAE,GAAG;wCAC7B,YAAY,GAAG,CAAC,KAAK,EAAE;wCAEvB,uBAAuB;wCACvB,MAAM,oBAAoB,CAAC,KAAK,QAAQ,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC;4CACnD,IAAI,CAAC,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI,OAAO,IAAI,IAAI,KAAK,YAAY,iBAAiB,IAAI,IAAI,EAAE;gDACxF,kDAAkD;gDAClD,OAAO;oDACL,GAAG,GAAG;oDACN,MAAM;wDACJ,aAAa,IAAI,IAAI,CAAC,WAAW;wDACjC,mBAAmB,IAAI,IAAI,CAAC,iBAAiB,IAAI,EAAE;wDACnD,mBAAmB,IAAI,IAAI,CAAC,iBAAiB,IAAI,EAAE;oDACrD;gDACF;4CACF,OAAO;gDACL,iDAAiD;gDACjD,OAAO;4CACT;wCACF;wCAGA,SAAS,IAAI,CAAC;4CACZ,IAAI,KAAK,EAAE;4CACX,OAAO,KAAK,KAAK,IAAI;4CACrB,WAAW,KAAK,SAAS,IAAI,IAAI,OAAO,WAAW;4CACnD,UAAU;4CACV,WAAW,KAAK,SAAS,IAAI;wCAC/B;oCACF;gCACF;4BACF;wBACF,EAAE,OAAO,KAAK;4BACZ,QAAQ,IAAI,CAAC,yCAAyC;wBACxD;oBACF;gBACF;gBAEA,IAAI,CAAC;oBACH,wEAAwE;oBACxE,MAAM,gBAAgB,IAAI,IAAI,SAAS,GAAG,CAAC,CAAA,OAAQ,KAAK,EAAE;oBAC1D,MAAM,mBAAmB,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAA;wBAC7C,+EAA+E;wBAC/E,IAAI,cAAc,GAAG,CAAC,KAAK,EAAE,GAAG,OAAO;wBAEvC,MAAM,UAAU,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,SAAS,EAAE,OAAO;wBAC7D,OAAO,UAAU,IAAI,KAAK,MAAM,YAAY;oBAC9C;oBAEA,6CAA6C;oBAC7C,MAAM,iBAAiB;2BAAI;2BAAqB;qBAAS;oBAEzD,OAAO;wBACL,GAAG,KAAK;wBACR,UAAU;oBACZ;gBACF;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,8BAA8B;YAC9C;QACF;QAIA,cAAc,CAAC,WAAW,QAAQ,YAAY,UAAU,CAAC,CAAC;YACxD,MAAM,YAAY,IAAI,OAAO,WAAW;YACxC,IAAI;YACJ,IAAI,mBAAmB,EAAE;YACzB,IAAI,mBAAmB,EAAE;YAEzB,mCAAmC;YACnC,MAAM,eAAe,SAAS,SAC5B,CAAC,uCAAiC,aAAa,OAAO,CAAC,uBAAuB,aAAa,OAAO,CAAC,4DAA6B,KAChI;YAEF,kEAAkE;YAClE,IAAI,SAAS,SAAS,aAAkB,aAAa;gBACnD,aAAa,OAAO,CAAC,oBAAoB,QAAQ,KAAK;gBACtD,aAAa,OAAO,CAAC,sBAAsB,QAAQ,KAAK;gBACxD,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,QAAQ,KAAK,EAAE;gBAErE,iGAAiG;gBACjG,IAAI,cAAc,UAAU,CAAC,WAAW;oBACtC,QAAQ,GAAG,CAAC;oBACZ;gBACF;YACF;YAEA,IAAI,eAAe,aAAa,eAAe,MAAM;gBACnD,cAAc;YAChB,OAAO,IAAI,OAAO,eAAe,UAAU;gBACzC,cAAc;YAChB,OAAO,IAAI,OAAO,eAAe,UAAU;gBACzC,IAAI,uBAAuB,cAAc,MAAM,OAAO,CAAC,WAAW,iBAAiB,GAAG;oBACpF,mBAAmB,WAAW,iBAAiB;gBACjD;gBACA,IAAI,uBAAuB,cAAc,MAAM,OAAO,CAAC,WAAW,iBAAiB,GAAG;oBACpF,mBAAmB,WAAW,iBAAiB;gBACjD;gBAEA,IAAI,iBAAiB,YAAY;oBAC/B,MAAM,WAAW,WAAW,WAAW;oBACvC,cAAc,OAAO,aAAa,WAAW,WAAW,KAAK,SAAS,CAAC;gBACzE,OAAO;oBACL,IAAI;wBACF,cAAc,KAAK,SAAS,CAAC;oBAC/B,EAAE,OAAO,GAAG;wBACV,cAAc;oBAChB;gBACF;YACF,OAAO;gBACL,cAAc,OAAO;YACvB;YAEA,IAAI,CAAC;gBACH,MAAM,oBAAoB,MAAM,QAAQ,CAAC,SAAS,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;gBAEzE,IAAI,sBAAsB,CAAC,GAAG;oBAC5B,MAAM,kBAAkB;2BAAI,MAAM,QAAQ;qBAAC;oBAC3C,MAAM,WAAW,eAAe,CAAC,kBAAkB,CAAC,QAAQ;oBAC5D,MAAM,sBAAsB,SAAS,SAAS,CAAC,CAAC,MAAQ,CAAC,IAAI,MAAM,IAAI,IAAI,IAAI,KAAK;oBAEpF,IAAI,wBAAwB,CAAC,GAAG;wBAC9B,MAAM,iBAAiB,QAAQ,CAAC,oBAAoB;wBACpD,QAAQ,CAAC,oBAAoB,GAAG;4BAC9B,QAAQ;4BACR,MAAM;gCACJ,aAAa;gCACb,mBAAmB;gCACnB,mBAAmB;4BACrB;4BACA;4BACA,WAAW,eAAe,SAAS;wBACrC;oBACF,OAAO;wBACL,SAAS,IAAI,CAAC;4BACZ,QAAQ;4BACR,MAAM;gCACJ,aAAa;gCACb,mBAAmB;gCACnB,mBAAmB;4BACrB;4BACA;wBACF;oBACF;oBAEA,MAAM,WAAW;wBACf,GAAG,KAAK;wBACR,UAAU;oBACZ;oBACA,aAAa,SAAS,QAAQ;oBAC9B,OAAO;gBACT,OAAO;oBACL,MAAM,YAAY,YACd,UAAU,KAAK,CAAC,GAAG,MAAM,CAAC,UAAU,MAAM,GAAG,KAAK,QAAQ,EAAE,IAC5D;oBAEJ,MAAM,UAAgB;wBACpB,IAAI;wBACJ,OAAO;wBACP,WAAW;wBACX,WAAW;wBACX,UAAU;4BACR;gCACE,QAAQ;gCACR,MAAM;oCACJ,aAAa;oCACb,mBAAmB;oCACnB,mBAAmB;gCACrB;gCACA;4BACF;yBACD;oBACH;oBAEA,MAAM,WAAW;wBACf,GAAG,KAAK;wBACR,UAAU;4BAAC;+BAAY,MAAM,QAAQ;yBAAC;oBACxC;oBACA,aAAa,SAAS,QAAQ;oBAC9B,OAAO;gBACT;YACF;QACF;QAKA,YAAY,CAAC,SAAS;YACpB,IAAI,CAAC;gBACH,MAAM,oBAAoB,MAAM,QAAQ,CAAC,SAAS,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;gBAEzE,IAAI,sBAAsB,CAAC,GAAG;oBAC5B,MAAM,kBAAkB;2BAAI,MAAM,QAAQ;qBAAC;oBAC3C,eAAe,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC;oBAEjD,OAAO;wBACL,GAAG,KAAK;wBACR,UAAU;oBACZ;gBACF,OAAO;oBACL,sCAAsC;oBACtC,MAAM,eAAe,uCACjB,aAAa,OAAO,CAAC,uBAAuB,aAAa,OAAO,CAAC,yBAAyB;oBAG9F,MAAM,UAAgB;wBACpB,IAAI;wBACJ,OACE,OAAO,QAAQ,IAAI,KAAK,WACpB,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,QAAQ,IAAI,CAAC,MAAM,GAAG,KAAK,QAAQ,EAAE,IAClE;wBACN,WAAW,QAAQ,SAAS;wBAC5B,WAAW;wBACX,UAAU;4BAAC;yBAAQ;oBACrB;oBAEA,OAAO;wBACL,GAAG,KAAK;wBACR,UAAU;4BAAC;+BAAY,MAAM,QAAQ;yBAAC;oBACxC;gBACF;YACF;QACF;QAGA,SAAS,CAAC;YACR,IAAI,CAAC;gBACH,mDAAmD;gBACnD,MAAM,oBAAoB,MAAM,QAAQ,CAAC,SAAS,CAAC,CAAA,eAAgB,aAAa,EAAE,KAAK,KAAK,EAAE;gBAE9F,IAAI;gBACJ,IAAI,sBAAsB,CAAC,GAAG;oBAC5B,uBAAuB;oBACvB,cAAc;2BAAI,MAAM,QAAQ;qBAAC;oBACjC,WAAW,CAAC,kBAAkB,GAAG;gBACnC,OAAO;oBACL,gCAAgC;oBAChC,cAAc;wBAAC;2BAAS,MAAM,QAAQ;qBAAC;gBACzC;gBAEA,MAAM,WAAW;oBACf,GAAG,KAAK;oBACR,UAAU;gBACZ;gBAEA,gCAAgC;gBAChC,aAAa,SAAS,QAAQ,EAAE,KAAK,CAAC,CAAA;oBACpC,QAAQ,KAAK,CAAC,sCAAsC;gBACtD;gBAEA,OAAO;YACT;QACF;QAEA,cAAc,CAAC,QAAU,IAAI;gBAAE,WAAW;YAAM;QAChD,cAAc,CAAC,YAAc,IAAI;gBAAE;YAAU;IAC/C,CAAC"}}, {"offset": {"line": 408, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 419, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/logo5.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 223, height: 54, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAACCAYAAABllJ3tAAAATUlEQVR42gFCAL3/ACl4mJwhZ4GHBAoLTAEBAUcBAQFCAQEBTAEBAUcBAQEhABmVo54ajqCgBhMVOAUEBTcFBQY0BgUHMwUFBjAFBAUhnm0KkfiOWmoAAAAASUVORK5CYII=\", blurWidth: 8, blurHeight: 2 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,yHAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAI,aAAa;IAA8M,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 435, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 446, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/logo6.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 500, height: 113, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAACCAYAAABllJ3tAAAATUlEQVR42gFCAL3/AAIlLzIGV3aAJjlBQTMzMzAsLCwpNjY2My8vLywKCgoJAAEgJSgDY3J6HTY8OzU1NS4vLy8oLy8vKC0tLSYJCQkIlTgLefW61qsAAAAASUVORK5CYII=\", blurWidth: 8, blurHeight: 2 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,yHAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAA8M,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 462, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 473, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/explore-article-icon-12.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 80, height: 80, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,oJAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAsO,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 489, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 500, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/explore-article-icon-14.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 80, height: 80, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,oJAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAsO,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 516, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 527, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/explore-article-icon-1.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 80, height: 80, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,mJAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAsO,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 543, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 554, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/explore-article-icon-2.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 80, height: 80, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,mJAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAsO,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 570, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 581, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/explore-article-icon-3.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 80, height: 80, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,mJAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAsO,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 597, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 608, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/explore-article-icon-4.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 80, height: 80, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,mJAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAsO,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 624, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 635, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/explore-article-icon-5.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 80, height: 80, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,mJAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAsO,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 651, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 662, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/explore-article-icon-6.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 80, height: 80, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,mJAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAsO,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 678, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 689, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/explore-article-icon-7.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 80, height: 80, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,mJAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAsO,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 705, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 716, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/explore-article-icon-8.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 80, height: 80, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,mJAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAsO,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 732, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 743, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/explore-article-icon-9.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 80, height: 80, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,mJAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAsO,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 759, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 770, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/explore-article-icon-10.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 80, height: 80, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,oJAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAsO,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 786, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 797, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/explore-article-icon-11.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 80, height: 80, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,oJAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAsO,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 813, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 824, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/explore-article-icon-13.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 80, height: 80, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,oJAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAsO,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 840, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 851, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/explore-article-icon-15.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 80, height: 80, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,oJAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAsO,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 867, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 878, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/explore-article-icon-16.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 80, height: 80, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,oJAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAsO,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 894, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 905, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/explore-article-icon-17.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 80, height: 80, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,oJAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAsO,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 921, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 932, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/explore-article-icon-18.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 80, height: 80, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,oJAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAsO,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 948, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 959, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/explore-article-icon-19.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 80, height: 80, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,oJAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAsO,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 975, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 986, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/explore-article-icon-20.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 80, height: 80, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,oJAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAsO,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 1002, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1013, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/explore-article-icon-21.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 80, height: 80, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,oJAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAsO,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 1029, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1040, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/explore-article-icon-22.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 80, height: 80, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,oJAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAsO,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 1056, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1067, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/explore-article-icon-23.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 80, height: 80, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,oJAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAsO,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 1083, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1094, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/explore-article-icon-24.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 80, height: 80, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,oJAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAsO,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 1110, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1121, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/explore-article-icon-25.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 80, height: 80, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAYElEQVR42pWOOwoAIQxEc2BLwUL8FYJXUURdSLo93A5rbeEU4TEPwtB7CB1FrXWt1Xt//mxorZFSynufc7bWllKMMc45NKS1TilBhBAgUMUYwTTGYGZ8E5ENuHNOul71AZTzfxuSYEpKAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,oJAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAsO,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 1137, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1148, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/figma.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 40, height: 40, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAbElEQVR42pXOKw7AIBRE0bdWHMGBQKD4KRQCBU1IYAEQaBB0cW3qK3rEmGsGrg/wGXLOx6uUUmudc6aUYoyAMaaUWmuFEFLKEAJCyDkHnHPGmNZaKfVk7z0hxBgDa60xRu/9fO29n22twe9XN/LefgqFd4GsAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,yHAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAsP,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 1164, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1175, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/shopify.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 40, height: 40, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAAyUlEQVR42k3PzQsBYRAG8P3XHBU3Lq5uznJSSnIjKSkOlJODpOQrIkVWkiwlRPJRSKu07LL7Pvbdza6ZyzTzO8zDsCybJYSIUEshMojatOiO3pjf8S2/sL50sTp38ZQeBmLo8FEkcIcSShMfUh0X0o0gBFFHGuCFPcpTP4bbHBJNJ7wZB878wQSn+wwVLoDJvoBozQZP0orlkTPB5tpDfR7WQKRqhztuwWDR0gF9RJBuKupjvMujOAoh145hd1npT/7HNKISYsT8AoRH4XdZ+up6AAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,2HAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAkX,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 1191, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1202, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/slack.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 40, height: 40, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAbElEQVR42pXOKw7AIBRE0bdWHMGBQKD4KRQCBU1IYAEQaBB0cW3qK3rEmGsGrg/wGXLOx6uUUmudc6aUYoyAMaaUWmuFEFLKEAJCyDkHnHPGmNZaKfVk7z0hxBgDa60xRu/9fO29n22twe9XN/LefgqFd4GsAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,yHAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAsP,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 1218, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1229, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/webflow.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 40, height: 40, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAbElEQVR42pXOKw7AIBRE0bdWHMGBQKD4KRQCBU1IYAEQaBB0cW3qK3rEmGsGrg/wGXLOx6uUUmudc6aUYoyAMaaUWmuFEFLKEAJCyDkHnHPGmNZaKfVk7z0hxBgDa60xRu/9fO29n22twe9XN/LefgqFd4GsAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,2HAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAsP,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 1245, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1256, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/airtable.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 40, height: 40, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAbElEQVR42pXOKw7AIBRE0bdWHMGBQKD4KRQCBU1IYAEQaBB0cW3qK3rEmGsGrg/wGXLOx6uUUmudc6aUYoyAMaaUWmuFEFLKEAJCyDkHnHPGmNZaKfVk7z0hxBgDa60xRu/9fO29n22twe9XN/LefgqFd4GsAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,4HAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAsP,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 1272, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1283, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/monday.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 40, height: 40, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAbElEQVR42pXOKw7AIBRE0bdWHMGBQKD4KRQCBU1IYAEQaBB0cW3qK3rEmGsGrg/wGXLOx6uUUmudc6aUYoyAMaaUWmuFEFLKEAJCyDkHnHPGmNZaKfVk7z0hxBgDa60xRu/9fO29n22twe9XN/LefgqFd4GsAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,0HAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAsP,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 1299, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1310, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/video-preview-img-2.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 408, height: 399, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAATklEQVR42pXOMQoAMQgEQL8eEkIQFPU1Ue5xdygpU9wUFi4sC88FXAN338nTPqC1xsy9dyIyM0Rca805oV5jjApERFW/CxFRDX5Egt+rXjd5gy/9DpGeAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,gJAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAA8M,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 1326, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1337, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/generate-photo-1.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 320, height: 320, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAQklEQVR42pXOSwoAIAgE0Lk/fhI8TEWHS4yiTYveSmdAxHjAs2hbT2cFEZkZM4uIu6tqrDEjIk2nKAk13adWgu+vJps/hBOmIwkvAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,0IAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAA8L,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 1353, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1364, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/generate-photo-2.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 320, height: 320, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAQklEQVR42pXOSwoAIAgE0Lk/fhI8TEWHS4yiTYveSmdAxHjAs2hbT2cFEZkZM4uIu6tqrDEjIk2nKAk13adWgu+vJps/hBOmIwkvAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,0IAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAA8L,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 1380, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1391, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/generate-photo-3.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 320, height: 320, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAQklEQVR42pXOSwoAIAgE0Lk/fhI8TEWHS4yiTYveSmdAxHjAs2hbT2cFEZkZM4uIu6tqrDEjIk2nKAk13adWgu+vJps/hBOmIwkvAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,0IAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAA8L,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 1407, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1418, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/generate-photo-4.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 320, height: 320, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAIAAABLbSncAAAAQklEQVR42pXOSwoAIAgE0Lk/fhI8TEWHS4yiTYveSmdAxHjAs2hbT2cFEZkZM4uIu6tqrDEjIk2nKAk13adWgu+vJps/hBOmIwkvAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,0IAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAA8L,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 1434, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1445, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/adjust-photo-modal.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 393, height: 593, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAICAIAAAC+k6JsAAAALklEQVR42pXMsREAIAxCUfbfICBM4nB66ay8vAqaj/3C71fVaiQlwXYStzsw7B0ZKFRk366MsAAAAABJRU5ErkJggg==\", blurWidth: 5, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,4IAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAAsK,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 1461, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1472, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/logodefault.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 58, height: 54, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAHCAYAAAA1WQxeAAAA8klEQVR42gHnABj/AAEBAgEUJTAqN2uRkkWOxM8/hre8IUpiWwUKDQkAAAAAABIpMy04kL3HPqnj+j2l2+48qeD1Oqnd7yJfdm8DBgcEACqAnJg0uOf8MI2vqhQ0QTceVGteMLbh6i2v1NwPKzMpACyryMMqx+36IGBtWwABAQAKGx4TKLLPyyfK6vMVSVNJACGVpJke1/L9Ia6+tRdUWkklcoBzMLrh7iPF3+QONjovAA44OiwZws3HE+f2/BXm9fUc3fL6Kcjt/h/Q6e8RXGFRAAECAgEMNjcrE6CklhHQ1McSwcW2GZCbjxnE0ssPZmpRi6piXKCTQHwAAAAASUVORK5CYII=\", blurWidth: 8, blurHeight: 7 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,+HAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAA0a,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 1488, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1499, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/upgrade-header.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 596, height: 256, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAADCAIAAAAhqtkfAAAAP0lEQVR42jWKwQnAAAgDXVzEjz7ENcQqOmOl0HuEHAlERFXNzO529/NxBYiImRFRVc3M3UXkFG6/e/2cXmbmC3yzLEQxtacRAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 3 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,qIAAA,CAAA,UAAG;IAAE,OAAO;IAAK,QAAQ;IAAK,aAAa;IAA0L,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 1515, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1526, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/public/images/create-new-bot-img.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 64, height: 64, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAAn0lEQVR42oWPOQ6CUBiEX+A2Np7BwgNRG60N7lsMcV/iHWiw9gi2GnchVLQ/73sJhI5iksnM/JP5lWXZdqVarzlN/9zx7omroXmAhqcgjfbl2l08pAg0POW0/ACht3zKYP2W0fYrw83HhGhVrndL+quXTA+hjHc/g8n+L2h4JsD17BjJ/BSbIA15gBrquCQE4AQYWz4yf1OPpRLQmr2ZAgVLn2v4A+29AAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,+IAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAA0T,WAAW;IAAG,YAAY;AAAE"}}, {"offset": {"line": 1542, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1548, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/constants/data.ts"], "sourcesContent": ["import {\r\n  PiAirplaneTilt,\r\n  PiClipboardText,\r\n  PiCode,\r\n  PiCodeFill,\r\n  PiEnvelopeSimple,\r\n  PiFileText,\r\n  PiFire,\r\n  PiGear,\r\n  PiGraduationCap,\r\n  PiGraduationCapFill,\r\n  PiImage,\r\n  PiImageFill,\r\n  PiImageSquare,\r\n  PiImageSquareFill,\r\n  PiLaptop,\r\n  PiLock,\r\n  PiLockKey,\r\n  PiMagicWand,\r\n  PiMegaphone,\r\n  PiMoon,\r\n  PiPaintBrushHousehold,\r\n  PiPaperPlaneTilt,\r\n  PiPath,\r\n  PiPencilSimpleLine,\r\n  PiQuestion,\r\n  PiRobot,\r\n  PiRocketLaunch,\r\n  PiSpeakerHigh,\r\n  PiSpeakerHighFill,\r\n  PiSun,\r\n  PiVideo,\r\n  PiVideoCameraFill,\r\n} from \"react-icons/pi\";\r\nimport { v4 as uuidv4 } from \"uuid\";\r\n\r\n//icons\r\nimport icon1 from \"@/public/images/explore-article-icon-1.png\";\r\nimport icon2 from \"@/public/images/explore-article-icon-2.png\";\r\nimport icon3 from \"@/public/images/explore-article-icon-3.png\";\r\nimport icon4 from \"@/public/images/explore-article-icon-4.png\";\r\nimport icon5 from \"@/public/images/explore-article-icon-5.png\";\r\nimport icon6 from \"@/public/images/explore-article-icon-6.png\";\r\nimport icon7 from \"@/public/images/explore-article-icon-7.png\";\r\nimport icon8 from \"@/public/images/explore-article-icon-8.png\";\r\nimport icon9 from \"@/public/images/explore-article-icon-9.png\";\r\nimport icon10 from \"@/public/images/explore-article-icon-10.png\";\r\nimport icon11 from \"@/public/images/explore-article-icon-11.png\";\r\nimport icon12 from \"@/public/images/explore-article-icon-12.png\";\r\nimport icon13 from \"@/public/images/explore-article-icon-13.png\";\r\nimport icon14 from \"@/public/images/explore-article-icon-14.png\";\r\nimport icon15 from \"@/public/images/explore-article-icon-15.png\";\r\nimport icon16 from \"@/public/images/explore-article-icon-16.png\";\r\nimport icon17 from \"@/public/images/explore-article-icon-17.png\";\r\nimport icon18 from \"@/public/images/explore-article-icon-18.png\";\r\nimport icon19 from \"@/public/images/explore-article-icon-19.png\";\r\nimport icon20 from \"@/public/images/explore-article-icon-20.png\";\r\nimport icon21 from \"@/public/images/explore-article-icon-21.png\";\r\nimport icon22 from \"@/public/images/explore-article-icon-22.png\";\r\nimport icon23 from \"@/public/images/explore-article-icon-23.png\";\r\nimport icon24 from \"@/public/images/explore-article-icon-24.png\";\r\nimport icon25 from \"@/public/images/explore-article-icon-25.png\";\r\n\r\n//integration icons\r\nimport figma from \"@/public/images/figma.png\";\r\nimport shopify from \"@/public/images/shopify.png\";\r\nimport slack from \"@/public/images/slack.png\";\r\nimport webflow from \"@/public/images/webflow.png\";\r\nimport airtable from \"@/public/images/airtable.png\";\r\nimport monday from \"@/public/images/monday.png\";\r\n\r\nexport const chatOptions = [\r\n  {\r\n    id: uuidv4(),\r\n    name: \"Image Generator\",\r\n    label: \"image\",\r\n    icon: PiImage,\r\n    color: \"77, 107, 254\",\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    name: \"Video Generator\",\r\n    label: \"video\",\r\n    icon: PiVideo,\r\n    color: \"142, 51, 255\",\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    name: \"Audio Generator\",\r\n    label: \"audio\",\r\n    icon: PiSpeakerHigh,\r\n    color: \"255, 86, 48\",\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    name: \"Photo Editor\",\r\n    label: \"retouch\",\r\n    icon: PiImageSquare,\r\n    color: \"255, 171, 0\",\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    name: \"Education Feedback\",\r\n    label: \"data-table\",\r\n    icon: PiGraduationCap,\r\n    color: \"34, 197, 94\",\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    name: \"Get Advice\",\r\n    label: \"text\",\r\n    icon: PiMegaphone,\r\n    color: \"255, 86, 48\",\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    name: \"Code Generator\",\r\n    label: \"code\",\r\n    icon: PiCode,\r\n    color: \"34, 197, 94\",\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    name: \"Help me write\",\r\n    label: \"text\",\r\n    icon: PiPencilSimpleLine,\r\n    color: \"77, 107, 254\",\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    name: \"Summarize text\",\r\n    label: \"text\",\r\n    icon: PiMagicWand,\r\n    color: \"255, 86, 48\",\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    name: \"Problem solving\",\r\n    label: \"code\",\r\n    icon: PiQuestion,\r\n    color: \"142, 51, 255\",\r\n  },\r\n];\r\n\r\nexport const aiGeneratorOptions = [\r\n  {\r\n    id: uuidv4(),\r\n    name: \"Video Generator\",\r\n    icon: PiVideoCameraFill,\r\n    color: \"142, 51, 255\",\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    name: \"Photo Generator\",\r\n    icon: PiImageFill,\r\n    color: \"255, 86, 48\",\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    name: \" Photo Editor\",\r\n    icon: PiImageSquareFill,\r\n    color: \"77, 107, 254\",\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    name: \"Code Generator\",\r\n    icon: PiCodeFill,\r\n    color: \"34, 197, 94\",\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    name: \"Education Feedback\",\r\n    icon: PiGraduationCapFill,\r\n    color: \"255, 171, 0\",\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    name: \"Audio Generator\",\r\n    icon: PiSpeakerHighFill,\r\n    color: \"255, 86, 48\",\r\n  },\r\n];\r\n\r\nexport const upgradePlanDetails = [\r\n  {\r\n    id: uuidv4(),\r\n    name: \"Basic\",\r\n    icon: PiPaperPlaneTilt,\r\n    price: 0,\r\n    features: [\r\n      \"Access to AIQuill mini and reasoning\",\r\n      \"Standard voice mode\",\r\n      \"Real-time data from the web with search\",\r\n      \"Limited access to AIQuill\",\r\n    ],\r\n    notProvidedFeatures: [\r\n      \"Limited access to file uploads, data analysis, and image generation\",\r\n      \"Use custom AIQuill\",\r\n    ],\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    name: \"Standard\",\r\n    icon: PiAirplaneTilt,\r\n    price: 59,\r\n    features: [\r\n      \"Access to AIQuill mini and reasoning\",\r\n      \"Standard voice mode\",\r\n      \"Real-time data from the web with search\",\r\n      \"Limited access to AIQuill\",\r\n      \"Limited access to file uploads, data analysis, and image generation\",\r\n    ],\r\n    notProvidedFeatures: [\"Use custom AIQuill\"],\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    name: \"Premium\",\r\n    icon: PiRocketLaunch,\r\n    price: 99,\r\n    features: [\r\n      \"Access to AIQuill mini and reasoning\",\r\n      \"Standard voice mode\",\r\n      \"Real-time data from the web with search\",\r\n      \"Limited access to AIQuill\",\r\n      \"Limited access to file uploads, data analysis, and image generation\",\r\n      \"Use custom AIQuill\",\r\n    ],\r\n    notProvidedFeatures: [],\r\n  },\r\n];\r\n\r\nexport const faqData = [\r\n  {\r\n    id: uuidv4(),\r\n    question: \"What is AIQuill?\",\r\n    answer:\r\n      \"Yes! With AIQuill, you can design and customize your chatbot, set response patterns, and integrate it into websites or applications.\",\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    question: \"How does AIQuill work?\",\r\n    answer:\r\n      \"Yes! With AIQuill, you can design and customize your chatbot, set response patterns, and integrate it into websites or applications.\",\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    question: \"Can I create my own AI chatbot?\",\r\n    answer:\r\n      \"Yes! With AIQuill, you can design and customize your chatbot, set response patterns, and integrate it into websites or applications.\",\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    question: \"Is coding knowledge required to use AIQuill?\",\r\n    answer:\r\n      \"Yes! With AIQuill, you can design and customize your chatbot, set response patterns, and integrate it into websites or applications.\",\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    question: \"What industries can use AIQuill?\",\r\n    answer:\r\n      \"Yes! With AIQuill, you can design and customize your chatbot, set response patterns, and integrate it into websites or applications.\",\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    question: \"Does AIQuill support multiple languages?\",\r\n    answer:\r\n      \"Yes! With AIQuill, you can design and customize your chatbot, set response patterns, and integrate it into websites or applications.\",\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    question: \"Can I integrate AIQuill with my website or app?\",\r\n    answer:\r\n      \"Yes! With AIQuill, you can design and customize your chatbot, set response patterns, and integrate it into websites or applications.\",\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    question: \"Is there a free version of AIQuill?\",\r\n    answer:\r\n      \"Yes! With AIQuill, you can design and customize your chatbot, set response patterns, and integrate it into websites or applications.\",\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    question: \"How secure is AIQuill?\",\r\n    answer:\r\n      \"Yes! With AIQuill, you can design and customize your chatbot, set response patterns, and integrate it into websites or applications.\",\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    question: \"How do I get started with AIQuill?\",\r\n    answer:\r\n      \"Yes! With AIQuill, you can design and customize your chatbot, set response patterns, and integrate it into websites or applications.\",\r\n  },\r\n];\r\n\r\nexport const supportMenuItems = [\r\n  {\r\n    id: uuidv4(),\r\n    name: \"FAQs\",\r\n    icon: PiQuestion,\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    name: \"Change Log\",\r\n    icon: PiFileText,\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    name: \"Roadmap\",\r\n    icon: PiPath,\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    name: \"Contact\",\r\n    icon: PiEnvelopeSimple,\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    name: \"Privacy\",\r\n    icon: PiLockKey,\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    name: \"Terms\",\r\n    icon: PiClipboardText,\r\n  },\r\n];\r\n\r\nexport const settingsTabItems = [\r\n  {\r\n    id: uuidv4(),\r\n    name: \"General\",\r\n    icon: PiGear,\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    name: \"Security\",\r\n    icon: PiLock,\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    name: \"Appearance\",\r\n    icon: PiPaintBrushHousehold,\r\n  },\r\n];\r\n\r\nexport const themeSettingsData = [\r\n  {\r\n    id: uuidv4(),\r\n    name: \"System\",\r\n    icon: PiLaptop,\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    name: \"Light\",\r\n    icon: PiSun,\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    name: \"Dark\",\r\n    icon: PiMoon,\r\n  },\r\n];\r\n\r\nexport const accentColorItems = [\r\n  {\r\n    id: uuidv4(),\r\n    color: \"#4D6BFE\",\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    color: \"#8E33FF\",\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    color: \"#00B8D9\",\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    color: \"#22C55E\",\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    color: \"#FFAB00\",\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    color: \"#FF5630\",\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    color: \"#0B1323\",\r\n  },\r\n];\r\n\r\nexport const customBotsData = [\r\n  {\r\n    id: uuidv4(),\r\n    icon: icon1,\r\n    title: \"Research Specialist\",\r\n    desc: \"Helps with academic research, paper analysis, and citation management\",\r\n    tag: \"Research\",\r\n    color: \"77, 107, 254\",\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    icon: icon10,\r\n    title: \"Programming Guide\",\r\n    desc: \"Assists with programming, code reviews, and best practices\",\r\n    tag: \"Programming\",\r\n    color: \"142, 51, 255\",\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    icon: icon12,\r\n    title: \"Writing Mentor\",\r\n    desc: \"Helps improve writing style, grammar, and content structure\",\r\n    tag: \"Writing\",\r\n    color: \"0, 184, 217\",\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    icon: icon16,\r\n    title: \"Information Specialist\",\r\n    desc: \"Assists with data analysis, visualization, insights and Data analyst management\",\r\n    tag: \"Research\",\r\n    color: \"34, 197, 94\",\r\n  },\r\n];\r\n\r\nexport const aiGeneratorBrainstorm = [\r\n  {\r\n    id: uuidv4(),\r\n    name: \"Ultra Pro AI\",\r\n    icon: PiRobot,\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    name: \"4K+ Upscaling\",\r\n    icon: PiRocketLaunch,\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    name: \"High Resolution\",\r\n    icon: PiAirplaneTilt,\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    name: \"Medium Precision\",\r\n    icon: PiFire,\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    name: \"Low Resolution\",\r\n    icon: PiPaperPlaneTilt,\r\n  },\r\n];\r\n\r\nexport const upgradeModalData = [\r\n  {\r\n    id: uuidv4(),\r\n    title: \"2.1v Flash\",\r\n    desc: \"Get everyday help\",\r\n    isNew: true,\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    title: \"2.0v Flash Thinking Experimental\",\r\n    desc: \"Best for multi-step reasoning\",\r\n    isNew: true,\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    title: \"1.9.1 Thinking Experimental with apps\",\r\n    desc: \"Reasoning across YouTube, Maps & Search\",\r\n    isNew: true,\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    title: \"1.9 Flash\",\r\n    desc: \"Previous Model\",\r\n    isNew: false,\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    title: \"1.5 Flash\",\r\n    desc: \"Start Journey With AI\",\r\n    isNew: false,\r\n  },\r\n];\r\n\r\nexport const integrationItemsData = [\r\n  {\r\n    id: uuidv4(),\r\n    name: \"Slack\",\r\n    desc: \"Real-time messaging and notifications.\",\r\n    icon: slack,\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    name: \"Shopify\",\r\n    desc: \"Manage your e-commerce platform.\",\r\n    icon: shopify,\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    name: \"Webflow\",\r\n    desc: \"Manage your Webflow site content.\",\r\n    icon: webflow,\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    name: \"Airtable\",\r\n    desc: \"Connect and manage your Airtable bases.\",\r\n    icon: airtable,\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    name: \"Monday\",\r\n    desc: \"Manage team workflows and projects.\",\r\n    icon: monday,\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    name: \"Figma\",\r\n    desc: \"Access and preview Figma designs.\",\r\n    icon: figma,\r\n  },\r\n];\r\n\r\nexport const explorePageData = [\r\n  {\r\n    id: uuidv4(),\r\n    title: \"Featured\",\r\n    desc: \"Curated top picks from this week\",\r\n    articles: [\r\n      {\r\n        id: uuidv4(),\r\n        title: \"AI Video Maker by Descript\",\r\n        desc: \"Turn your ideas into videos with the AI Video Maker by Descript—a powerful text-to-speech video generator and video editor in one.\",\r\n        icon: icon1,\r\n      },\r\n      {\r\n        id: uuidv4(),\r\n        title: \"Mermaid Chart: diagrams and charts\",\r\n        desc: \"Official AIQuill from the Mermaid team. Generate a Mermaid diagram or chart with text including video generator and video editor in one.\",\r\n        icon: icon2,\r\n      },\r\n      {\r\n        id: uuidv4(),\r\n        title: \"Form And Survey Maker by demo.co\",\r\n        desc: \"Build and share live surveys with the AI Video Maker by Descript—a powerful text-to-speech video generator and video editor in one.\",\r\n        icon: icon3,\r\n      },\r\n      {\r\n        id: uuidv4(),\r\n        title: \"Tutor Me\",\r\n        desc: \"Your personal AI tutor by Khan Academy! I'm Khanmigo Lite - here to help you with math, science, and—a powerful text-to-speech video generator.\",\r\n        icon: icon4,\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    title: \"Trending\",\r\n    desc: \"Most popular AIQuill by our community\",\r\n    articles: [\r\n      {\r\n        id: uuidv4(),\r\n        title: \"AI Innovations Reshaping the Future\",\r\n        desc: \"Explore cutting-edge AI advancements driving transformation. From automation to deep learning, discover groundbreaking.\",\r\n        icon: icon5,\r\n      },\r\n      {\r\n        id: uuidv4(),\r\n        title: \"The Rise of AI Technology\",\r\n        desc: \"Artificial intelligence is evolving rapidly. Stay updated with the latest AI breakthroughs, trends, and emerging tools revolutionizing.\",\r\n        icon: icon6,\r\n      },\r\n      {\r\n        id: uuidv4(),\r\n        title: \"Stay Updated with AI Trends\",\r\n        desc: \"Keep up with AI’s fast-paced evolution. Learn about innovations in automation, robotics, and machine learning.\",\r\n        icon: icon7,\r\n      },\r\n      {\r\n        id: uuidv4(),\r\n        title: \"Exploring the Future of AI\",\r\n        desc: \"AI is changing how we work and live. Discover upcoming trends, research, and advancements that redefine industries.\",\r\n        icon: icon8,\r\n      },\r\n      {\r\n        id: uuidv4(),\r\n        title: \"AI-Powered Future: What’s Next?\",\r\n        desc: \"Predict the future of AI-driven technology. Explore innovations in AI-powered assistants, automation, and predictive analytics.\",\r\n        icon: icon9,\r\n      },\r\n      {\r\n        id: uuidv4(),\r\n        title: \"AI’s Latest Breakthroughs\",\r\n        desc: \"Get the latest AI news and updates. Learn about groundbreaking research, industry shifts, and revolutionary discoveries.\",\r\n        icon: icon10,\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    title: \"Writing\",\r\n    desc: \"Enhance your writing with tools for creation, editing, and style refinement\",\r\n    articles: [\r\n      {\r\n        id: uuidv4(),\r\n        title: \"AI-Powered Writing for Creativity\",\r\n        desc: \"Enhance your writing with AI tools. Generate ideas, improve grammar, and create engaging content effortlessly.\",\r\n        icon: icon11,\r\n      },\r\n      {\r\n        id: uuidv4(),\r\n        title: \"Write Smarter with AI Help\",\r\n        desc: \"Boost your productivity with AI writing assistants. Get real-time feedback, refine your style, and craft compelling narratives with ease.\",\r\n        icon: icon12,\r\n      },\r\n      {\r\n        id: uuidv4(),\r\n        title: \"AI Writing: The Future Tool\",\r\n        desc: \"From blogs to business reports, AI enhances content creation. Explore AI-driven writing tools that help improve clarity and efficiency.\",\r\n        icon: icon13,\r\n      },\r\n      {\r\n        id: uuidv4(),\r\n        title: \"Transform Your Words with AI\",\r\n        desc: \"Experience AI-enhanced writing assistance. Generate professional-quality text, refine drafts, and create high-impact content.\",\r\n        icon: icon14,\r\n      },\r\n      {\r\n        id: uuidv4(),\r\n        title: \"AI Text Generation Made Easy\",\r\n        desc: \"Simplify content creation with AI-powered writing. Generate high-quality text, rephrase sentences, and optimize readability.\",\r\n        icon: icon15,\r\n      },\r\n      {\r\n        id: uuidv4(),\r\n        title: \"Boost Writing Skills with AI\",\r\n        desc: \"Take your writing to the next level. AI helps enhance tone, grammar, and structure, making every piece clear and engaging.\",\r\n        icon: icon16,\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    title: \"Productivity\",\r\n    desc: \"Increase your efficiency\",\r\n    articles: [\r\n      {\r\n        id: uuidv4(),\r\n        title: \"AI Tools for Maximum Efficiency\",\r\n        desc: \"Optimize your workflow with AI-powered automation. Save time, eliminate repetitive tasks, and enhance focus with intelligent.\",\r\n        icon: icon17,\r\n      },\r\n      {\r\n        id: uuidv4(),\r\n        title: \"Work Smarter with AI Solutions\",\r\n        desc: \"AI enhances productivity by automating tedious tasks. From scheduling to data processing, let AI handle the workload.\",\r\n        icon: icon18,\r\n      },\r\n      {\r\n        id: uuidv4(),\r\n        title: \"Increase Productivity AI Assistance\",\r\n        desc: \"Streamline your daily tasks with AI-powered organization tools. Stay on top of deadlines, manage projects.\",\r\n        icon: icon19,\r\n      },\r\n      {\r\n        id: uuidv4(),\r\n        title: \"AI Automation for Smarter Workflows\",\r\n        desc: \"Let AI handle repetitive work. Automate scheduling, email responses, and task management to free up valuable time.\",\r\n        icon: icon20,\r\n      },\r\n      {\r\n        id: uuidv4(),\r\n        title: \"The Future of Smart Work\",\r\n        desc: \"AI-driven solutions make productivity effortless. Use machine learning tools to enhance workplace efficiency, manage tasks.\",\r\n        icon: icon21,\r\n      },\r\n      {\r\n        id: uuidv4(),\r\n        title: \"Revolutionizing Work with AI Tech\",\r\n        desc: \"Embrace AI-powered tools for a smarter workflow. Automate time-consuming processes and focus on what truly matters in your work.\",\r\n        icon: icon22,\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    title: \"Research & Analysis\",\r\n    desc: \"Find, evaluate, interpret, and visualize information\",\r\n    articles: [\r\n      {\r\n        id: uuidv4(),\r\n        title: \"AI for Smarter Data Analysis\",\r\n        desc: \"Unlock deep insights using AI-powered research tools. Analyze large datasets, find trends, and make data-driven decisions efficiently.\",\r\n        icon: icon23,\r\n      },\r\n      {\r\n        id: uuidv4(),\r\n        title: \"Revolutionizing Research AI Tools\",\r\n        desc: \"AI is transforming how we analyze data. Enhance your research with machine learning models designed for accuracy and efficiency.\",\r\n        icon: icon24,\r\n      },\r\n      {\r\n        id: uuidv4(),\r\n        title: \"AI Insights for Smarter Decisions\",\r\n        desc: \"Process information faster with AI-driven analytics. From business reports to academic research, AI helps uncover valuable insights.\",\r\n        icon: icon25,\r\n      },\r\n      {\r\n        id: uuidv4(),\r\n        title: \"Transform Data Actionable Insights\",\r\n        desc: \"AI-powered analytics provide real-time data interpretation. Identify trends, predict outcomes, and optimize strategies using intelligent.\",\r\n        icon: icon1,\r\n      },\r\n      {\r\n        id: uuidv4(),\r\n        title: \"Deep Learning for Data Analysis\",\r\n        desc: \"Leverage deep learning to analyze patterns and trends. AI-powered tools make complex research faster, more precise, and highly effective.\",\r\n        icon: icon7,\r\n      },\r\n      {\r\n        id: uuidv4(),\r\n        title: \"AI Research: Smarter, Faster, Better\",\r\n        desc: \"Accelerate research with AI automation. From literature reviews to predictive modeling, AI simplifies complex data analysis.\",\r\n        icon: icon8,\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    title: \"Education\",\r\n    desc: \"Explore new ideas, revisit existing skills\",\r\n    articles: [\r\n      {\r\n        id: uuidv4(),\r\n        title: \"AI-Powered Learning for Students\",\r\n        desc: \"AI transforms education by personalizing learning experiences. Get instant feedback, adaptive quizzes, and AI-generated study plans.\",\r\n        icon: icon2,\r\n      },\r\n      {\r\n        id: uuidv4(),\r\n        title: \"Smart Education with AI Assistance\",\r\n        desc: \"Enhance learning with AI-powered tools. From automated tutoring to AI-driven assessments, modern education.\",\r\n        icon: icon3,\r\n      },\r\n      {\r\n        id: uuidv4(),\r\n        title: \"AI Tutors: The Future of Learning\",\r\n        desc: \"AI-powered tutors provide personalized lessons, improving knowledge retention and engagement. Learn at your own pace.\",\r\n        icon: icon4,\r\n      },\r\n      {\r\n        id: uuidv4(),\r\n        title: \"Learning with AI Technology\",\r\n        desc: \"AI makes learning smarter. Interactive study assistants help students grasp complex topics, while AI-generated summaries study.\",\r\n        icon: icon5,\r\n      },\r\n      {\r\n        id: uuidv4(),\r\n        title: \"Personalized Learning with AI\",\r\n        desc: \"AI analyzes your progress and adapts to your learning style. Get tailored recommendations, instant feedback.\",\r\n        icon: icon7,\r\n      },\r\n      {\r\n        id: uuidv4(),\r\n        title: \"Smarter Classrooms with AI\",\r\n        desc: \"AI-powered tools enhance education with interactive learning, automated grading, and real-time student performance tracking.\",\r\n        icon: icon8,\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    title: \"Lifestyle\",\r\n    desc: \"Get tips on travel, workouts, style, food, and more\",\r\n    articles: [\r\n      {\r\n        id: uuidv4(),\r\n        title: \"AI for a Smarter Life\",\r\n        desc: \"AI simplifies everyday life with intelligent automation, from smart home devices to personalized recommendations.\",\r\n        icon: icon9,\r\n      },\r\n      {\r\n        id: uuidv4(),\r\n        title: \"Personalized AI for Daily Living\",\r\n        desc: \"Experience AI-powered convenience. From smart assistants to automated routines, AI enhances comfort, productivity.\",\r\n        icon: icon10,\r\n      },\r\n      {\r\n        id: uuidv4(),\r\n        title: \"Smart Assistants for Effortless Living\",\r\n        desc: \"Let AI handle your daily tasks. Voice assistants, smart reminders, and AI-powered planning tools help streamline routines with ease.\",\r\n        icon: icon11,\r\n      },\r\n      {\r\n        id: uuidv4(),\r\n        title: \"AI-Driven Lifestyle Enhancements\",\r\n        desc: \"Upgrade your daily life with AI solutions. Manage tasks, track fitness, and get tailored recommendations for a seamless experience.\",\r\n        icon: icon12,\r\n      },\r\n      {\r\n        id: uuidv4(),\r\n        title: \"AI Technology for Smarter Homes\",\r\n        desc: \"Control your home with AI-powered automation. From smart lighting to voice-controlled devices, technology simplifies life for greater comfort.\",\r\n        icon: icon7,\r\n      },\r\n      {\r\n        id: uuidv4(),\r\n        title: \"Live Smarter with AI Innovations\",\r\n        desc: \"AI enhances your lifestyle by managing schedules, automating tasks, and providing personalized suggestions.\",\r\n        icon: icon8,\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    id: uuidv4(),\r\n    title: \"Programming\",\r\n    desc: \"Write code, debug, test, and learn\",\r\n    articles: [\r\n      {\r\n        id: uuidv4(),\r\n        title: \"AI-Assisted Coding for Developers\",\r\n        desc: \"Accelerate programming with AI-powered suggestions. Get real-time code recommendations, debug efficiently.\",\r\n        icon: icon15,\r\n      },\r\n      {\r\n        id: uuidv4(),\r\n        title: \"Code Faster with AI Assistance\",\r\n        desc: \"AI-driven coding assistants help you write cleaner, more efficient code. Automate repetitive tasks and optimize performance with AI.\",\r\n        icon: icon16,\r\n      },\r\n      {\r\n        id: uuidv4(),\r\n        title: \"AI in Software Development\",\r\n        desc: \"Transform programming with AI automation. From generating code to predictive debugging, AI is revolutionizing software.\",\r\n        icon: icon17,\r\n      },\r\n      {\r\n        id: uuidv4(),\r\n        title: \"Smart Coding with AI Tools\",\r\n        desc: \"Enhance development workflows with AI-powered solutions. Automate repetitive coding tasks, debug errors, and optimize algorithms.\",\r\n        icon: icon18,\r\n      },\r\n      {\r\n        id: uuidv4(),\r\n        title: \"The Future of AI Coding\",\r\n        desc: \"AI is reshaping programming. Explore tools that suggest code, optimize structures, and streamline development processes.\",\r\n        icon: icon19,\r\n      },\r\n      {\r\n        id: uuidv4(),\r\n        title: \"AI Debugging & Optimization\",\r\n        desc: \"Use AI-driven debugging tools to detect errors faster and enhance code performance, making software development smarter.\",\r\n        icon: icon20,\r\n      },\r\n    ],\r\n  },\r\n];\r\n\r\nexport const phpCode = `<?php\r\nsession_start();\r\n$conn = new \r\nmysqli(\"localhost\", \"root\", \"\", \"your_database\");\r\nif ($conn->connect_error) {\r\n    die(\"Connection failed: \" . $conn->connect);\r\n}\r\nif \r\n($_SERVER[\"REQUEST\"] == \"POST\") \r\n{\r\n    $name = $_POST[\"name\"];\r\n    $gmail = $_POST[\"gmail\"];\r\n    $phone = $_POST[\"phone\"];\r\n    $pass =($_POST[\"pass\"], \r\n    PASSWORD_BCRYPT);\r\n    $stmt = $conn->\r\n    prepare(\"INSERT INTO users \r\n    (name, gmail, phone, pass);\r\n    $stmt->\r\n    bind_param(\"ssss\", $name, $gmail, \r\n    $phone, $password);\r\n    \r\n<body>\r\n    <h2>Register</h2>\r\n    <?php \r\n    if(isset($_SESSION['error'])) \r\n    { echo \"<p style='color:red;'>\"  \r\n    \"</p>\"; unset($_SESSION['error']); } ?>\r\n   \r\n</body>\r\n</html>\r\n`;\r\n\r\nexport const countryOptions = [\r\n  { value: \"afganistan\", label: \"Afganistan\" },\r\n  { value: \"america\", label: \"United States\" },\r\n  { value: \"london\", label: \"United Kingdom\" },\r\n  { value: \"china\", label: \"China\" },\r\n];\r\n\r\nexport const emailPreferenceOptions = [\r\n  {\r\n    value: \"I am okay with promo emails\",\r\n    label: \"I am okay with promo emails\",\r\n  },\r\n  { value: \"I want only update emails\", label: \"I want only update email\" },\r\n  {\r\n    value: \"I want only security emails\",\r\n    label: \"I want only security emails\",\r\n  },\r\n  { value: \"I want all emails\", label: \"I want all emails\" },\r\n];\r\n\r\nexport const languageOptions = [\r\n  {\r\n    value: \"English\",\r\n    label: \"English\",\r\n  },\r\n  {\r\n    value: \"French\",\r\n    label: \"French\",\r\n  },\r\n  {\r\n    value: \"Arabic\",\r\n    label: \"Arabic\",\r\n  },\r\n  {\r\n    value: \"Japanese\",\r\n    label: \"Japanese\",\r\n  },\r\n];\r\n\r\nexport const exploreBotTags = [\r\n  \"Featured\",\r\n  \"Trending\",\r\n  \"Writing\",\r\n  \"Productivity\",\r\n  \"Research & Analysis\",\r\n  \"Lifestyle\",\r\n  \"Education\",\r\n  \"Programming\",\r\n];\r\n\r\nexport const botCategory = [\r\n  {\r\n    value: \"Education\",\r\n    label: \"Education\",\r\n  },\r\n  {\r\n    value: \"Research\",\r\n    label: \"Research\",\r\n  },\r\n  {\r\n    value: \"Video Creation\",\r\n    label: \"Video Creation\",\r\n  },\r\n];\r\n\r\nexport const responseStyle = [\r\n  {\r\n    value: \"Response Style 1\",\r\n    label: \"Response Style 1\",\r\n  },\r\n  {\r\n    value: \"Response Style 2\",\r\n    label: \"Response Style 2\",\r\n  },\r\n  {\r\n    value: \"Response Style 3\",\r\n    label: \"Response Style 3\",\r\n  },\r\n];\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAoCA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,mBAAmB;AACnB;AACA;AACA;AACA;AACA;AACA;AAnCA;AAlCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuEO,MAAM,cAAc;IACzB;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,OAAO;QACP,MAAM,iJAAA,CAAA,UAAO;QACb,OAAO;IACT;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,OAAO;QACP,MAAM,iJAAA,CAAA,UAAO;QACb,OAAO;IACT;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,OAAO;QACP,MAAM,iJAAA,CAAA,gBAAa;QACnB,OAAO;IACT;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,OAAO;QACP,MAAM,iJAAA,CAAA,gBAAa;QACnB,OAAO;IACT;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,OAAO;QACP,MAAM,iJAAA,CAAA,kBAAe;QACrB,OAAO;IACT;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,OAAO;QACP,MAAM,iJAAA,CAAA,cAAW;QACjB,OAAO;IACT;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,OAAO;QACP,MAAM,iJAAA,CAAA,SAAM;QACZ,OAAO;IACT;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,OAAO;QACP,MAAM,iJAAA,CAAA,qBAAkB;QACxB,OAAO;IACT;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,OAAO;QACP,MAAM,iJAAA,CAAA,cAAW;QACjB,OAAO;IACT;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,OAAO;QACP,MAAM,iJAAA,CAAA,aAAU;QAChB,OAAO;IACT;CACD;AAEM,MAAM,qBAAqB;IAChC;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,MAAM,iJAAA,CAAA,oBAAiB;QACvB,OAAO;IACT;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,MAAM,iJAAA,CAAA,cAAW;QACjB,OAAO;IACT;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,MAAM,iJAAA,CAAA,oBAAiB;QACvB,OAAO;IACT;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,MAAM,iJAAA,CAAA,aAAU;QAChB,OAAO;IACT;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,MAAM,iJAAA,CAAA,sBAAmB;QACzB,OAAO;IACT;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,MAAM,iJAAA,CAAA,oBAAiB;QACvB,OAAO;IACT;CACD;AAEM,MAAM,qBAAqB;IAChC;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,MAAM,iJAAA,CAAA,mBAAgB;QACtB,OAAO;QACP,UAAU;YACR;YACA;YACA;YACA;SACD;QACD,qBAAqB;YACnB;YACA;SACD;IACH;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,MAAM,iJAAA,CAAA,iBAAc;QACpB,OAAO;QACP,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,qBAAqB;YAAC;SAAqB;IAC7C;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,MAAM,iJAAA,CAAA,iBAAc;QACpB,OAAO;QACP,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,qBAAqB,EAAE;IACzB;CACD;AAEM,MAAM,UAAU;IACrB;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,UAAU;QACV,QACE;IACJ;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,UAAU;QACV,QACE;IACJ;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,UAAU;QACV,QACE;IACJ;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,UAAU;QACV,QACE;IACJ;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,UAAU;QACV,QACE;IACJ;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,UAAU;QACV,QACE;IACJ;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,UAAU;QACV,QACE;IACJ;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,UAAU;QACV,QACE;IACJ;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,UAAU;QACV,QACE;IACJ;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,UAAU;QACV,QACE;IACJ;CACD;AAEM,MAAM,mBAAmB;IAC9B;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,MAAM,iJAAA,CAAA,aAAU;IAClB;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,MAAM,iJAAA,CAAA,aAAU;IAClB;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,MAAM,iJAAA,CAAA,SAAM;IACd;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,MAAM,iJAAA,CAAA,mBAAgB;IACxB;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,MAAM,iJAAA,CAAA,YAAS;IACjB;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,MAAM,iJAAA,CAAA,kBAAe;IACvB;CACD;AAEM,MAAM,mBAAmB;IAC9B;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,MAAM,iJAAA,CAAA,SAAM;IACd;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,MAAM,iJAAA,CAAA,SAAM;IACd;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,MAAM,iJAAA,CAAA,wBAAqB;IAC7B;CACD;AAEM,MAAM,oBAAoB;IAC/B;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,MAAM,iJAAA,CAAA,WAAQ;IAChB;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,MAAM,iJAAA,CAAA,QAAK;IACb;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,MAAM,iJAAA,CAAA,SAAM;IACd;CACD;AAEM,MAAM,mBAAmB;IAC9B;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,OAAO;IACT;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,OAAO;IACT;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,OAAO;IACT;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,OAAO;IACT;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,OAAO;IACT;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,OAAO;IACT;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,OAAO;IACT;CACD;AAEM,MAAM,iBAAiB;IAC5B;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM,gVAAA,CAAA,UAAK;QACX,OAAO;QACP,MAAM;QACN,KAAK;QACL,OAAO;IACT;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM,kVAAA,CAAA,UAAM;QACZ,OAAO;QACP,MAAM;QACN,KAAK;QACL,OAAO;IACT;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM,kVAAA,CAAA,UAAM;QACZ,OAAO;QACP,MAAM;QACN,KAAK;QACL,OAAO;IACT;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM,kVAAA,CAAA,UAAM;QACZ,OAAO;QACP,MAAM;QACN,KAAK;QACL,OAAO;IACT;CACD;AAEM,MAAM,wBAAwB;IACnC;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,MAAM,iJAAA,CAAA,UAAO;IACf;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,MAAM,iJAAA,CAAA,iBAAc;IACtB;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,MAAM,iJAAA,CAAA,iBAAc;IACtB;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,MAAM,iJAAA,CAAA,SAAM;IACd;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,MAAM,iJAAA,CAAA,mBAAgB;IACxB;CACD;AAEM,MAAM,mBAAmB;IAC9B;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,OAAO;QACP,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,OAAO;QACP,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,OAAO;QACP,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,OAAO;QACP,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,OAAO;QACP,MAAM;QACN,OAAO;IACT;CACD;AAEM,MAAM,uBAAuB;IAClC;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,MAAM;QACN,MAAM,4RAAA,CAAA,UAAK;IACb;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,MAAM;QACN,MAAM,gSAAA,CAAA,UAAO;IACf;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,MAAM;QACN,MAAM,gSAAA,CAAA,UAAO;IACf;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,MAAM;QACN,MAAM,kSAAA,CAAA,UAAQ;IAChB;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,MAAM;QACN,MAAM,8RAAA,CAAA,UAAM;IACd;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,MAAM;QACN,MAAM;QACN,MAAM,4RAAA,CAAA,UAAK;IACb;CACD;AAEM,MAAM,kBAAkB;IAC7B;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,OAAO;QACP,MAAM;QACN,UAAU;YACR;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,gVAAA,CAAA,UAAK;YACb;YACA;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,gVAAA,CAAA,UAAK;YACb;YACA;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,gVAAA,CAAA,UAAK;YACb;YACA;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,gVAAA,CAAA,UAAK;YACb;SACD;IACH;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,OAAO;QACP,MAAM;QACN,UAAU;YACR;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,gVAAA,CAAA,UAAK;YACb;YACA;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,gVAAA,CAAA,UAAK;YACb;YACA;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,gVAAA,CAAA,UAAK;YACb;YACA;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,gVAAA,CAAA,UAAK;YACb;YACA;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,gVAAA,CAAA,UAAK;YACb;YACA;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,kVAAA,CAAA,UAAM;YACd;SACD;IACH;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,OAAO;QACP,MAAM;QACN,UAAU;YACR;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,kVAAA,CAAA,UAAM;YACd;YACA;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,kVAAA,CAAA,UAAM;YACd;YACA;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,kVAAA,CAAA,UAAM;YACd;YACA;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,kVAAA,CAAA,UAAM;YACd;YACA;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,kVAAA,CAAA,UAAM;YACd;YACA;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,kVAAA,CAAA,UAAM;YACd;SACD;IACH;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,OAAO;QACP,MAAM;QACN,UAAU;YACR;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,kVAAA,CAAA,UAAM;YACd;YACA;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,kVAAA,CAAA,UAAM;YACd;YACA;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,kVAAA,CAAA,UAAM;YACd;YACA;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,kVAAA,CAAA,UAAM;YACd;YACA;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,kVAAA,CAAA,UAAM;YACd;YACA;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,kVAAA,CAAA,UAAM;YACd;SACD;IACH;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,OAAO;QACP,MAAM;QACN,UAAU;YACR;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,kVAAA,CAAA,UAAM;YACd;YACA;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,kVAAA,CAAA,UAAM;YACd;YACA;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,kVAAA,CAAA,UAAM;YACd;YACA;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,gVAAA,CAAA,UAAK;YACb;YACA;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,gVAAA,CAAA,UAAK;YACb;YACA;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,gVAAA,CAAA,UAAK;YACb;SACD;IACH;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,OAAO;QACP,MAAM;QACN,UAAU;YACR;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,gVAAA,CAAA,UAAK;YACb;YACA;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,gVAAA,CAAA,UAAK;YACb;YACA;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,gVAAA,CAAA,UAAK;YACb;YACA;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,gVAAA,CAAA,UAAK;YACb;YACA;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,gVAAA,CAAA,UAAK;YACb;YACA;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,gVAAA,CAAA,UAAK;YACb;SACD;IACH;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,OAAO;QACP,MAAM;QACN,UAAU;YACR;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,gVAAA,CAAA,UAAK;YACb;YACA;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,kVAAA,CAAA,UAAM;YACd;YACA;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,kVAAA,CAAA,UAAM;YACd;YACA;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,kVAAA,CAAA,UAAM;YACd;YACA;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,gVAAA,CAAA,UAAK;YACb;YACA;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,gVAAA,CAAA,UAAK;YACb;SACD;IACH;IACA;QACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACT,OAAO;QACP,MAAM;QACN,UAAU;YACR;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,kVAAA,CAAA,UAAM;YACd;YACA;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,kVAAA,CAAA,UAAM;YACd;YACA;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,kVAAA,CAAA,UAAM;YACd;YACA;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,kVAAA,CAAA,UAAM;YACd;YACA;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,kVAAA,CAAA,UAAM;YACd;YACA;gBACE,IAAI,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;gBACT,OAAO;gBACP,MAAM;gBACN,MAAM,kVAAA,CAAA,UAAM;YACd;SACD;IACH;CACD;AAEM,MAAM,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BxB,CAAC;AAEM,MAAM,iBAAiB;IAC5B;QAAE,OAAO;QAAc,OAAO;IAAa;IAC3C;QAAE,OAAO;QAAW,OAAO;IAAgB;IAC3C;QAAE,OAAO;QAAU,OAAO;IAAiB;IAC3C;QAAE,OAAO;QAAS,OAAO;IAAQ;CAClC;AAEM,MAAM,yBAAyB;IACpC;QACE,OAAO;QACP,OAAO;IACT;IACA;QAAE,OAAO;QAA6B,OAAO;IAA2B;IACxE;QACE,OAAO;QACP,OAAO;IACT;IACA;QAAE,OAAO;QAAqB,OAAO;IAAoB;CAC1D;AAEM,MAAM,kBAAkB;IAC7B;QACE,OAAO;QACP,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;IACT;CACD;AAEM,MAAM,iBAAiB;IAC5B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,cAAc;IACzB;QACE,OAAO;QACP,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;IACT;CACD;AAEM,MAAM,gBAAgB;IAC3B;QACE,OAAO;QACP,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;IACT;IACA;QACE,OAAO;QACP,OAAO;IACT;CACD"}}, {"offset": {"line": 2537, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2543, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/services/sharingService.ts"], "sourcesContent": ["import { Chat } from \"@/stores/chatList\";\n\nexport interface ShareableChat {\n  id: string;\n  shareId: string;\n  title: string;\n  messages: any[];\n  createdAt: string;\n  sharedAt: string;\n  expiresAt?: string;\n  isPublic: boolean;\n  includeMessages: boolean;\n  viewCount: number;\n  originalChatId: string;\n}\n\nexport interface ShareSettings {\n  includeMessages: boolean;\n  publicAccess: boolean;\n  expiresIn: string;\n}\n\nclass SharingService {\n  private baseUrl: string;\n\n  constructor() {\n    this.baseUrl = typeof window !== 'undefined' ? window.location.origin : '';\n  }\n\n  /**\n   * Generate a unique share ID\n   */\n  private generateShareId(): string {\n    return Math.random().toString(36).substring(2, 15) + \n           Math.random().toString(36).substring(2, 15);\n  }\n\n  /**\n   * Calculate expiration date based on settings\n   */\n  private calculateExpirationDate(expiresIn: string): Date | null {\n    if (expiresIn === 'never') return null;\n    \n    const now = new Date();\n    switch (expiresIn) {\n      case '1day':\n        return new Date(now.getTime() + 24 * 60 * 60 * 1000);\n      case '7days':\n        return new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);\n      case '30days':\n        return new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);\n      default:\n        return null;\n    }\n  }\n\n  /**\n   * Create a shareable version of a chat\n   */\n  async createShareableChat(\n    chat: Chat, \n    settings: ShareSettings\n  ): Promise<{ shareId: string; shareUrl: string }> {\n    try {\n      const shareId = this.generateShareId();\n      const expiresAt = this.calculateExpirationDate(settings.expiresIn);\n      \n      // Filter messages based on settings\n      const messagesToShare = settings.includeMessages \n        ? chat.messages \n        : chat.messages.slice(0, 1); // Only include first message if not sharing all\n\n      const shareableChat: ShareableChat = {\n        id: shareId,\n        shareId,\n        title: chat.title,\n        messages: messagesToShare,\n        createdAt: chat.createdAt,\n        sharedAt: new Date().toISOString(),\n        expiresAt: expiresAt?.toISOString(),\n        isPublic: settings.publicAccess,\n        includeMessages: settings.includeMessages,\n        viewCount: 0,\n        originalChatId: chat.id\n      };\n\n      // In a real implementation, you would save this to your backend\n      // For now, we'll store it in localStorage as a demo\n      const existingShares = this.getStoredShares();\n      existingShares[shareId] = shareableChat;\n      localStorage.setItem('sharedChats', JSON.stringify(existingShares));\n\n      const shareUrl = `${this.baseUrl}/shared/chat/${shareId}`;\n      \n      return { shareId, shareUrl };\n    } catch (error) {\n      console.error('Failed to create shareable chat:', error);\n      throw new Error('Failed to create share link');\n    }\n  }\n\n  /**\n   * Get a shared chat by share ID\n   */\n  async getSharedChat(shareId: string): Promise<ShareableChat | null> {\n    try {\n      // In a real implementation, this would fetch from your backend\n      const storedShares = this.getStoredShares();\n      const sharedChat = storedShares[shareId];\n      \n      if (!sharedChat) {\n        return null;\n      }\n\n      // Check if the share has expired\n      if (sharedChat.expiresAt && new Date() > new Date(sharedChat.expiresAt)) {\n        // Remove expired share\n        delete storedShares[shareId];\n        localStorage.setItem('sharedChats', JSON.stringify(storedShares));\n        return null;\n      }\n\n      // Increment view count\n      sharedChat.viewCount += 1;\n      storedShares[shareId] = sharedChat;\n      localStorage.setItem('sharedChats', JSON.stringify(storedShares));\n\n      return sharedChat;\n    } catch (error) {\n      console.error('Failed to get shared chat:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Get all shares for the current user\n   */\n  getUserShares(): ShareableChat[] {\n    try {\n      const storedShares = this.getStoredShares();\n      return Object.values(storedShares).filter(share => !this.isExpired(share));\n    } catch (error) {\n      console.error('Failed to get user shares:', error);\n      return [];\n    }\n  }\n\n  /**\n   * Delete a shared chat\n   */\n  async deleteShare(shareId: string): Promise<boolean> {\n    try {\n      const storedShares = this.getStoredShares();\n      if (storedShares[shareId]) {\n        delete storedShares[shareId];\n        localStorage.setItem('sharedChats', JSON.stringify(storedShares));\n        return true;\n      }\n      return false;\n    } catch (error) {\n      console.error('Failed to delete share:', error);\n      return false;\n    }\n  }\n\n  /**\n   * Check if a share has expired\n   */\n  private isExpired(share: ShareableChat): boolean {\n    return share.expiresAt ? new Date() > new Date(share.expiresAt) : false;\n  }\n\n  /**\n   * Get stored shares from localStorage\n   */\n  private getStoredShares(): Record<string, ShareableChat> {\n    try {\n      const stored = localStorage.getItem('sharedChats');\n      return stored ? JSON.parse(stored) : {};\n    } catch (error) {\n      console.error('Failed to parse stored shares:', error);\n      return {};\n    }\n  }\n\n  /**\n   * Clean up expired shares\n   */\n  cleanupExpiredShares(): void {\n    try {\n      const storedShares = this.getStoredShares();\n      const validShares: Record<string, ShareableChat> = {};\n      \n      Object.entries(storedShares).forEach(([shareId, share]) => {\n        if (!this.isExpired(share)) {\n          validShares[shareId] = share;\n        }\n      });\n      \n      localStorage.setItem('sharedChats', JSON.stringify(validShares));\n    } catch (error) {\n      console.error('Failed to cleanup expired shares:', error);\n    }\n  }\n\n  /**\n   * Generate share metadata for social media\n   */\n  generateShareMetadata(chat: Chat): {\n    title: string;\n    description: string;\n    image?: string;\n  } {\n    const messageCount = chat.messages.length;\n    const userMessages = chat.messages.filter(msg => msg.isUser).length;\n    \n    return {\n      title: `AIQuill Conversation: ${chat.title}`,\n      description: `Check out this conversation with ${messageCount} messages and ${userMessages} questions on AIQuill.`,\n      // You could add an image here if you generate conversation previews\n    };\n  }\n}\n\nexport const sharingService = new SharingService();\nexport default sharingService;\n"], "names": [], "mappings": ";;;;AAsBA,MAAM;IACI,QAAgB;IAExB,aAAc;QACZ,IAAI,CAAC,OAAO,GAAG,uCAAgC,OAAO,QAAQ,CAAC,MAAM;IACvE;IAEA;;GAEC,GACD,AAAQ,kBAA0B;QAChC,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,MACxC,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;IACjD;IAEA;;GAEC,GACD,AAAQ,wBAAwB,SAAiB,EAAe;QAC9D,IAAI,cAAc,SAAS,OAAO;QAElC,MAAM,MAAM,IAAI;QAChB,OAAQ;YACN,KAAK;gBACH,OAAO,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,KAAK,KAAK;YACjD,KAAK;gBACH,OAAO,IAAI,KAAK,IAAI,OAAO,KAAK,IAAI,KAAK,KAAK,KAAK;YACrD,KAAK;gBACH,OAAO,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK;YACtD;gBACE,OAAO;QACX;IACF;IAEA;;GAEC,GACD,MAAM,oBACJ,IAAU,EACV,QAAuB,EACyB;QAChD,IAAI;YACF,MAAM,UAAU,IAAI,CAAC,eAAe;YACpC,MAAM,YAAY,IAAI,CAAC,uBAAuB,CAAC,SAAS,SAAS;YAEjE,oCAAoC;YACpC,MAAM,kBAAkB,SAAS,eAAe,GAC5C,KAAK,QAAQ,GACb,KAAK,QAAQ,CAAC,KAAK,CAAC,GAAG,IAAI,gDAAgD;YAE/E,MAAM,gBAA+B;gBACnC,IAAI;gBACJ;gBACA,OAAO,KAAK,KAAK;gBACjB,UAAU;gBACV,WAAW,KAAK,SAAS;gBACzB,UAAU,IAAI,OAAO,WAAW;gBAChC,WAAW,WAAW;gBACtB,UAAU,SAAS,YAAY;gBAC/B,iBAAiB,SAAS,eAAe;gBACzC,WAAW;gBACX,gBAAgB,KAAK,EAAE;YACzB;YAEA,gEAAgE;YAChE,oDAAoD;YACpD,MAAM,iBAAiB,IAAI,CAAC,eAAe;YAC3C,cAAc,CAAC,QAAQ,GAAG;YAC1B,aAAa,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC;YAEnD,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,SAAS;YAEzD,OAAO;gBAAE;gBAAS;YAAS;QAC7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,MAAM,IAAI,MAAM;QAClB;IACF;IAEA;;GAEC,GACD,MAAM,cAAc,OAAe,EAAiC;QAClE,IAAI;YACF,+DAA+D;YAC/D,MAAM,eAAe,IAAI,CAAC,eAAe;YACzC,MAAM,aAAa,YAAY,CAAC,QAAQ;YAExC,IAAI,CAAC,YAAY;gBACf,OAAO;YACT;YAEA,iCAAiC;YACjC,IAAI,WAAW,SAAS,IAAI,IAAI,SAAS,IAAI,KAAK,WAAW,SAAS,GAAG;gBACvE,uBAAuB;gBACvB,OAAO,YAAY,CAAC,QAAQ;gBAC5B,aAAa,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC;gBACnD,OAAO;YACT;YAEA,uBAAuB;YACvB,WAAW,SAAS,IAAI;YACxB,YAAY,CAAC,QAAQ,GAAG;YACxB,aAAa,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC;YAEnD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO;QACT;IACF;IAEA;;GAEC,GACD,gBAAiC;QAC/B,IAAI;YACF,MAAM,eAAe,IAAI,CAAC,eAAe;YACzC,OAAO,OAAO,MAAM,CAAC,cAAc,MAAM,CAAC,CAAA,QAAS,CAAC,IAAI,CAAC,SAAS,CAAC;QACrE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO,EAAE;QACX;IACF;IAEA;;GAEC,GACD,MAAM,YAAY,OAAe,EAAoB;QACnD,IAAI;YACF,MAAM,eAAe,IAAI,CAAC,eAAe;YACzC,IAAI,YAAY,CAAC,QAAQ,EAAE;gBACzB,OAAO,YAAY,CAAC,QAAQ;gBAC5B,aAAa,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC;gBACnD,OAAO;YACT;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO;QACT;IACF;IAEA;;GAEC,GACD,AAAQ,UAAU,KAAoB,EAAW;QAC/C,OAAO,MAAM,SAAS,GAAG,IAAI,SAAS,IAAI,KAAK,MAAM,SAAS,IAAI;IACpE;IAEA;;GAEC,GACD,AAAQ,kBAAiD;QACvD,IAAI;YACF,MAAM,SAAS,aAAa,OAAO,CAAC;YACpC,OAAO,SAAS,KAAK,KAAK,CAAC,UAAU,CAAC;QACxC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO,CAAC;QACV;IACF;IAEA;;GAEC,GACD,uBAA6B;QAC3B,IAAI;YACF,MAAM,eAAe,IAAI,CAAC,eAAe;YACzC,MAAM,cAA6C,CAAC;YAEpD,OAAO,OAAO,CAAC,cAAc,OAAO,CAAC,CAAC,CAAC,SAAS,MAAM;gBACpD,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ;oBAC1B,WAAW,CAAC,QAAQ,GAAG;gBACzB;YACF;YAEA,aAAa,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC;QACrD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD;IACF;IAEA;;GAEC,GACD,sBAAsB,IAAU,EAI9B;QACA,MAAM,eAAe,KAAK,QAAQ,CAAC,MAAM;QACzC,MAAM,eAAe,KAAK,QAAQ,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,EAAE,MAAM;QAEnE,OAAO;YACL,OAAO,CAAC,sBAAsB,EAAE,KAAK,KAAK,EAAE;YAC5C,aAAa,CAAC,iCAAiC,EAAE,aAAa,cAAc,EAAE,aAAa,sBAAsB,CAAC;QAEpH;IACF;AACF;AAEO,MAAM,iBAAiB,IAAI;uCACnB"}}, {"offset": {"line": 2711, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2717, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/services/fileUploadService.ts"], "sourcesContent": ["/**\r\n * Format file size to human-readable format\r\n * @param {number} bytes - File size in bytes\r\n * @returns {string} - Formatted file size (e.g., \"2.5 MB\")\r\n */\r\nexport const formatFileSize = (bytes: number): string => {\r\n  if (bytes === 0) return '0 Bytes';\r\n\r\n  const k = 1024;\r\n  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\r\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n\r\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];\r\n};\r\n\r\n/**\r\n * Upload a CSV file to FAISS via the backend\r\n * @param {File} file - The CSV file to upload\r\n * @param {string} clientEmail - Client email identifier\r\n * @param {string} indexName - Name for the FAISS index\r\n * @param {string} updateMode - Update mode ('update' or 'new') (optional)\r\n * @param {AbortSignal} signal - AbortSignal for cancelling the upload (optional)\r\n * @param {Function} onProgress - Callback function for upload progress (optional)\r\n * @param {string} embedModel - Name of the embedding model to use (optional)\r\n * @returns {Promise} - Promise that resolves with the server response including upload_id\r\n */\r\nexport const uploadCSVToFaiss = async (\r\n  file: File,\r\n  clientEmail: string,\r\n  indexName: string,\r\n  updateMode?: string | null,\r\n  signal?: AbortSignal,\r\n  onProgress?: (progress: number) => void,\r\n  embedModel?: string\r\n): Promise<any> => {\r\n  return new Promise((resolve, reject) => {\r\n    try {\r\n      // Validate file type\r\n      if (file.type !== 'text/csv') {\r\n        reject(new Error('Only CSV files are supported for FAISS upload'));\r\n        return;\r\n      }\r\n\r\n      // Create a new FormData instance\r\n      const formData = new FormData();\r\n      formData.append('file', file);\r\n\r\n      // Add client information to form data if provided\r\n      if (clientEmail) {\r\n        formData.append('client', clientEmail);\r\n      }\r\n\r\n      // Add index name to form data\r\n      if (indexName) {\r\n        formData.append('index_name', indexName);\r\n      }\r\n\r\n      // Add index name to form data\r\n      if (indexName) {\r\n        formData.append('index_name', indexName);\r\n      }\r\n\r\n      // Add update mode to form data if provided\r\n      if (updateMode) {\r\n        formData.append('update_mode', updateMode);\r\n      }\r\n\r\n      // Add embedding model to form data if provided\r\n      if (embedModel) {\r\n        formData.append('embed_model', embedModel);\r\n      }\r\n\r\n      // Create a new XMLHttpRequest and connect abort signal\r\n      const xhr = new XMLHttpRequest();\r\n\r\n      // Handle abort signal for client-side cancellation\r\n      if (signal) {\r\n        signal.onabort = () => {\r\n          xhr.abort();\r\n          // Instead of rejecting with an error, resolve with a cancellation object\r\n          // This prevents the error from appearing in the console\r\n          resolve({\r\n            success: false,\r\n            cancelled: true,\r\n            message: 'Upload cancelled by user'\r\n          });\r\n        };\r\n      }\r\n\r\n      // Configure the request to our backend endpoint\r\n      xhr.open('POST', 'http://localhost:5010/api/upload-csv', true);\r\n      // Add authentication header only (no Content-Type for FormData)\r\n      xhr.setRequestHeader('xxxid', 'FAISS');\r\n\r\n      // Track upload progress if callback provided\r\n      if (onProgress) {\r\n        xhr.upload.onprogress = (event) => {\r\n          if (event.lengthComputable) {\r\n            const progress = Math.round((event.loaded / event.total) * 100);\r\n            onProgress(progress);\r\n          }\r\n        };\r\n      }\r\n\r\n      // Handle response\r\n      xhr.onload = () => {\r\n        if (xhr.status >= 200 && xhr.status < 300) {\r\n          try {\r\n            const response = JSON.parse(xhr.responseText);\r\n            resolve(response);\r\n          } catch (error) {\r\n            resolve({\r\n              success: true,\r\n              message: 'CSV file uploaded successfully to FAISS',\r\n              indexName: indexName // Use the user-provided index name\r\n            });\r\n          }\r\n        } else {\r\n          reject(new Error(`Server error: ${xhr.status} ${xhr.statusText}`));\r\n        }\r\n      };\r\n\r\n      // Handle network errors\r\n      xhr.onerror = () => {\r\n        reject(new Error('Network error occurred while uploading CSV file'));\r\n      };\r\n\r\n      // Send the request\r\n      xhr.send(formData);\r\n    } catch (error) {\r\n      reject(error);\r\n    }\r\n  });\r\n};\r\n\r\n// Compatibility alias for existing code\r\nexport const uploadCSVToPinecone = uploadCSVToFaiss;\r\n\r\n/**\r\n * Upload a single file to the server\r\n * @param {File} file - The file to upload\r\n * @param {Function} onProgress - Callback function for upload progress\r\n * @returns {Promise} - Promise that resolves with the server response\r\n */\r\nexport const uploadFile = async (\r\n  file: File,\r\n  onProgress?: (progress: number) => void\r\n): Promise<any> => {\r\n  return new Promise((resolve, reject) => {\r\n    try {\r\n      // Create a new FormData instance\r\n      const formData = new FormData();\r\n      formData.append('file', file);\r\n\r\n      // Create a new XMLHttpRequest\r\n      const xhr = new XMLHttpRequest();\r\n\r\n      // Configure the request\r\n      xhr.open('POST', 'http://localhost:5010/api/upload', true);\r\n\r\n      // Track upload progress\r\n      xhr.upload.onprogress = (event) => {\r\n        if (event.lengthComputable && onProgress) {\r\n          const progress = Math.round((event.loaded / event.total) * 100);\r\n          onProgress(progress);\r\n        }\r\n      };\r\n\r\n      // Handle response\r\n      xhr.onload = () => {\r\n        if (xhr.status >= 200 && xhr.status < 300) {\r\n          try {\r\n            const response = JSON.parse(xhr.responseText);\r\n            resolve(response);\r\n          } catch (error) {\r\n            resolve({\r\n              success: true,\r\n              message: 'File uploaded successfully',\r\n              fileName: file.name\r\n            });\r\n          }\r\n        } else {\r\n          reject(new Error(`Server error: ${xhr.status} ${xhr.statusText}`));\r\n        }\r\n      };\r\n\r\n      // Handle network errors\r\n      xhr.onerror = () => {\r\n        reject(new Error('Network error occurred while uploading file'));\r\n      };\r\n\r\n      // Send the request\r\n      xhr.send(formData);\r\n    } catch (error) {\r\n      reject(error);\r\n    }\r\n  });\r\n};\r\n\r\n/**\r\n * Upload multiple files to the server\r\n * @param {File[]} files - Array of files to upload\r\n * @param {Function} onProgress - Callback function for upload progress\r\n * @returns {Promise} - Promise that resolves with an array of server responses\r\n */\r\nexport const uploadMultipleFiles = async (\r\n  files: File[],\r\n  onProgress?: (fileName: string, progress: number) => void\r\n): Promise<any[]> => {\r\n  const uploadPromises = files.map((file) => {\r\n    return uploadFile(file, (progress) => {\r\n      if (onProgress) {\r\n        onProgress(file.name, progress);\r\n      }\r\n    });\r\n  });\r\n\r\n  return Promise.all(uploadPromises);\r\n};\r\n\r\n/**\r\n * Check if a file type is allowed\r\n * @param {string} fileType - MIME type of the file\r\n * @param {string[]} allowedTypes - Array of allowed MIME types\r\n * @returns {boolean} - True if the file type is allowed\r\n */\r\nexport const isFileTypeAllowed = (fileType: string, allowedTypes: string[]): boolean => {\r\n  return allowedTypes.includes(fileType);\r\n};\r\n\r\n/**\r\n * Check if a file size is within the limit\r\n * @param {number} fileSize - Size of the file in bytes\r\n * @param {number} maxSizeMB - Maximum allowed size in MB\r\n * @returns {boolean} - True if the file size is within the limit\r\n */\r\nexport const isFileSizeValid = (fileSize: number, maxSizeMB: number): boolean => {\r\n  const maxSizeBytes = maxSizeMB * 1024 * 1024;\r\n  return fileSize <= maxSizeBytes;\r\n};\r\n\r\n/**\r\n * List all CSV files stored in the database\r\n * @param {string} clientEmail - Optional client email to filter by\r\n * @returns {Promise} - Promise that resolves with the list of CSV files\r\n */\r\nexport const listCSVFiles = async (clientEmail?: string): Promise<any> => {\r\n  try {\r\n    const response = await fetch('http://localhost:5010/api/list-csv-files', {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({\r\n        client_email: clientEmail\r\n      })\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`Server error: ${response.status} ${response.statusText}`);\r\n    }\r\n\r\n    return await response.json();\r\n  } catch (error) {\r\n    console.error('Error listing CSV files:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * Get CSV data from the database\r\n * @param {string} indexName - Name of the index to get data for\r\n * @param {number} limit - Maximum number of rows to return (default: 100)\r\n * @param {number} offset - Number of rows to skip (default: 0)\r\n * @returns {Promise} - Promise that resolves with the CSV data\r\n */\r\nexport const getCSVData = async (indexName: string, limit: number = 100, offset: number = 0): Promise<any> => {\r\n  try {\r\n    const response = await fetch('http://localhost:5010/api/get-csv-data', {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({\r\n        index_name: indexName,\r\n        limit,\r\n        offset\r\n      })\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`Server error: ${response.status} ${response.statusText}`);\r\n    }\r\n\r\n    return await response.json();\r\n  } catch (error) {\r\n    console.error('Error getting CSV data:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * Get available embedding models\r\n * @returns {Promise} - Promise that resolves with the list of available embedding models\r\n */\r\nexport const getEmbeddingModels = async (): Promise<any> => {\r\n  try {\r\n    const response = await fetch('http://localhost:5010/api/list-embedding-models', {\r\n      method: 'GET',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      }\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`Server error: ${response.status} ${response.statusText}`);\r\n    }\r\n\r\n    return await response.json();\r\n  } catch (error) {\r\n    console.error('Error getting embedding models:', error);\r\n\r\n    // Return fallback data when backend is not available\r\n    return {\r\n      success: true,\r\n      models: {\r\n        \"all-MiniLM-L6-v2\": {\r\n          \"name\": \"all-MiniLM-L6-v2\",\r\n          \"description\": \"Sentence Transformers model for semantic similarity\",\r\n          \"dimensions\": 384\r\n        },\r\n        \"all-mpnet-base-v2\": {\r\n          \"name\": \"all-mpnet-base-v2\",\r\n          \"description\": \"High-quality sentence embeddings\",\r\n          \"dimensions\": 768\r\n        },\r\n        \"paraphrase-MiniLM-L6-v2\": {\r\n          \"name\": \"paraphrase-MiniLM-L6-v2\",\r\n          \"description\": \"Paraphrase detection model\",\r\n          \"dimensions\": 384\r\n        }\r\n      },\r\n      default_model: \"all-MiniLM-L6-v2\"\r\n    };\r\n  }\r\n};\r\n\r\n/**\r\n * Check if an error or response is a cancellation\r\n * @param {any} errorOrResponse - Error object or response from upload/cancel functions\r\n * @returns {boolean} - True if the error/response indicates a cancellation\r\n */\r\nexport const isCancellation = (errorOrResponse: any): boolean => {\r\n  // Check for our custom cancellation response\r\n  if (errorOrResponse && errorOrResponse.cancelled === true) {\r\n    return true;\r\n  }\r\n\r\n  // Check for error message containing cancellation text\r\n  if (errorOrResponse instanceof Error) {\r\n    const errorMessage = errorOrResponse.message.toLowerCase();\r\n    return errorMessage.includes('cancel') ||\r\n           errorMessage.includes('abort') ||\r\n           errorMessage.includes('user interrupt');\r\n  }\r\n\r\n  // Check for response with cancellation status\r\n  if (errorOrResponse && errorOrResponse.status === 'cancelled') {\r\n    return true;\r\n  }\r\n\r\n  // Check for response with error_type indicating cancellation\r\n  if (errorOrResponse && errorOrResponse.error_type === 'upload_cancelled') {\r\n    return true;\r\n  }\r\n\r\n  return false;\r\n};\r\n\r\n/**\r\n * Fetch emails from the API\r\n * @returns {Promise<string[]>} - Promise that resolves with an array of email addresses\r\n */\r\nexport const fetchEmails = async (): Promise<string[]> => {\r\n  try {\r\n    const response = await fetch('https://dev-commonmannit.mannit.co/mannit/eSearch', {\r\n      method: 'GET',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'xxxid': 'QUKTYWK'\r\n      }\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`Server error: ${response.status}`);\r\n    }\r\n\r\n    const data = await response.json();\r\n\r\n    if (data.statusCode === 200 && Array.isArray(data.source)) {\r\n      // Parse each JSON string in the source array and extract emails\r\n      const emails = data.source.map((jsonStr: string) => {\r\n        try {\r\n          const userObj = JSON.parse(jsonStr);\r\n          return userObj.email || '';\r\n        } catch (error) {\r\n          console.error('Error parsing JSON:', error);\r\n          return '';\r\n        }\r\n      }).filter(Boolean); // Remove empty strings\r\n\r\n      return emails;\r\n    }\r\n\r\n    return [];\r\n  } catch (error) {\r\n    console.error('Error fetching emails:', error);\r\n    return [];\r\n  }\r\n};\r\n\r\n/**\r\n * Create PINE Collection Entry for index storage\r\n * @param {string} embedModel - Embedding model name (stored as api_key)\r\n * @param {string} indexName - Index name\r\n * @param {string} clientEmail - Client email\r\n * @returns {Promise<any>} - Promise that resolves with the server response\r\n */\r\nexport const createPineCollectionEntry = async (\r\n  embedModel: string,\r\n  indexName: string,\r\n  clientEmail: string\r\n): Promise<any> => {\r\n  try {\r\n    console.log(`Creating PINE collection entry: embedModel=${embedModel}, indexName=${indexName}, clientEmail=${clientEmail}`);\r\n\r\n    const response = await fetch('https://dev-commonmannit.mannit.co/mannit/eCreateCol?colname=PINE', {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'xxxid': 'PINE'\r\n      },\r\n      body: JSON.stringify({\r\n        api_key: embedModel,    // Store embedding model name as api_key\r\n        index_name: indexName,  // Store provided index name\r\n        client: clientEmail     // Store email as client\r\n      })\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorText = await response.text();\r\n      throw new Error(`Server error: ${response.status} - ${errorText}`);\r\n    }\r\n\r\n    const result = await response.json();\r\n    console.log('PINE collection entry created successfully:', result);\r\n    return result;\r\n  } catch (error) {\r\n    console.error('Error creating PINE collection entry:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * Get all indexes for a specific email from PINE collection\r\n * @param {string} clientEmail - Client email to filter by\r\n * @returns {Promise<any[]>} - Promise that resolves with array of index data\r\n */\r\nexport const getIndexesByEmail = async (clientEmail: string): Promise<any[]> => {\r\n  try {\r\n    console.log(`Fetching indexes for email: ${clientEmail}`);\r\n\r\n    const response = await fetch(\r\n      `https://dev-commonmannit.mannit.co/mannit/retrievecollection?ColName=PINE&filtercount=1&f1_field=client_S&f1_op=eq&f1_value=${encodeURIComponent(clientEmail)}`,\r\n      {\r\n        method: 'GET',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'xxxid': 'PINE'\r\n        }\r\n      }\r\n    );\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`Server error: ${response.status}`);\r\n    }\r\n\r\n    const data = await response.json();\r\n    console.log('PINE collection response for email:', data);\r\n\r\n    if (data.statusCode === 200 && Array.isArray(data.source)) {\r\n      // Parse each JSON string in the source array\r\n      const indexes = data.source.map((jsonStr: string, index: number) => {\r\n        try {\r\n          const indexObj = JSON.parse(jsonStr);\r\n          return {\r\n            _id: indexObj._id?.$oid || indexObj._id || `pine-item-${index}`,\r\n            email: indexObj.client || clientEmail,\r\n            index_name: indexObj.index_name || 'N/A',\r\n            embed_model: indexObj.api_key || 'N/A', // api_key contains the embedding model\r\n            source: 'PINE' as const,\r\n            originalData: indexObj\r\n          };\r\n        } catch (error) {\r\n          console.error('Error parsing PINE index JSON:', error);\r\n          return null;\r\n        }\r\n      }).filter((item: any) => item !== null);\r\n\r\n      return indexes;\r\n    }\r\n\r\n    return [];\r\n  } catch (error) {\r\n    console.error('Error fetching indexes by email:', error);\r\n    return [];\r\n  }\r\n};\r\n\r\n/**\r\n * Check if a FAISS index exists\r\n * @param {string} indexName - Name of the index to check\r\n * @param {string} client - Client email or identifier (optional)\r\n * @param {string} embedModel - Name of the embedding model to use (optional)\r\n * @returns {Promise<{exists: boolean, embedding_model?: string}>} - Promise that resolves with info about the index\r\n */\r\nexport const checkIndexExists = async (\r\n  indexName: string,\r\n  client?: string,\r\n  embedModel?: string\r\n): Promise<{exists: boolean, embedding_model?: string}> => {\r\n  try {\r\n    const response = await fetch('http://localhost:5010/api/check-index', {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({\r\n        index_name: indexName,\r\n        client: client,\r\n        embed_model: embedModel\r\n      })\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`Server error: ${response.status}`);\r\n    }\r\n\r\n    const data = await response.json();\r\n\r\n    if (data.success) {\r\n      return {\r\n        exists: data.exists,\r\n        embedding_model: data.embedding_model\r\n      };\r\n    }\r\n\r\n    return { exists: false };\r\n  } catch (error) {\r\n    console.error('Error checking if index exists:', error);\r\n    return { exists: false };\r\n  }\r\n};\r\n\r\n/**\r\n * Cancel an ongoing upload\r\n * @param {string} uploadId - The ID of the upload to cancel\r\n * @param {AbortController} abortController - Optional AbortController to abort the HTTP request\r\n * @returns {Promise<any>} - Promise that resolves with the server response\r\n */\r\nexport const cancelUpload = async (uploadId: string, abortController?: AbortController): Promise<any> => {\r\n  try {\r\n    // First, abort the HTTP request if an AbortController is provided\r\n    if (abortController) {\r\n      try {\r\n        abortController.abort();\r\n        console.log('HTTP request aborted');\r\n      } catch (abortError) {\r\n        // Don't log this as an error since it's expected behavior\r\n        console.log('Note: AbortController already used or not applicable');\r\n        // Continue with server-side cancellation even if client-side abort fails\r\n      }\r\n    }\r\n\r\n    // Then, send a cancellation request to the server\r\n    console.log(`Sending cancellation request for upload ${uploadId}`);\r\n    const response = await fetch('http://localhost:5010/api/cancel-upload', {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({\r\n        upload_id: uploadId\r\n      })\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorText = await response.text();\r\n      throw new Error(`Server error: ${response.status} - ${errorText}`);\r\n    }\r\n\r\n    const data = await response.json();\r\n    console.log('Cancellation response:', data);\r\n\r\n    // Verify cancellation by checking status\r\n    try {\r\n      const statusResponse = await checkUploadStatus(uploadId);\r\n      console.log('Status after cancellation:', statusResponse);\r\n    } catch (statusError) {\r\n      console.error('Error checking status after cancellation:', statusError);\r\n      // Continue even if status check fails\r\n    }\r\n\r\n    return data;\r\n  } catch (error) {\r\n    console.error('Error cancelling upload:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * Check the status of an ongoing upload\r\n * @param {string} uploadId - The ID of the upload to check\r\n * @param {boolean} silent - Whether to suppress console errors (default: false)\r\n * @returns {Promise<any>} - Promise that resolves with the upload status\r\n */\r\nexport const checkUploadStatus = async (uploadId: string, silent: boolean = false): Promise<any> => {\r\n  try {\r\n    const response = await fetch('http://localhost:5010/api/upload-status', {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({\r\n        upload_id: uploadId\r\n      })\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorText = await response.text();\r\n      throw new Error(`Server error: ${response.status} - ${errorText}`);\r\n    }\r\n\r\n    const data = await response.json();\r\n\r\n    // Log cancellation status if detected\r\n    if (data.success && data.cancelled) {\r\n      console.log(`Upload ${uploadId} is marked as cancelled. Status: ${data.status}`);\r\n    }\r\n\r\n    return data;\r\n  } catch (error) {\r\n    if (!silent) {\r\n      console.error('Error checking upload status:', error);\r\n    }\r\n    throw error;\r\n  }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;;;;;;;;;;;;;;;AACM,MAAM,iBAAiB,CAAC;IAC7B,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;QAAM;KAAK;IAC/C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAaO,MAAM,mBAAmB,OAC9B,MACA,aACA,WACA,YACA,QACA,YACA;IAEA,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,IAAI;YACF,qBAAqB;YACrB,IAAI,KAAK,IAAI,KAAK,YAAY;gBAC5B,OAAO,IAAI,MAAM;gBACjB;YACF;YAEA,iCAAiC;YACjC,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YAExB,kDAAkD;YAClD,IAAI,aAAa;gBACf,SAAS,MAAM,CAAC,UAAU;YAC5B;YAEA,8BAA8B;YAC9B,IAAI,WAAW;gBACb,SAAS,MAAM,CAAC,cAAc;YAChC;YAEA,8BAA8B;YAC9B,IAAI,WAAW;gBACb,SAAS,MAAM,CAAC,cAAc;YAChC;YAEA,2CAA2C;YAC3C,IAAI,YAAY;gBACd,SAAS,MAAM,CAAC,eAAe;YACjC;YAEA,+CAA+C;YAC/C,IAAI,YAAY;gBACd,SAAS,MAAM,CAAC,eAAe;YACjC;YAEA,uDAAuD;YACvD,MAAM,MAAM,IAAI;YAEhB,mDAAmD;YACnD,IAAI,QAAQ;gBACV,OAAO,OAAO,GAAG;oBACf,IAAI,KAAK;oBACT,yEAAyE;oBACzE,wDAAwD;oBACxD,QAAQ;wBACN,SAAS;wBACT,WAAW;wBACX,SAAS;oBACX;gBACF;YACF;YAEA,gDAAgD;YAChD,IAAI,IAAI,CAAC,QAAQ,wCAAwC;YACzD,gEAAgE;YAChE,IAAI,gBAAgB,CAAC,SAAS;YAE9B,6CAA6C;YAC7C,IAAI,YAAY;gBACd,IAAI,MAAM,CAAC,UAAU,GAAG,CAAC;oBACvB,IAAI,MAAM,gBAAgB,EAAE;wBAC1B,MAAM,WAAW,KAAK,KAAK,CAAC,AAAC,MAAM,MAAM,GAAG,MAAM,KAAK,GAAI;wBAC3D,WAAW;oBACb;gBACF;YACF;YAEA,kBAAkB;YAClB,IAAI,MAAM,GAAG;gBACX,IAAI,IAAI,MAAM,IAAI,OAAO,IAAI,MAAM,GAAG,KAAK;oBACzC,IAAI;wBACF,MAAM,WAAW,KAAK,KAAK,CAAC,IAAI,YAAY;wBAC5C,QAAQ;oBACV,EAAE,OAAO,OAAO;wBACd,QAAQ;4BACN,SAAS;4BACT,SAAS;4BACT,WAAW,UAAU,mCAAmC;wBAC1D;oBACF;gBACF,OAAO;oBACL,OAAO,IAAI,MAAM,CAAC,cAAc,EAAE,IAAI,MAAM,CAAC,CAAC,EAAE,IAAI,UAAU,EAAE;gBAClE;YACF;YAEA,wBAAwB;YACxB,IAAI,OAAO,GAAG;gBACZ,OAAO,IAAI,MAAM;YACnB;YAEA,mBAAmB;YACnB,IAAI,IAAI,CAAC;QACX,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;AACF;AAGO,MAAM,sBAAsB;AAQ5B,MAAM,aAAa,OACxB,MACA;IAEA,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,IAAI;YACF,iCAAiC;YACjC,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YAExB,8BAA8B;YAC9B,MAAM,MAAM,IAAI;YAEhB,wBAAwB;YACxB,IAAI,IAAI,CAAC,QAAQ,oCAAoC;YAErD,wBAAwB;YACxB,IAAI,MAAM,CAAC,UAAU,GAAG,CAAC;gBACvB,IAAI,MAAM,gBAAgB,IAAI,YAAY;oBACxC,MAAM,WAAW,KAAK,KAAK,CAAC,AAAC,MAAM,MAAM,GAAG,MAAM,KAAK,GAAI;oBAC3D,WAAW;gBACb;YACF;YAEA,kBAAkB;YAClB,IAAI,MAAM,GAAG;gBACX,IAAI,IAAI,MAAM,IAAI,OAAO,IAAI,MAAM,GAAG,KAAK;oBACzC,IAAI;wBACF,MAAM,WAAW,KAAK,KAAK,CAAC,IAAI,YAAY;wBAC5C,QAAQ;oBACV,EAAE,OAAO,OAAO;wBACd,QAAQ;4BACN,SAAS;4BACT,SAAS;4BACT,UAAU,KAAK,IAAI;wBACrB;oBACF;gBACF,OAAO;oBACL,OAAO,IAAI,MAAM,CAAC,cAAc,EAAE,IAAI,MAAM,CAAC,CAAC,EAAE,IAAI,UAAU,EAAE;gBAClE;YACF;YAEA,wBAAwB;YACxB,IAAI,OAAO,GAAG;gBACZ,OAAO,IAAI,MAAM;YACnB;YAEA,mBAAmB;YACnB,IAAI,IAAI,CAAC;QACX,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;AACF;AAQO,MAAM,sBAAsB,OACjC,OACA;IAEA,MAAM,iBAAiB,MAAM,GAAG,CAAC,CAAC;QAChC,OAAO,WAAW,MAAM,CAAC;YACvB,IAAI,YAAY;gBACd,WAAW,KAAK,IAAI,EAAE;YACxB;QACF;IACF;IAEA,OAAO,QAAQ,GAAG,CAAC;AACrB;AAQO,MAAM,oBAAoB,CAAC,UAAkB;IAClD,OAAO,aAAa,QAAQ,CAAC;AAC/B;AAQO,MAAM,kBAAkB,CAAC,UAAkB;IAChD,MAAM,eAAe,YAAY,OAAO;IACxC,OAAO,YAAY;AACrB;AAOO,MAAM,eAAe,OAAO;IACjC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,4CAA4C;YACvE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,cAAc;YAChB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QAC3E;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AASO,MAAM,aAAa,OAAO,WAAmB,QAAgB,GAAG,EAAE,SAAiB,CAAC;IACzF,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,0CAA0C;YACrE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,YAAY;gBACZ;gBACA;YACF;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QAC3E;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF;AAMO,MAAM,qBAAqB;IAChC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,mDAAmD;YAC9E,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QAC3E;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QAEjD,qDAAqD;QACrD,OAAO;YACL,SAAS;YACT,QAAQ;gBACN,oBAAoB;oBAClB,QAAQ;oBACR,eAAe;oBACf,cAAc;gBAChB;gBACA,qBAAqB;oBACnB,QAAQ;oBACR,eAAe;oBACf,cAAc;gBAChB;gBACA,2BAA2B;oBACzB,QAAQ;oBACR,eAAe;oBACf,cAAc;gBAChB;YACF;YACA,eAAe;QACjB;IACF;AACF;AAOO,MAAM,iBAAiB,CAAC;IAC7B,6CAA6C;IAC7C,IAAI,mBAAmB,gBAAgB,SAAS,KAAK,MAAM;QACzD,OAAO;IACT;IAEA,uDAAuD;IACvD,IAAI,2BAA2B,OAAO;QACpC,MAAM,eAAe,gBAAgB,OAAO,CAAC,WAAW;QACxD,OAAO,aAAa,QAAQ,CAAC,aACtB,aAAa,QAAQ,CAAC,YACtB,aAAa,QAAQ,CAAC;IAC/B;IAEA,8CAA8C;IAC9C,IAAI,mBAAmB,gBAAgB,MAAM,KAAK,aAAa;QAC7D,OAAO;IACT;IAEA,6DAA6D;IAC7D,IAAI,mBAAmB,gBAAgB,UAAU,KAAK,oBAAoB;QACxE,OAAO;IACT;IAEA,OAAO;AACT;AAMO,MAAM,cAAc;IACzB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,qDAAqD;YAChF,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,SAAS;YACX;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,SAAS,MAAM,EAAE;QACpD;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,KAAK,UAAU,KAAK,OAAO,MAAM,OAAO,CAAC,KAAK,MAAM,GAAG;YACzD,gEAAgE;YAChE,MAAM,SAAS,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC;gBAC9B,IAAI;oBACF,MAAM,UAAU,KAAK,KAAK,CAAC;oBAC3B,OAAO,QAAQ,KAAK,IAAI;gBAC1B,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,uBAAuB;oBACrC,OAAO;gBACT;YACF,GAAG,MAAM,CAAC,UAAU,uBAAuB;YAE3C,OAAO;QACT;QAEA,OAAO,EAAE;IACX,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,EAAE;IACX;AACF;AASO,MAAM,4BAA4B,OACvC,YACA,WACA;IAEA,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,WAAW,YAAY,EAAE,UAAU,cAAc,EAAE,aAAa;QAE1H,MAAM,WAAW,MAAM,MAAM,qEAAqE;YAChG,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,SAAS;YACX;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,SAAS;gBACT,YAAY;gBACZ,QAAQ,YAAgB,wBAAwB;YAClD;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,SAAS,MAAM,CAAC,GAAG,EAAE,WAAW;QACnE;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,QAAQ,GAAG,CAAC,+CAA+C;QAC3D,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;QACvD,MAAM;IACR;AACF;AAOO,MAAM,oBAAoB,OAAO;IACtC,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,aAAa;QAExD,MAAM,WAAW,MAAM,MACrB,CAAC,4HAA4H,EAAE,mBAAmB,cAAc,EAChK;YACE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,SAAS;YACX;QACF;QAGF,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,SAAS,MAAM,EAAE;QACpD;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,uCAAuC;QAEnD,IAAI,KAAK,UAAU,KAAK,OAAO,MAAM,OAAO,CAAC,KAAK,MAAM,GAAG;YACzD,6CAA6C;YAC7C,MAAM,UAAU,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,SAAiB;gBAChD,IAAI;oBACF,MAAM,WAAW,KAAK,KAAK,CAAC;oBAC5B,OAAO;wBACL,KAAK,SAAS,GAAG,EAAE,QAAQ,SAAS,GAAG,IAAI,CAAC,UAAU,EAAE,OAAO;wBAC/D,OAAO,SAAS,MAAM,IAAI;wBAC1B,YAAY,SAAS,UAAU,IAAI;wBACnC,aAAa,SAAS,OAAO,IAAI;wBACjC,QAAQ;wBACR,cAAc;oBAChB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,kCAAkC;oBAChD,OAAO;gBACT;YACF,GAAG,MAAM,CAAC,CAAC,OAAc,SAAS;YAElC,OAAO;QACT;QAEA,OAAO,EAAE;IACX,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO,EAAE;IACX;AACF;AASO,MAAM,mBAAmB,OAC9B,WACA,QACA;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,yCAAyC;YACpE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,YAAY;gBACZ,QAAQ;gBACR,aAAa;YACf;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,SAAS,MAAM,EAAE;QACpD;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,KAAK,OAAO,EAAE;YAChB,OAAO;gBACL,QAAQ,KAAK,MAAM;gBACnB,iBAAiB,KAAK,eAAe;YACvC;QACF;QAEA,OAAO;YAAE,QAAQ;QAAM;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO;YAAE,QAAQ;QAAM;IACzB;AACF;AAQO,MAAM,eAAe,OAAO,UAAkB;IACnD,IAAI;QACF,kEAAkE;QAClE,IAAI,iBAAiB;YACnB,IAAI;gBACF,gBAAgB,KAAK;gBACrB,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,YAAY;gBACnB,0DAA0D;gBAC1D,QAAQ,GAAG,CAAC;YACZ,yEAAyE;YAC3E;QACF;QAEA,kDAAkD;QAClD,QAAQ,GAAG,CAAC,CAAC,wCAAwC,EAAE,UAAU;QACjE,MAAM,WAAW,MAAM,MAAM,2CAA2C;YACtE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,WAAW;YACb;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,SAAS,MAAM,CAAC,GAAG,EAAE,WAAW;QACnE;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,0BAA0B;QAEtC,yCAAyC;QACzC,IAAI;YACF,MAAM,iBAAiB,MAAM,kBAAkB;YAC/C,QAAQ,GAAG,CAAC,8BAA8B;QAC5C,EAAE,OAAO,aAAa;YACpB,QAAQ,KAAK,CAAC,6CAA6C;QAC3D,sCAAsC;QACxC;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAQO,MAAM,oBAAoB,OAAO,UAAkB,SAAkB,KAAK;IAC/E,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,2CAA2C;YACtE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,WAAW;YACb;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,SAAS,MAAM,CAAC,GAAG,EAAE,WAAW;QACnE;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,sCAAsC;QACtC,IAAI,KAAK,OAAO,IAAI,KAAK,SAAS,EAAE;YAClC,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,SAAS,iCAAiC,EAAE,KAAK,MAAM,EAAE;QACjF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,IAAI,CAAC,QAAQ;YACX,QAAQ,KAAK,CAAC,iCAAiC;QACjD;QACA,MAAM;IACR;AACF"}}, {"offset": {"line": 3211, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3217, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/services/mockServer.ts"], "sourcesContent": ["/**\r\n * Mock server for file upload API\r\n * This file simulates a backend server for handling file uploads during development\r\n *\r\n * In a real application, you would replace this with actual API calls to your backend\r\n */\r\n\r\n/**\r\n * Simulates uploading a CSV file to Pinecone\r\n * @param {File} file - The CSV file to upload\r\n * @returns {Promise} - Promise that resolves with a mock response\r\n */\r\nexport const mockUploadCSVToPinecone = (file: File) => {\r\n  return new Promise<{\r\n    success: boolean;\r\n    indexName: string;\r\n    vectorCount: number;\r\n    message: string;\r\n  }>((resolve, reject) => {\r\n    // Validate file type\r\n    if (file.type !== 'text/csv') {\r\n      reject(new Error('Only CSV files are supported for Pinecone upload'));\r\n      return;\r\n    }\r\n\r\n    // Simulate network delay\r\n    setTimeout(() => {\r\n      // Create index name from file name\r\n      const indexName = file.name.replace('.csv', '').replace(/\\s+/g, '_').toLowerCase();\r\n\r\n      // Randomly succeed or fail (95% success rate)\r\n      const shouldSucceed = Math.random() < 0.95;\r\n\r\n      if (shouldSucceed) {\r\n        resolve({\r\n          success: true,\r\n          indexName: indexName,\r\n          vectorCount: Math.floor(Math.random() * 1000) + 100, // Random number of vectors\r\n          message: `CSV data successfully uploaded to Pinecone index: ${indexName}`\r\n        });\r\n      } else {\r\n        reject(new Error('Server error: Failed to upload CSV to Pinecone'));\r\n      }\r\n    }, 2000); // Simulate a 2 second delay for processing\r\n  });\r\n};\r\n\r\n/**\r\n * Simulates a file upload to the server\r\n * @param {File} file - The file to upload\r\n * @returns {Promise} - Promise that resolves with a mock response\r\n */\r\nexport const mockUploadFile = (file: File) => {\r\n  return new Promise<{\r\n    success: boolean;\r\n    fileId: string;\r\n    fileName: string;\r\n    fileSize: number;\r\n    fileType: string;\r\n    uploadDate: string;\r\n    url: string;\r\n    message: string;\r\n  }>((resolve, reject) => {\r\n    // Simulate network delay\r\n    setTimeout(() => {\r\n      // Randomly succeed or fail (90% success rate)\r\n      const shouldSucceed = Math.random() < 0.9;\r\n\r\n      if (shouldSucceed) {\r\n        resolve({\r\n          success: true,\r\n          fileId: `file-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`,\r\n          fileName: file.name,\r\n          fileSize: file.size,\r\n          fileType: file.type,\r\n          uploadDate: new Date().toISOString(),\r\n          url: `https://example.com/files/${file.name}`,\r\n          message: 'File uploaded successfully'\r\n        });\r\n      } else {\r\n        reject(new Error('Server error: Failed to upload file'));\r\n      }\r\n    }, 1500); // Simulate a 1.5 second delay\r\n  });\r\n};\r\n\r\n/**\r\n * Simulates uploading multiple files to the server\r\n * @param {File[]} files - Array of files to upload\r\n * @returns {Promise} - Promise that resolves with an array of mock responses\r\n */\r\nexport const mockUploadMultipleFiles = (files: File[]) => {\r\n  const uploadPromises = files.map(file => mockUploadFile(file));\r\n  return Promise.all(uploadPromises);\r\n};\r\n\r\n/**\r\n * Simulates getting a list of uploaded files from the server\r\n * @returns {Promise} - Promise that resolves with a mock response\r\n */\r\nexport const mockGetUploadedFiles = () => {\r\n  return new Promise<{\r\n    success: boolean;\r\n    files: Array<{\r\n      fileId: string;\r\n      fileName: string;\r\n      fileSize: number;\r\n      fileType: string;\r\n      uploadDate: string;\r\n      url: string;\r\n    }>;\r\n  }>((resolve) => {\r\n    // Simulate network delay\r\n    setTimeout(() => {\r\n      resolve({\r\n        success: true,\r\n        files: [\r\n          {\r\n            fileId: 'file-1234567890',\r\n            fileName: 'example-document.pdf',\r\n            fileSize: 1024 * 1024 * 2.5, // 2.5 MB\r\n            fileType: 'application/pdf',\r\n            uploadDate: '2023-05-15T10:30:00Z',\r\n            url: 'https://example.com/files/example-document.pdf'\r\n          },\r\n          {\r\n            fileId: 'file-0987654321',\r\n            fileName: 'sample-image.jpg',\r\n            fileSize: 1024 * 512, // 512 KB\r\n            fileType: 'image/jpeg',\r\n            uploadDate: '2023-05-14T15:45:00Z',\r\n            url: 'https://example.com/files/sample-image.jpg'\r\n          }\r\n        ]\r\n      });\r\n    }, 800); // Simulate a 0.8 second delay\r\n  });\r\n};\r\n\r\n/**\r\n * Simulates deleting a file from the server\r\n * @param {string} fileId - ID of the file to delete\r\n * @returns {Promise} - Promise that resolves with a mock response\r\n */\r\nexport const mockDeleteFile = (fileId: string) => {\r\n  return new Promise<{\r\n    success: boolean;\r\n    fileId: string;\r\n    message: string;\r\n  }>((resolve) => {\r\n    // Simulate network delay\r\n    setTimeout(() => {\r\n      resolve({\r\n        success: true,\r\n        fileId,\r\n        message: 'File deleted successfully'\r\n      });\r\n    }, 500); // Simulate a 0.5 second delay\r\n  });\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAED;;;;CAIC;;;;;;;AACM,MAAM,0BAA0B,CAAC;IACtC,OAAO,IAAI,QAKR,CAAC,SAAS;QACX,qBAAqB;QACrB,IAAI,KAAK,IAAI,KAAK,YAAY;YAC5B,OAAO,IAAI,MAAM;YACjB;QACF;QAEA,yBAAyB;QACzB,WAAW;YACT,mCAAmC;YACnC,MAAM,YAAY,KAAK,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ,KAAK,WAAW;YAEhF,8CAA8C;YAC9C,MAAM,gBAAgB,KAAK,MAAM,KAAK;YAEtC,IAAI,eAAe;gBACjB,QAAQ;oBACN,SAAS;oBACT,WAAW;oBACX,aAAa,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ;oBAChD,SAAS,CAAC,kDAAkD,EAAE,WAAW;gBAC3E;YACF,OAAO;gBACL,OAAO,IAAI,MAAM;YACnB;QACF,GAAG,OAAO,2CAA2C;IACvD;AACF;AAOO,MAAM,iBAAiB,CAAC;IAC7B,OAAO,IAAI,QASR,CAAC,SAAS;QACX,yBAAyB;QACzB,WAAW;YACT,8CAA8C;YAC9C,MAAM,gBAAgB,KAAK,MAAM,KAAK;YAEtC,IAAI,eAAe;gBACjB,QAAQ;oBACN,SAAS;oBACT,QAAQ,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,KAAK;oBAC3E,UAAU,KAAK,IAAI;oBACnB,UAAU,KAAK,IAAI;oBACnB,UAAU,KAAK,IAAI;oBACnB,YAAY,IAAI,OAAO,WAAW;oBAClC,KAAK,CAAC,0BAA0B,EAAE,KAAK,IAAI,EAAE;oBAC7C,SAAS;gBACX;YACF,OAAO;gBACL,OAAO,IAAI,MAAM;YACnB;QACF,GAAG,OAAO,8BAA8B;IAC1C;AACF;AAOO,MAAM,0BAA0B,CAAC;IACtC,MAAM,iBAAiB,MAAM,GAAG,CAAC,CAAA,OAAQ,eAAe;IACxD,OAAO,QAAQ,GAAG,CAAC;AACrB;AAMO,MAAM,uBAAuB;IAClC,OAAO,IAAI,QAUR,CAAC;QACF,yBAAyB;QACzB,WAAW;YACT,QAAQ;gBACN,SAAS;gBACT,OAAO;oBACL;wBACE,QAAQ;wBACR,UAAU;wBACV,UAAU,OAAO,OAAO;wBACxB,UAAU;wBACV,YAAY;wBACZ,KAAK;oBACP;oBACA;wBACE,QAAQ;wBACR,UAAU;wBACV,UAAU,OAAO;wBACjB,UAAU;wBACV,YAAY;wBACZ,KAAK;oBACP;iBACD;YACH;QACF,GAAG,MAAM,8BAA8B;IACzC;AACF;AAOO,MAAM,iBAAiB,CAAC;IAC7B,OAAO,IAAI,QAIR,CAAC;QACF,yBAAyB;QACzB,WAAW;YACT,QAAQ;gBACN,SAAS;gBACT;gBACA,SAAS;YACX;QACF,GAAG,MAAM,8BAA8B;IACzC;AACF"}}, {"offset": {"line": 3329, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3335, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/services/fileDownloadService.ts"], "sourcesContent": ["/**\r\n * Service for handling file downloads\r\n */\r\n\r\n/**\r\n * Download a sample CSV file\r\n * @param {string} fileName - Name of the sample file to download\r\n * @returns {Promise<boolean>} - Promise that resolves to true if download was successful\r\n */\r\nexport const downloadSampleFile = async (fileName: string = 'querry.csv'): Promise<boolean> => {\r\n  try {\r\n    // First check if the file exists by making a HEAD request\r\n    try {\r\n      // Use the public directory path\r\n      const checkResponse = await fetch(`/${fileName}`, { method: 'HEAD' });\r\n      if (!checkResponse.ok) {\r\n        console.error(`Sample file ${fileName} not found. Status: ${checkResponse.status}`);\r\n        return false;\r\n      }\r\n    } catch (checkError) {\r\n      console.error('Error checking if sample file exists:', checkError);\r\n      return false;\r\n    }\r\n\r\n    // Create a URL to the file in the public directory\r\n    const fileUrl = `/${fileName}`;\r\n\r\n    // Create a link element\r\n    const link = document.createElement('a');\r\n    link.href = fileUrl;\r\n    link.download = fileName;\r\n\r\n    // Append to the document\r\n    document.body.appendChild(link);\r\n\r\n    // Trigger the download\r\n    link.click();\r\n\r\n    // Clean up\r\n    document.body.removeChild(link);\r\n\r\n    return true;\r\n  } catch (error) {\r\n    console.error('Error downloading sample file:', error);\r\n    return false;\r\n  }\r\n};\r\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;;;CAIC;;;AACM,MAAM,qBAAqB,OAAO,WAAmB,YAAY;IACtE,IAAI;QACF,0DAA0D;QAC1D,IAAI;YACF,gCAAgC;YAChC,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAC,EAAE,UAAU,EAAE;gBAAE,QAAQ;YAAO;YACnE,IAAI,CAAC,cAAc,EAAE,EAAE;gBACrB,QAAQ,KAAK,CAAC,CAAC,YAAY,EAAE,SAAS,oBAAoB,EAAE,cAAc,MAAM,EAAE;gBAClF,OAAO;YACT;QACF,EAAE,OAAO,YAAY;YACnB,QAAQ,KAAK,CAAC,yCAAyC;YACvD,OAAO;QACT;QAEA,mDAAmD;QACnD,MAAM,UAAU,CAAC,CAAC,EAAE,UAAU;QAE9B,wBAAwB;QACxB,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,IAAI,GAAG;QACZ,KAAK,QAAQ,GAAG;QAEhB,yBAAyB;QACzB,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,uBAAuB;QACvB,KAAK,KAAK;QAEV,WAAW;QACX,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO;IACT;AACF"}}, {"offset": {"line": 3381, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3387, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/app/%28with-layout%29/layout.tsx"], "sourcesContent": ["\"use client\";\r\nimport Footer from \"@/components/Footer\";\r\nimport Header from \"@/components/Header\";\r\nimport MainSidebar from \"@/components/MainSidebar\";\r\nimport MainModal from \"@/components/modals/MainModal\";\r\nimport GradientBackground from \"@/components/ui/GradientBackground\";\r\nimport ClientOnly from \"@/components/ui/ClientOnly\";\r\nimport HydrationBoundary from \"@/components/ui/HydrationBoundary\";\r\nimport { useChatHandler } from \"@/stores/chatList\";\r\nimport React, { useEffect, useState } from \"react\";\r\n\r\nfunction Layout({ children }: { children: React.ReactNode }) {\r\n  const [showSidebar, setShowSidebar] = useState(false);\r\n  const { updateChatList } = useChatHandler();\r\n\r\n  useEffect(() => {\r\n    // Only run on client side to prevent hydration errors\r\n    if (typeof window !== 'undefined') {\r\n      updateChatList();\r\n    }\r\n  }, [updateChatList]);\r\n\r\n  return (\r\n    <HydrationBoundary>\r\n      <ClientOnly fallback={\r\n        <div className=\"text-n500 bg-white relative z-10 h-dvh overflow-hidden dark:bg-n0 dark:text-n30\">\r\n          <div className=\"flex justify-center items-center h-full\">\r\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primaryColor\"></div>\r\n          </div>\r\n        </div>\r\n      }>\r\n        <div className=\"text-n500 bg-white relative z-10 h-dvh overflow-hidden dark:bg-n0 dark:text-n30\">\r\n          <GradientBackground />\r\n          <div className=\"flex justify-start items-start h-full\">\r\n            <MainSidebar\r\n              showSidebar={showSidebar}\r\n              setShowSidebar={setShowSidebar}\r\n            />\r\n            <div className=\"flex-1 flex flex-col gap-2 sm:gap-3 justify-between items-center h-full pb-2 sm:pb-3 relative z-20 w-full overflow-hidden\">\r\n              <Header showSidebar={showSidebar} setShowSidebar={setShowSidebar} />\r\n              <div className=\"w-full flex-1 overflow-auto flex flex-col\">\r\n                {children}\r\n              </div>\r\n              <Footer />\r\n            </div>\r\n          </div>\r\n\r\n          {/* Modal */}\r\n          <MainModal />\r\n        </div>\r\n      </ClientOnly>\r\n    </HydrationBoundary>\r\n  );\r\n}\r\n\r\nexport default Layout;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;;AAWA,SAAS,OAAO,EAAE,QAAQ,EAAiC;;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAExC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,sDAAsD;YACtD,wCAAmC;gBACjC;YACF;QACF;2BAAG;QAAC;KAAe;IAEnB,qBACE,6LAAC,yIAAA,CAAA,UAAiB;kBAChB,cAAA,6LAAC,kIAAA,CAAA,UAAU;YAAC,wBACV,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;sBAInB,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,0IAAA,CAAA,UAAkB;;;;;kCACnB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6HAAA,CAAA,UAAW;gCACV,aAAa;gCACb,gBAAgB;;;;;;0CAElB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,wHAAA,CAAA,UAAM;wCAAC,aAAa;wCAAa,gBAAgB;;;;;;kDAClD,6LAAC;wCAAI,WAAU;kDACZ;;;;;;kDAEH,6LAAC,wHAAA,CAAA,UAAM;;;;;;;;;;;;;;;;;kCAKX,6LAAC,qIAAA,CAAA,UAAS;;;;;;;;;;;;;;;;;;;;;AAKpB;GA1CS;;QAEoB,qHAAA,CAAA,iBAAc;;;KAFlC;uCA4CM"}}, {"offset": {"line": 3538, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}