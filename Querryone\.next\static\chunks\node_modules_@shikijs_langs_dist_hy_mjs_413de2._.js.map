{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/testing-querryone/Querryone/node_modules/%40shikijs/langs/dist/hy.mjs"], "sourcesContent": ["const lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"Hy\\\",\\\"name\\\":\\\"hy\\\",\\\"patterns\\\":[{\\\"include\\\":\\\"#all\\\"}],\\\"repository\\\":{\\\"all\\\":{\\\"patterns\\\":[{\\\"include\\\":\\\"#comment\\\"},{\\\"include\\\":\\\"#constants\\\"},{\\\"include\\\":\\\"#keywords\\\"},{\\\"include\\\":\\\"#strings\\\"},{\\\"include\\\":\\\"#operators\\\"},{\\\"include\\\":\\\"#keysym\\\"},{\\\"include\\\":\\\"#builtin\\\"},{\\\"include\\\":\\\"#symbol\\\"}]},\\\"builtin\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<![\\\\\\\\.:\\\\\\\\w_\\\\\\\\-=!@\\\\\\\\$%^&?/<>*])(abs|all|any|ascii|bin|breakpoint|callable|chr|compile|delattr|dir|divmod|eval|exec|format|getattr|globals|hasattr|hash|hex|id|input|isinstance|issubclass|iter|aiter|len|locals|max|min|next|anext|oct|ord|pow|print|repr|round|setattr|sorted|sum|vars|False|None|True|NotImplemented|bool|memoryview|bytearray|bytes|classmethod|complex|dict|enumerate|filter|float|frozenset|property|int|list|map|object|range|reversed|set|slice|staticmethod|str|super|tuple|type|zip|open|quit|exit|copyright|credits|help)(?![\\\\\\\\.:\\\\\\\\w_\\\\\\\\-=!@\\\\\\\\$%^&?/<>*])\\\",\\\"name\\\":\\\"storage.builtin.hy\\\"},{\\\"match\\\":\\\"(?<=\\\\\\\\(\\\\\\\\s*)\\\\\\\\.\\\\\\\\.\\\\\\\\.(?![\\\\\\\\.:\\\\\\\\w_\\\\\\\\-=!@\\\\\\\\$%^&?/<>*])\\\",\\\"name\\\":\\\"storage.builtin.dots.hy\\\"}]},\\\"comment\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(;).*$\\\",\\\"name\\\":\\\"comment.line.hy\\\"}]},\\\"constants\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<=[\\\\\\\\{\\\\\\\\[\\\\\\\\(\\\\\\\\s])([0-9]+(\\\\\\\\.[0-9]+)?|(#x)[0-9a-fA-F]+|(#o)[0-7]+|(#b)[01]+)(?=[\\\\\\\\s;()'\\\\\\\",\\\\\\\\[\\\\\\\\]\\\\\\\\{\\\\\\\\}])\\\",\\\"name\\\":\\\"constant.numeric.hy\\\"}]},\\\"keysym\\\":{\\\"match\\\":\\\"(?<![\\\\\\\\.:\\\\\\\\w_\\\\\\\\-=!@\\\\\\\\$%^&?\\\\\\\\/<>*]):[\\\\\\\\.:\\\\\\\\w_\\\\\\\\-=!@\\\\\\\\$%^&?\\\\\\\\/<>*]*\\\",\\\"name\\\":\\\"variable.other.constant\\\"},\\\"keywords\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<![\\\\\\\\.:\\\\\\\\w_\\\\\\\\-=!@\\\\\\\\$%^&?/<>*])(and|await|match|let|annotate|assert|break|chainc|cond|continue|deftype|do|except\\\\\\\\*?|finally|else|defreader|([dgls])?for|set[vx]|defclass|defmacro|del|export|eval-and-compile|eval-when-compile|get|global|if|import|(de)?fn|nonlocal|not-in|or|(quasi)?quote|require|return|cut|raise|try|unpack-iterable|unpack-mapping|unquote|unquote-splice|when|while|with|yield|local-macros|in|is|py(s)?|pragma|nonlocal|(is-)?not)(?![\\\\\\\\.:\\\\\\\\w_\\\\\\\\-=!@\\\\\\\\$%^&?/<>*])\\\",\\\"name\\\":\\\"keyword.control.hy\\\"},{\\\"match\\\":\\\"(?<=\\\\\\\\(\\\\\\\\s*)\\\\\\\\.(?![\\\\\\\\.:\\\\\\\\w_\\\\\\\\-=!@\\\\\\\\$%^&?/<>*])\\\",\\\"name\\\":\\\"keyword.control.dot.hy\\\"}]},\\\"operators\\\":{\\\"patterns\\\":[{\\\"match\\\":\\\"(?<![\\\\\\\\.:\\\\\\\\w_\\\\\\\\-=!@\\\\\\\\$%^&?/<>*])(\\\\\\\\+=?|\\\\\\\\/\\\\\\\\/?=?|\\\\\\\\*\\\\\\\\*?=?|--?=?|[!<>]?=|@=?|%=?|<<?=?|>>?=?|&=?|\\\\\\\\|=?|\\\\\\\\^|~@|~=?|#\\\\\\\\*\\\\\\\\*?)(?![\\\\\\\\.:\\\\\\\\w_\\\\\\\\-=!@\\\\\\\\$%^&?/<>*])\\\",\\\"name\\\":\\\"keyword.control.hy\\\"}]},\\\"strings\\\":{\\\"begin\\\":\\\"(f?\\\\\\\"|}(?=[^\\\\n]*?[{\\\\\\\"]))\\\",\\\"end\\\":\\\"(\\\\\\\"|(?<=[\\\\\\\"}][^\\\\n]*?){)\\\",\\\"name\\\":\\\"string.quoted.double.hy\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.hy\\\"}]},\\\"symbol\\\":{\\\"match\\\":\\\"(?<![\\\\\\\\.:\\\\\\\\w_\\\\\\\\-=!@\\\\\\\\$%^&?/<>*#])[\\\\\\\\.a-zA-ZΑ-Ωα-ω_\\\\\\\\-=!@\\\\\\\\$%^<?/<>*#][\\\\\\\\.:\\\\\\\\w_\\\\\\\\-=!@\\\\\\\\$%^&?/<>*#]*\\\",\\\"name\\\":\\\"variable.other.hy\\\"}},\\\"scopeName\\\":\\\"source.hy\\\"}\"))\n\nexport default [\nlang\n]\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC;uCAEvB;IACf;CACC", "ignoreList": [0]}}, {"offset": {"line": 14, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}